<?xml version="1.0" encoding="UTF-8"?>
<PermissionSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <applicationVisibilities>
        <application>Recoupment</application>
        <visible>true</visible>
    </applicationVisibilities>
    <customPermissions>
        <enabled>true</enabled>
        <name>Recoupment</name>
    </customPermissions>
    <description>Enables the Permissions specific to the Recoupment App functionality. It does not contain all Permissions a User requires, just those specific to &apos;net&apos; Recoupment functionality.</description>
    <fieldPermissions>
        <editable>true</editable>
        <field>Bond__c.Recoupment_Status_Date__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Bond__c.Recoupment_Status__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Bond__c.Subsequent_Bond__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>false</editable>
        <field>Repayment__c.Partial_Bond_Refund_Reason__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <fieldPermissions>
        <editable>true</editable>
        <field>Repayment__c.Recoupment_Repayment__c</field>
        <readable>true</readable>
    </fieldPermissions>
    <hasActivationRequired>false</hasActivationRequired>
    <label>Recoupment</label>
    <objectPermissions>
        <allowCreate>false</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>Bond__c</object>
        <viewAllFields>false</viewAllFields>
        <viewAllRecords>false</viewAllRecords>
    </objectPermissions>
    <objectPermissions>
        <allowCreate>false</allowCreate>
        <allowDelete>false</allowDelete>
        <allowEdit>true</allowEdit>
        <allowRead>true</allowRead>
        <modifyAllRecords>false</modifyAllRecords>
        <object>Repayment__c</object>
        <viewAllFields>false</viewAllFields>
        <viewAllRecords>false</viewAllRecords>
    </objectPermissions>
    <recordTypeVisibilities>
        <recordType>Bond__c.RTBA_Bond</recordType>
        <visible>true</visible>
    </recordTypeVisibilities>
    <userPermissions>
        <enabled>true</enabled>
        <name>CreateAuditFields</name>
    </userPermissions>
</PermissionSet>
