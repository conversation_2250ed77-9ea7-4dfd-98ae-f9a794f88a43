public with sharing class Constants {
	public static final String ACCOUNT_TYPE_RENTER = 'Renter';
	public static final String ACCOUNT_TYPE_RENTAL_PROVIDER = 'Rental Provider';
	public static final String ACCOUNT_TYPE_STAT_AUTH = 'Statutory Authority';
	public static final String ACCOUNT_SUBTYPE_LANDLORD_COMPANY = 'Landlord - Company';
	public static final String ACCOUNT_SUBTYPE_LANDLORD_INDIVIDUAL = 'Landlord - Individual';
	public static final String ACCOUNT_SUBTYPE_LICENSED_ESTATE_AGENT = 'Licensed Estate Agent';

	public static final String RP_ORG_STATUS_ACTIVE = 'Active';
	public static final String RP_ORG_STATUS_MONITOR = 'Monitor';
	public static final String RP_ORG_STATUS_SUSPENDED = 'Suspended';
	public static final String RP_ORG_STATUS_DEREGISTERED = 'Deregistered';

	public static final String DPID_FLAG_ADDITIONAL_ADDRESS = 'A';
	public static final String DPID_FLAG_MANUAL_ADDRESS = 'M';
	public static final String DPID_FLAG_ADDRESS_NO_CHANGE = 'N';

	public static final String DPID_SOURCE_PMR = 'PMR';
	public static final String DPID_SOURCE_BL = 'BL';
	public static final String DPID_SOURCE_BC = 'BC';

	public static final String TRANSACTION_STATUS_CANCELLED = 'Cancelled';
	public static final String TRANSACTION_STATUS_FAILED = 'Failed';
	public static final String TRANSACTION_STATUS_FINALISED = 'Finalised';
	public static final String TRANSACTION_STATUS_PENDING_PAYMENT = 'Pending Payment';
	public static final String TRANSACTION_STATUS_PENDING_WITH_RENTERS = 'Pending with Renters';
	public static final String TRANSACTION_STATUS_PENDING_WITH_RP = 'Pending with RP';
	public static final String TRANSACTION_STATUS_PENDING_WITH_RTBA = 'Pending with RTBA';
	public static final String TRANSACTION_STATUS_TIMEDOUT = 'Timed Out';
	public static final String TRANSACTION_STATUS_PENDING_WITH_ANOTHER_RENTAL_PROVIDER = 'Pending with Another Rental Provider';
	public static final String TRANSACTION_STATUS_PENDING_RESPONSE = 'Pending Response';
	public static final String TRANSACTION_STATUS_TO_BE_PROCESSED = 'To Be Processed';
	public static final String TRANSACTION_STATUS_RTBA_REJECTED = 'RTBA Rejected';
	public static final String TRANSACTION_STATUS_RTBA_CANCELLED = 'RTBA Cancelled';
	public static final String TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE = 'Pending with Transferee';
	public static final Set<String> TRANSACTION_STATUSES_PENDING = new Set<String>{
		TRANSACTION_STATUS_PENDING_PAYMENT,
		TRANSACTION_STATUS_PENDING_WITH_RENTERS,
		TRANSACTION_STATUS_PENDING_WITH_RP,
		TRANSACTION_STATUS_PENDING_WITH_RTBA,
		TRANSACTION_STATUS_PENDING_WITH_ANOTHER_RENTAL_PROVIDER,
		TRANSACTION_STATUS_PENDING_RESPONSE,
		TRANSACTION_STATUS_TO_BE_PROCESSED,
		TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
	};

	public static final String TRANSACTION_PAYMENT_METHOD_DIRECT_DEBIT = 'Direct Debit';

	public static final String TRANSACTION_SUB_STATUS_INITIAL = 'Initial';
	public static final String TRANSACTION_SUB_STATUS_RENTER_CHANGE = 'Renter Requested Change';
	public static final String TRANSACTION_SUB_STATUS_RESUBMISSION = 'Resubmission';
	public static final String TRANSACTION_SUB_STATUS_RESUBMISSION_NO_CHANGE = 'Resubmission - No Change';

	public static final String TRANSACTION_SUB_STATUS_TC_REJECTED = 'TC Rejected';
	public static final String TRANSACTION_SUB_STATUS_EMAIL_BOUNCED = 'Email Bounced';
	public static final String TRANSACTION_SUB_STATUS_RENTAL_PROVIDER_CANCELLED = 'Rental Provider Cancelled';
	public static final String TRANSACTION_SUB_STATUS_RENTER_TIMED_OUT = 'Renter Timed Out';
	public static final String TRANSACTION_SUB_STATUS_RENTER_PAYMENT_TIMED_OUT = 'Renter Payment Timed Out';
	public static final String TRANSACTION_SUB_STATUS_UNPAID = 'Unpaid';
	public static final String TRANSACTION_SUB_STATUS_RENTAL_PROVIDER_TIMED_OUT = 'Rental Provider Timed Out';

	public static final String TRANSACTION_TYPE_BOND_LODGEMENT = 'Bond Lodgement';
	public static final String TRANSACTION_TYPE_MODIFICATION_BOND_LODGEMENT = 'Modification Bond Lodgement';
	public static final String TRANSACTION_TYPE_CLAIMS = 'Claims';
	public static final String TRANSACTION_TYPE_RENTAL_PROVIDER_TRANSFER = 'Rental Provider Transfer';
	public static final String TRANSACTION_TYPE_RENTER_TRANSFER = 'Renter Transfer';
	public static final String TRANSACTION_TYPE_BOND_MERGE = 'Bond Merge';
	public static final String TRANSACTION_TYPE_RETAINED_REPAYMENT = 'Retained Repayment Request';
	public static final String TRANSACTION_TYPE_BOND_AMENDMENT = 'Bond Amendment';

	public static final String TRANSACTION_BOND_PAID_BY_HOMES_VICTORIA = 'Homes Victoria';
	public static final String TRANSACTION_ORIGIN_WEBSITE = 'RTBA Website';
	public static final String TRANSACTION_ORIGIN_MAIL = 'Mail';

	public static final String TRANSACTION_TRANSFER_OPTION_TRANSFER_IN_ONE_BOND = 'Transfer in - one bond';
	public static final String TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_ONE_BOND = 'Transfer out - one bond';
	public static final String TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_TWO_TO_TWENTY_BONDS = 'Transfer out - 2-20 bonds';
	public static final String TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_MORE_THAN_TWENTY_BONDS = 'Transfer out - more than 20 bonds';
	public static final String TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_ENTIRE_RENT_ROLL = 'Transfer out - entire rent roll';
	public static final String TRANSACTION_TRANSFER_OPTION_RENTER_REMOVAL = 'Renter removal';
	public static final String TRANSACTION_TRANSFER_OPTION_RENTER_TRANSFER = 'Renter transfer';
	public static final String TRANSACTION_TRANSFER_OPTION_BOND_MERGE = 'Bond merge';

	public static final Set<String> TRANSACTION_TRANSFER_OPTION_TRANSFER_WITH_CONSENT = new Set<String>{
		TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_ONE_BOND,
		TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_TWO_TO_TWENTY_BONDS,
		TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_MORE_THAN_TWENTY_BONDS,
		TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_ENTIRE_RENT_ROLL
	};

	public static final String TRANSACTION_PARTY_STATUS_AGREED = 'Agreed';
	public static final String TRANSACTION_PARTY_STATUS_CANCELLED = 'Cancelled';
	public static final String TRANSACTION_PARTY_STATUS_CHANGE_REQUESTED = 'Change Requested';
	public static final String TRANSACTION_PARTY_STATUS_EMAIL_BOUNCED = 'Email Bounced';
	public static final String TRANSACTION_PARTY_STATUS_INPUT_AWAITED = 'Input Awaited';
	public static final String TRANSACTION_PARTY_STATUS_PAID = 'Paid';
	public static final String TRANSACTION_PARTY_STATUS_PENDING_PAYMENT = 'Pending Payment';
	public static final String TRANSACTION_PARTY_STATUS_RP_CANCELLED = 'RP Cancelled';
	public static final String TRANSACTION_PARTY_STATUS_RENTER_CANCELLED = 'Renter Cancelled';
	public static final String TRANSACTION_PARTY_STATUS_RTBA_CANCELLED = 'RTBA Cancelled';
	public static final String TRANSACTION_PARTY_STATUS_DISPUTED = 'Disputed';
	public static final String TRANSACTION_PARTY_STATUS_POST_RETURNED = 'Post Returned';
	public static final String TRANSACTION_PARTY_STATUS_TC_REJECTED = 'TC Rejected';
	public static final String TRANSACTION_PARTY_STATUS_TIMED_OUT = 'Timed Out';
	public static final String TRANSACTION_PARTY_STATUS_UNPAID = 'Unpaid';
	public static final String TRANSACTION_PARTY_STATUS_REMOVED = 'Removed';
	public static final String TRANSACTION_PARTY_STATUS_RP_REJECTED = 'RP Rejected';

	public static final String TRANSACTION_PARTY_ROLE_RENTER = 'Renter';
	public static final String TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER = 'Rental Provider';
	public static final String TRANSACTION_PARTY_ROLE_THIRD_PARTY = 'Third Party';

	public static final String TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL = 'Individual';
	public static final String TRANSACTION_PARTY_RENTER_TYPE_COMPANY = 'Company';

	public static final String TRANSACTION_PARTY_REVIEW_URL_STATUS_ACTIVE = 'Active';
	public static final String TRANSACTION_PARTY_REVIEW_URL_STATUS_INACTIVE = 'Inactive';

	public static final String TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR = 'Transferor';
	public static final String TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE = 'Transferee';

	public static final String TRANSACTION_BOND_PAID_BY_RENTER = 'Renter';
	public static final String TRANSACTION_BOND_PAID_BY_RENTAL_PROVIDER = 'Rental Provider';

	public static final String TRANSACTION_PARTY_RENTER_POSITION_STAYING = 'Staying';
	public static final String TRANSACTION_PARTY_RENTER_POSITION_LEAVING = 'Leaving';
	public static final String TRANSACTION_PARTY_RENTER_POSITION_INCOMING = 'Incoming';

	public static final String TRANSACTION_PARTY_LODGMENT_NOTIFICATION = 'Yes';

	/*Transaction Response */
	public static final String TRANSACTION_RESPONSE_STATUS_RTBA_REJECTED = 'RTBA Rejected';
	public static final String TRANSACTION_RESPONSE_STATUS_NEW = 'New';

	public static final String TRANSACTION_ROLE_INITIATING_CLAIM_RENTER = 'Renter';
	public static final String TRANSACTION_ROLE_INITIATING_CLAIM_RENTAL_PROVIDER = 'Rental Provider	';

	public static final String BOND_STATUS_ACTIVE = 'Active';
	public static final String BOND_STATUS_SUSPENDED = 'Suspended';
	public static final String BOND_STATUS_PARTIALLY_PAID = 'Partially Paid';
	public static final String BOND_STATUS_CANCELLED = 'Cancelled';
	public static final String BOND_STATUS_CLOSED = 'Closed';
	public static final String BOND_ORIGIN_WEBSITE = 'RTBA Website';
	public static final List<String> BOND_STATUSES_PREVENTING_DEACTIVATION = new List<String>{
		BOND_STATUS_ACTIVE,
		BOND_STATUS_SUSPENDED,
		BOND_STATUS_PARTIALLY_PAID
	};

	public static final String BOND_PARTY_ROLE_RENTER = 'Renter';
	public static final String BOND_PARTY_ROLE_RENTAL_PROVIDER = 'Rental Provider';
	public static final String BOND_PARTY_RENTER_TYPE_INDIVIDUAL = 'Individual';
	public static final String BOND_PARTY_RENTER_TYPE_COMPANY = 'Company';
	public static final String BOND_PARTY_ROLE_THIRD_PARTY = 'Third Party';

	public static final String BOND_PARTY_STATUS_ACTIVE = 'Active';
	public static final String BOND_PARTY_STATUS_INACTIVE = 'Inactive';

	/* Repayment Basis */
	public static final String TRANSACTION_REPAYMENT_BASIS_VCAT = 'VCAT Order';
	public static final String TRANSACTION_REPAYMENT_BASIS_OTHER_COURT_ORDER = 'Other Court Order';
	public static final String TRANSACTION_REPAYMENT_RENTER_AGREEMENT = 'Renter Agreement';
	public static final String TRANSACTION_REPAYMENT_WITHOUT_CONSENT = 'Without Consent';

	/* Refund Full Claim Amount To Party */
	public static final String TRANSACTION_REFUND_FULL_CLAIM_AMOUNT_TO_RENTER = 'Renter';
	public static final String TRANSACTION_REFUND_FULL_CLAIM_AMOUNT_TO_RENTAL_PROVIDER = 'Rental Provider';
	public static final String TRANSACTION_REFUND_FULL_CLAIM_AMOUNT_TO_HOMES_VICTORIA = 'Homes Victoria';

	/* Role Initiating Claim */
	public static final String TRANSACTION_ROLE_INITIATING_CLAIM_BY_RENTER = 'Renter';
	public static final String TRANSACTION_ROLE_INITIATING_CLAIM_BY_RENTAL_PROVIDER = 'Rental Provider';

	public static final String BOND_FUNDING_SOURCE_HOMES_VICTORIA = 'Homes Victoria';
	public static final String BOND_FUNDING_SOURCE_PRIVATE = 'Private';

	public static final String BOND_PAID_BY_HOMES_VICTORIA = 'HOMES VICTORIA';

	public static final String SETTING_RTBA_ONLINE_SITE_NAME = 'RTBA_Online_Site_Name';
	public static final String SETTING_RENTER_REVIEW_TERMS_OF_USE = 'Renter_Review_Terms_of_Use';
	public static final String SETTINGS_GENERAL_TERMS_OF_USE = 'Terms_of_Use';

	// PDF Butler Doc Config Settings
	public static final String PDF_DC_BOND_SUMMARY = 'Bond Summary';
	public static final String PDF_DC_POSTAL_NOTIFICATION = 'Postal Notification';
	public static final String PDF_DC_UNREG_RP_POSTAL_NOTIFICATION = 'Unregistered RP Postal Notification';
	public static final String PDF_DC_PDF_BUTLER_BC_RENTER_NOT_CONTACTABLE = 'BC - Finalised Claim Renters not contactable';
	public static final String PDF_DC_BOUNCED_EMAIL_UNREG_RP_ZERO_REPAYMENT = 'Bounced Email - Unreg. RP Zero Repayment';
	public static final String PDF_DC_BOUNCED_EMAIL_UNREG_RP_REPAYMENT_WITH_BANK_DETAILS = 'Bounced Email - Unreg. RP repayment with bank details';
	public static final String PDF_DC_BOUNCED_EMAIL_UNREG_RP_REPAYMENT_WITHOUT_BANK_DETAILS = 'Bounced Email - Unreg. RP repayment without bank details';
	public static final String PDF_DC_UNREGISTERED_RP_NOTIFICATION_VALID_VCAT_ZERO_DOLLAR = 'Unregistered RP Notification Valid VCAT Zero Dollar';
	public static final String PDF_DC_UNREGISTERED_RP_NOTIFICATION_VALID_VCAT_MORE_THAN_ZERO_DOLLAR = 'Unregistered RP Notification Valid VCAT More Than Zero Dollar';
	public static final String PDF_DC_POSTAL_NOTIFICATION_RIC = 'Postal Notification RIC';
	public static final String PDF_DC_BOUNCED_EMAIL_UNREG_RP_REPAYMENT_MORE_THAN_ZERO = 'Bounced Email - Unreg. RP Repayment more than zero';
	public static final String PDF_DC_BOUNCED_EMAIL_UNREG_RP_RIC_RESPONSE_PERIOD_EXPIRED = 'Bounced Email - Unreg RP - RIC Response Period Expired';
	public static final String PDF_DC_BOUNCED_EMAIL_OTHER_RENTERS_RIC_VCAT_ACCEPTED_AMOUNT = 'Bounced Email - Other Renters - RIC VCAT Accepted Amount';
	public static final String PDF_DC_BOUNCED_EMAIL_RIC_HV_UNREG_RP_NOTIFICATION = 'Bounced Email - Unreg RP - RIC HV Bond Notification';
	public static final String PDF_DC_BOUNCED_EMAIL_RIC_HV_RENTER_NOTIFICATION = 'Bounced Email - RIC - HV Claim - Renter notification';
	public static final String PDF_DC_RENTER_MISSING_EMAIL_VALID_VCAT_POSTAL_NOTIFICATION = 'Renter Missing Email - Valid VCAT Postal Notification';
	public static final String PDF_DC_RENTER_INITIATED_WITHOUT_CONSENT_POSTAL_NOTIFICATION = 'Renter Initiated Without Consent Postal Notification';
	public static final String PDF_DC_RIC_WITHOUT_CONSENT_UNREGISTERED_RP_POSTAL_NOTIFICATION = 'RIC Without Consent Unregistered RP Postal Notification';
	public static final String PDF_DC_RIC_WITHOUT_CONSENT_RENTER_POSTAL_NOTIFICATION = 'RIC - Without Consent - Renter Email Notification Bounced';
	public static final String PDF_DC_RP_TRANSFER_BONDS_LIST_EXCEL = 'RP Transfer Bonds List - Excel';
	public static final String PDF_DC_RP_TRANSFER_BOND_LIST_PDF = 'RP Transfer Bond List - Pdf';
	public static final String PDF_DC_BOND_SEARCH_DOWNLOAD_EXCEL = 'Bond search - download excel';
	public static final String PDF_DC_BOND_SEARCH_DOWNLOAD_PDF = 'Bond search - download pdf';
	public static final String PDF_DC_EXTERNAL_USER_BOND_SEARCH_DOWNLOAD_PDF = 'External user Bond search - download pdf';
	public static final String PDF_DC_EXTERNAL_USER_BOND_SEARCH_DOWNLOAD_EXCEL = 'External user Bond search - download excel';
	public static final String PDF_DC_TRANSACTION_SEARCH_DOWNLOAD_EXCEL = 'Transaction search - download excel';
	public static final String PDF_DC_TRANSACTION_SEARCH_DOWNLOAD_PDF = 'Transaction search - download pdf';
	public static final String PDF_DC_TRANSACTION_RESPONSE_CANCELLED_RIC_NO_CONSENT = 'Transaction Response Cancelled - RIC No consent';
	public static final String PDF_DC_BOND_SUMMARY_ADVICE = 'Bond Summary Advice';
	public static final String PDF_DC_ALL_PARTIES_AGREED_PRIVATE = 'RIC - All parties agreed - Unreg RP postal notification - private';
	public static final String PDF_DC_ALL_PARTIES_AGREED_HOMES_VICTORIA = 'RIC - All parties agreed - unreg RP postal notification - homes victoria';
	public static final String PDF_DC_ALL_PARTIES_AGREED_BANK_DETAILS_NOT_PROVIDED = 'RIC - All parties agreed - bank details not given Unreg RP postal notification';
	public static final String PDF_DC_ALL_PARTIES_AGREED_RENTER_NOTIFICATION = 'RIC - All parties agreed - Renter postal notification';
	public static final String PDF_DC_RIC_HOMES_VICTORIA_UNREGISTERED_RP_NOTIFICATION = 'RIC - HomesVictoria Unreg RP notification';
	public static final String PDF_DC_RIC_RP_EMAIL_BOUNCED_NOTIIFICATION = 'RIC - Without Consent - RP Email Notification Bounced';
	public static final String PDF_DC_REPAYMENT_EXCEPTION_CLAIM_RENTER_NOTIFICATION = 'Repayment Exception - Claims Transaction - Renter Email Notification';
	public static final String PDF_DC_REPAYMENT_EXCEPTION_CLAIM_RP_NOTIFICATION = 'Repayment Exception - Claims Transaction - RP Email Notification';
	public static final String PDF_DC_REPAYMENT_EXCEPTION_RR_RENTER_NOTIFICATION = 'Repayment Exception - RR Transaction - Renter Email Notification';
	public static final String PDF_DC_REPAYMENT_EXCEPTION_RR_RP_NOTIFICATION = 'Repayment Exception - RR Transaction - RP Email Notification';
	public static final String PDF_DC_MANAGE_TRANSACTION_PDF = 'Manage transaction - Pdf';
	public static final String PDF_DC_MANAGE_TRANSACTION_RP_TRANSFER_PDF = 'Manage transaction - Transfer - Pdf';
	public static final String PDF_DC_MANAGE_TRANSACTION_PENDING_WITH_RP_RENTER_EXCEL = 'Manage transaction - Pending with RP/Renter - Excel';
	public static final String PDF_DC_MANAGE_TRANSACTION_SUCCESSFUL_EXCEL = 'Manage transaction - Successful tx - Excel';
	public static final String PDF_DC_MANAGE_TRANSACTION_UNSUCCESSFUL_EXCEL = 'Manage transaction - Unsuccessful tx - Excel';
	public static final String PDF_DC_MANAGE_TRANSACTION_RP_TRANSFER_EXCEL = 'Manage transaction - RP Transfer - Excel';
	public static final String PDF_DC_MANAGE_TRANSACTION_PENDING_WITH_RTBA_EXCEL = 'Manage transaction - Pending with RTBA - Excel';
	public static final String PDF_DC_MANAGE_TRANSACTION_RENTER_INITIATED_CLAIM = 'Manage transaction - Renter initiated claims - Excel';
	public static final String PDF_DC_RENTAL_PROVIDER_BOND_LIST_PDF = 'Rental Provider Bond List - Pdf';
	public static final String PDF_DC_RENTAL_PROVIDER_BOND_LIST_EXCEL = 'Rental Provider Bond List - Excel';
	public static final String PDF_DC_RENTAL_PROVIDER_WEEKLY_STATEMENT_PDF = 'Weekly RP Statement - Pdf';

	// PDF Butler Data Source Settings
	public static final String PDF_DS_UNREG_POSTAL_NOTIFICATION = 'Unregistered RP Postal Notification KeyValue';
	public static final String PDF_DS_RENTERS_LIST_DATA = 'Renters List Data';
	public static final String PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS = 'Rented Property Full Street Address';
	public static final String PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS = 'Rental Provider Full Street Address';
	public static final String PDF_DS_TRANSACTION_SEARCH_TABLE = 'Transaction search - table';
	public static final String PDF_DS_BOND_SEARCH_LIST = 'Bond search - List';
	public static final String PDF_DS_BOND_SEARCH_SINGLE = 'Bond search - Single';
	public static final String PDF_DS_RP_TRANSFER_LIST_OF_BONDS = 'RP Transfer - List of Bonds';
	public static final String PDF_DS_RP_TRANSFER_SINGLE_BOND_DETAILS = 'RP Transfer - Single bond details';
	public static final String PDF_DS_BOND_SUMMARY_ADVICE_HEADER = 'Bond summary advice - Header';
	public static final String PDF_DS_BOND_SUMMARY_ADVICE_ITEMS = 'Bond summary advice - Items';
	public static final String PDF_DS_MANAGE_TX_SELECTED_LIST_VIEW = 'Manage transaction selected list view';
	public static final String PDF_DS_RP_WEEKLY_STATEMENT_SINGLE = 'Weekly RP Statement - Single';
	public static final String PDF_DS_RP_WEEKLY_STATEMENT_LIST = 'Weekly RP Statement - Table';

	public static final String SETTING_LOGOUT_DELAY_MILLISECONDS = 'Logout_Delay_Milliseconds';
	public static final String SETTING_LANDLORD_REGISTRATION_GUIDANCE = 'Landlord_Registration_Guidance';
	public static final String SETTING_LEA_REGISTRATION_GUIDANCE = 'LEA_Registration_Guidance';
	public static final String SETTINGS_UNREGISTERED_RETAINED_REPAYMENT_GUIDANCE = 'Unregistered_Retained_Repayment_Guidance';
	public static final String SETTING_BPAY_BILLER_CODE = 'BPay_Biller_Code';

	public static final String TRANSACTION_REVIEW_REVISION_INITIAL_SUBMISSION = 'Initial Submission';
	public static final String TRANSACTION_REVIEW_REVISION_AMENDED = 'Amended';
	public static final String TRANSACTION_REVIEW_REVISION_AGREED = 'Agree';

	public static final String PROFILE_RTBA_CUSTOMER_COMMUNITY_LOGIN_USER = 'RTBA Community Login User';
	public static final String PROFILE_RTBA_INTERNAL_USER_BASELINE = 'RTBA Internal User - Baseline';
	public static final String PROFILE_SYSTEM_ADMINISTRATOR = 'System Administrator';
	public static final String PROFILE_RTBA_SYSTEM_ADMINISTRATOR = 'RTBA System Administrator';
	public static final String PROFILE_CUSTOMER_COMMUNITY_PLUS_USER = 'RTBA Community Plus User';

	public static final String PERMISSION_SET_GROUP_RENTAL_PROVIDER_L1 = 'Rental_Provider_L1';
	public static final String PERMISSION_SET_GROUP_RENTAL_PROVIDER_L2 = 'Rental_Provider_L2';
	public static final String PERMISSION_SET_GROUP_RENTAL_PROVIDER_L3 = 'Rental_Provider_L3';
	public static final String PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4 = 'Rental_Provider_L4';
	public static final String PERMISSION_SET_GROUP_RTBA_SYSTEM_ADMINISTRATOR = 'RTBA_System_Administrator';

	public static final String RENTAL_PROVIDER_PERMISSION_L1 = 'L1';
	public static final String RENTAL_PROVIDER_PERMISSION_L2 = 'L2';
	public static final String RENTAL_PROVIDER_PERMISSION_L3 = 'L3';
	public static final String RENTAL_PROVIDER_PERMISSION_L4 = 'L4';
	public static final String INTERNAL_USER_PERMISSION_EO = 'RTBA_Internal_Staff_EO';
	public static final String INTERNAL_USER_PERMISSION_SEO = 'RTBA_Internal_Staff_SEO';
	public static final String INTERNAL_USER_PERMISSION_SBA = 'RTBA_Internal_Staff_Business_Admin';
	public static final String INTERNAL_USER_RECONCILIATION_ADMIN = 'Reconciliation_Admin';
	public static final String USERNAME_POSTFIX = '.rtba';
	public static final String VOUCHER_STATUS_PRESENTED = 'Presented';
	public static final String VOUCHER_STATUS_NEW = 'New';
	public static final String EXTERNAL_USER_LE1 = 'LE1';
	public static final String EXTERNAL_USER_LE2 = 'LE2';

	public static final List<String> RENTAL_PROVIDER_PERMISSION_LIST = new List<String>{
		RENTAL_PROVIDER_PERMISSION_L1,
		RENTAL_PROVIDER_PERMISSION_L2,
		RENTAL_PROVIDER_PERMISSION_L3,
		RENTAL_PROVIDER_PERMISSION_L4
	};

	public static final Set<String> EXTERNAL_USER_PERMISSION_LIST = new Set<String>{
		EXTERNAL_USER_LE1,
		EXTERNAL_USER_LE2
	};

	public static final Map<String, String> RENTAL_PROVIDER_PERMISSION_PERMISSIONSETGROUP_MAP = new Map<String, String>{
		RENTAL_PROVIDER_PERMISSION_L1 => PERMISSION_SET_GROUP_RENTAL_PROVIDER_L1,
		RENTAL_PROVIDER_PERMISSION_L2 => PERMISSION_SET_GROUP_RENTAL_PROVIDER_L2,
		RENTAL_PROVIDER_PERMISSION_L3 => PERMISSION_SET_GROUP_RENTAL_PROVIDER_L3,
		RENTAL_PROVIDER_PERMISSION_L4 => PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
	};

	public static final String CUSTOM_PERMISSION_REVIEW_CLAIM_RENTER_CHANGE_REQUEST = 'Review_Claim_Renter_Change_Request';
	public static final String CUSTOM_PERMISSION_UPDATE_CLAIM_TRANSACTION = 'Update_Claim_Transaction';
	public static final String CUSTOM_PERMISSION_MANAGE_RP_SUB_USERS = 'Manage_RP_sub_users';
	public static final String CUSTOM_PERMISSION_MANAGE_RP_DETAILS = 'Manage_RP_details';
	public static final String CUSTOM_PERMISSION_MANAGE_RP_BANKING_DETAILS = 'Manage_RP_banking_details';
	public static final String CUSTOM_PERMISSION_CANCEL_LODGEMENT_TRANSACTION = 'Cancel_Lodgement_Transaction';
	public static final String CUSTOM_PERMISSION_RESUBMIT_LODGEMENT_TRANSACTION = 'Resubmit_Lodgement_Transaction';
	public static final String CUSTOM_PERMISSION_REVIEW_LODGEMENT_RENTER_CHANGE = 'Review_Lodgement_Renter_Change_Request';
	public static final String CUSTOM_PERMISSION_UPDATE_LODGEMENT_TRANSACTION = 'Update_Lodgement_Transaction';
	public static final String CUSTOM_PERMISSION_CANCEL_CLAIM_TRANSACTION = 'Cancel_Claim_Transaction';
	public static final String CUSTOM_PERMISSION_RENTAL_PROVIDER_TRANSFER = 'Rental_Provider_Transfer';
	public static final String CUSTOM_PERMISSION_CANCEL_TRANSFER_TRANSACTION = 'Cancel_Transfer_Transaction';
	public static final String CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION = 'Resubmit_Renter_Transfer_Transaction';
	public static final String CUSTOM_PERMISSION_UPDATE_RENTER_TRANSFER_TRANSACTION = 'Update_Renter_Transfer_Transaction';
	public static final String CUSTOM_PERMISSION_CANCEL_BOND_MERGE_TRANSACTION = 'Cancel_Bond_Merge_Transaction';
	public static final String CUSTOM_PERMISSION_MANUAL_RECONCILIATION_ROLLUP_AUTOMATION = 'Manual_Reconciliation_Rollup_Automation';
	public static final String CUSTOM_PERMISSION_CAN_EDIT_OWN_USER_DETAILS = 'Can_Edit_Own_User_Details';

	public static final String USER_STATUS_ACTIVE = 'Active';
	public static final String USER_STATUS_FROZEN = 'Frozen';
	public static final String USER_STATUS_DEACTIVATED = 'Deactivated';
	public static final String USER_GROUP_RENTAL_PROVIDER = 'Rental Provider';

	public static final String RECORD_TYPE_DEVELOPERNAME_ACCOUNT_PERSONACCOUNT = 'PersonAccount';
	public static final String RECORD_TYPE_DEVELOPERNAME_ACCOUNT_BUSINESSACCOUNT = 'Business_Account';

	public static final Id RECORD_TYPE_ID_ACCOUNT_PERSONACCOUNT = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
		.get(RECORD_TYPE_DEVELOPERNAME_ACCOUNT_PERSONACCOUNT)
		.getRecordTypeId();

	public static final Id RECORD_TYPE_ID_ACCOUNT_BUSINESSACCOUNT = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName()
		.get(RECORD_TYPE_DEVELOPERNAME_ACCOUNT_BUSINESSACCOUNT)
		.getRecordTypeId();

	public static final String RECORD_TYPE_NAME_TRANSACTION_BOND_LODGEMENT = 'Bond Lodgement';
	public static final String RECORD_TYPE_NAME_TRANSACTION_MODIFICATION_BOND_LODGEMENT = 'Modification Bond Lodgement';
	public static final String RECORD_TYPE_NAME_TRANSACTION_CLAIM = 'Claim';
	public static final String RECORD_TYPE_NAME_TRANSACTION_TRANSFER = 'Transfer';
	public static final String RECORD_TYPE_NAME_TRANSACTION_BOND_AMENDMENT = 'Bond Amendment';

	public static final Id RECORD_TYPE_ID_TRANSACTION_BOND_LODGEMENT = Schema.SObjectType.Transaction__c.getRecordTypeInfosByName()
		.get(RECORD_TYPE_NAME_TRANSACTION_BOND_LODGEMENT)
		.getRecordTypeId();
	public static final Id RECORD_TYPE_ID_TRANSACTION_MODIFICATION_BOND_LODGEMENT = Schema.SObjectType.Transaction__c.getRecordTypeInfosByName()
		.get(RECORD_TYPE_NAME_TRANSACTION_MODIFICATION_BOND_LODGEMENT)
		.getRecordTypeId();
	public static final Id RECORD_TYPE_ID_TRANSACTION_CLAIM = Schema.SObjectType.Transaction__c.getRecordTypeInfosByName()
		.get(RECORD_TYPE_NAME_TRANSACTION_CLAIM)
		.getRecordTypeId();
	public static final Id RECORD_TYPE_ID_TRANSACTION_TRANSFER = Schema.SObjectType.Transaction__c.getRecordTypeInfosByName()
		.get(RECORD_TYPE_NAME_TRANSACTION_TRANSFER)
		.getRecordTypeId();
	public static final Id RECORD_TYPE_ID_TRANSACTION_BOND_AMENDMENT = Schema.SObjectType.Transaction__c.getRecordTypeInfosByName()
		.get(RECORD_TYPE_NAME_TRANSACTION_BOND_AMENDMENT)
		.getRecordTypeId();

	public static final String MESSAGE_RENTER_TRANSACTION_CANCELLED_BY_RP = 'Renter_Transaction_Cancelled_By_RP';
	public static final String MESSAGE_RENTER_TRANSACTION_CANCELLED_BY_RENTER = 'Renter_Transaction_Cancelled_By_Renter';
	public static final String MESSAGE_RENTER_TRANSACTION_UNDER_REVIEW = 'Renter_Transaction_Under_Review';
	public static final String MESSAGE_RENTER_TRANSACTION_AMENDED_BY_RP = 'Renter_Transaction_Amended_By_RP';
	public static final String MESSAGE_RENTER_TRANSACTION_RESUBMITTED = 'Renter_Transaction_Resubmitted_By_RP';
	public static final String MESSAGE_RENTER_REMOVED_BY_RP = 'Renter_Has_Been_Removed_By_RP';
	public static final String MESSAGE_RENTER_TRANSACTION_EMAIL_BOUNCED = 'Renter_Transaction_Email_Bounced';
	public static final String MESSAGE_CONCURRENT_RENTER_TRANSACTION_EMAIL_BOUNCED = 'Concurrent_Renter_Tr_Email_Bounced';
	public static final String MESSAGE_CONCURRENT_RENTER_REQUESTED_CHANGE = 'Concurrent_Renter_Requesting_Change';
	public static final String MESSAGE_CONCURRENT_RENTAL_PROVIDER_CANCELLED = 'Concurrent_Rental_Provider_Cancelled';
	public static final String MESSAGE_CONCURRENT_RENTER_TC_REJECTED = 'Concurrent_Renter_TC_Rejected';

	public static final String RTBA_APP_CUSTOMER_TYPE_INDIVIDUAL = 'Individual';
	public static final String RTBA_APP_CUSTOMER_TYPE_COMPANY = 'Company';
	public static final String RTBA_APP_REGISTER_AS_LANDLORD = 'Register as a Landlord';
	public static final String RTBA_APP_REGISTER_AS_LICENSED_ESTATE_AGENT = 'Register as a Licensed Estate Agent';

	public static final String ADDRESS_SEARCH_ADDRESS_NOT_LISTED = 'Address not listed';
	public static final String ADDRESS_SEARCH_COUNTRY_AUSTRALIA = 'Australia';

	public static final String OIEC_UPDATE_CONTACT_DETAILS_USER_EMAIL_TEMPLATE = 'OIEC_Update_Contact_Details';
	public static final String OIEC_UPDATE_CONTACT_DETAILS_BY_INTERNAL_USER_EMAIL_TEMPLATE = 'OIEC_Update_Contact_Details_Internal';
	public static final String RP_SUBUSER_UPDATE_CONTACT_DETAILS_USER_EMAIL_TEMPLATE = 'RP_Sub_user_Contact_Details';
	public static final String RP_SUBUSER_UPDATE_CONTACT_DETAILS_USER_BY_INTERNAL_USER_EMAIL_TEMPLATE = 'RP_Sub_user_Contact_Details_by_Internal_User';
	public static final String EXPERIENCE_CLOUD_WELCOME_USER_EMAIL_TEMPLATE = 'Welcome_Email_to_New_Member';
	public static final String EXPERIENCE_CLOUD_OIEC_WELCOME_USER_EMAIL_TEMPLATE = 'OIEC_New_User_Welcome_Email';
	public static final String EXPERIENCE_CLOUD_OIEC_WELCOME_INTERNAL_USER_EMAIL_TEMPLATE = 'OIEC_New_Internal_User_Welcome_Email';
	public static final String RENTAL_PROVIDER_REGISTRATION_EMAIL_TEMPLATE = 'Rental_Provider_Registration_Notification_Final_Confirmation';
	public static final String EXPERIENCE_CLOUD_DEACTIVATION_USER_EMAIL_TEMPLATE = 'Deactivation_of_RTBA_Online_user_Final';
	public static final String FREEZING_A_RTBA_ONLINE_USER_EMAIL_TEMPLATE = 'Freezing_a_RTBA_online_user';
	public static final String INTERNAL_FREEZING_A_RTBA_ONLINE_USER_EMAIL_TEMPLATE = 'Internal_Freezing_a_RTBA_online_user';
	public static final String UNFREEZING_A_RTBA_ONLINE_USER_EMAIL_TEMPLATE = 'Unfreezing_a_RTBA_Online_User';
	public static final String INTERNAL_UNFREEZING_A_RTBA_ONLINE_USER_EMAIL_TEMPLATE = 'Internal_Unfreezing_a_RTBA_Online_User';
	public static final String UNFREEZING_A_RTBA_ONLINE_USER_ACCOUNT_EMAIL_TEMPLATE = 'Unfreezing_a_RTBA_Online_User_Account';
	public static final String INTERNAL_UNFREEZING_A_RTBA_ONLINE_USER_ACCOUNT_EMAIL_TEMPLATE = 'Internal_Unfreezing_a_RTBA_Online_User_Account';
	public static final String OIEC_UPDATE_ACCESS_LEVEL_USER_EMAIL_TEMPLATE = 'User_Management_Access_Level_Changes_for_OIEC_Primary_Contact';
	public static final String OIEC_UPDATE_ACCESS_LEVEL_USER_BY_INTERNAL_USER_EMAIL_TEMPLATE = 'User_Management_Access_Level_Changes_for_OIEC_Primary_Contact_by_Internal_User';
	public static final String RP_SUBUSER_UPDATE_ACCESS_LEVEL_USER_EMAIL_TEMPLATE = 'User_Management_Access_Level_Changes_for_RP_Sub_User';
	public static final String RP_SUBUSER_UPDATE_ACCESS_LEVEL_BY_INTERNAL_USER_EMAIL_TEMPLATE = 'User_Management_Access_Level_Changes_for_RP_Sub_User_by_Internal_User';
	public static final String BOND_SEARCH_REMINDER_EMAIL_TEMPLATE = 'Bond_Search_Reminder';
	public static final String INTERNAL_USER_DEACTIVATE_SUB_USER_EMAIL_TEMPLATE = 'Internal_user_Deactivate_sub_user';
	public static final String EXPERIENCE_CLOUD_WELCOME_SUB_USER_EMAIL_TEMPLATE = 'Welcome_Email_for_Internal_RP_sub_user';
	public static final String RP_ORG_STATUS_SUSPENDED_EMAIL_TEMPLATE = 'RTBA_Account_Suspension_Notification';
	public static final String RP_ORG_STATUS_REACTIVATION_EMAIL_TEMPLATE = 'RTBA_Account_Reactivation_Notification';
	public static final String RTBA_REGISTERED_NUMBER_EMAIL_TEMPLATE = 'Changes_to_RTBA_Registered_Name';
	public static final String RTBA_ACCOUNT_AGENCY_LICENCE_NUMBER_EMAIL_TEMPLATE = 'Change_to_Agency_Licence_Number';
	public static final String RTBA_LANDLORD_TWO_EMAIL_TEMPLATE = 'Changes_to_Secondary_Rental_Provider';
	public static final String RTBA_ABN_EMAIL_TEMPLATE = 'Changes_to_ABN_ACN';
	public static final String INTERNAL_USER_UPDATES_RENTAL_PROVIDER_CONTACT_DETAILS_TEMPLATE = 'Internal_User_Updates_Rental_Provider_Contact_Details';
	public static final String INTERNAL_USER_UPDATES_RENTAL_PROVIDER_BANKING_DETAILS_TEMPLATE = 'Internal_User_Updates_Rental_Provider_Banking_Details';
	public static final String INTERNAL_USER_UPDATES_RENTAL_PROVIDER_DELIVERY_INSTRUCTIONS_TEMPLATE = 'Internal_User_Updates_Rental_Provider_Delivery_Instructions';
	public static final String RP_ADMIN_UPDATES_RENTAL_PROVIDER_CONTACT_DETAILS_TEMPLATE = 'RP_Admin_Updates_Rental_Provider_Contact_Details';
	public static final String RP_ADMIN_UPDATES_RENTAL_PROVIDER_BANKING_DETAILS_TEMPLATE = 'RP_Admin_Updates_Rental_Provider_Banking_Details';
	public static final String RP_ADMIN_UPDATES_RENTAL_PROVIDER_DELIVERY_INSTRUCTIONS_TEMPLATE = 'RP_Admin_Updates_Rental_Provider_Delivery_Instructions';

	public static final String LOAN_NUMBER_REGEX = '^\\d{1,8}$';
	public static final String ADDRESS_REGEX = '^[A-Za-z0-9 \\-\\"&\'/,.]+$';
	public static final String SUBURB_REGEX = '^[A-Za-z \\s-"\']+$';
	public static final String POSTAL_CODE_REGEX = '^[0-9]{4}$';
	public static final String NAME_REGEX = '^[A-Za-z \\s-"()\'/]+$';
	public static final String DATE_REGEX = '[0-9]{2}/[0-9]{2}/[0-9]{4}';
	public static final String EMAIL_REGEX = '([a-zA-Z0-9_\\-\\.+]+)@((\\[a-z]{1,3}\\.[a-z]{1,3}\\.[a-z]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})';
	public static final String VOUCHER_NUMBER_REGEX = '^V[0-9]{9}$';
	public static final String MOBILE_REGEX = '^04[0-9]{2} ?[0-9]{3} ?[0-9]{3}$';
	public static final String NUMBER_REGEX = '^[0-9]{3,10}$';
	public static final String DECIMAL_REGEX = '^[-+]?\\d+\\.\\d{2}$';

	public static final String PAYMENT_SURCHARGE_INTERNATIONAL_BANK_TRANSFER = 'International bank transfer';
	public static final String PAYMENT_SURCHARGE_INTERNATIONAL_BANK_TRANSFER_DEVNAME = 'International_bank_transfer';

	/*VCAT Order pattern */
	public static final String VCAT_ORDER_NUMBER_REGEX = '^R[0-9]{4}/[0-9]{5}/[0-9]{2}$';

	public static final String TRANSACTION_FUNDING_SOURCE_PRIVATE = 'Private';
	public static final String TRANSACTION_FUNDING_SOURCE_HOMES_VICTORIA = 'Homes Victoria';
	public static final String PAYMENT_STATUS_PENDING = 'Pending';
	public static final String PAYMENT_STATUS_SUCCESSFUL = 'Successful';
	public static final String PAYMENT_STATUS_RETURNED = 'Returned';
	public static final String PAYMENT_PAYER_RENTER = 'RENTER';
	public static final String PAYMENT_PAYER_RENTAL_PROVIDER = 'RENTAL PROVIDER';
	public static final String PAYMENT_PAYER_HOMES_VICTORIA = 'HOMES VICTORIA';
	public static final String PAYMENT_PAYMENT_SOURCE_ONLINE = 'Online';
	public static final String PAYMENT_PAYMENT_METHOD_DIRECT_DEBIT = 'Direct Debit';
	public static final String PAYMENT_PAYMENT_METHOD_PAYPAL = 'PayPal';
	public static final String PAYMENT_PAYMENT_METHOD_CREDIT_CARD = 'Credit Card';
	public static final String PAYMENT_PAYMENT_METHOD_DEBIT_CARD = 'Debit Card';
	public static final String PAYMENT_PAYMENT_METHOD_BPAY = 'BPAY';

	public static final String PAYMENT_CONDITION_SUCCESS = 'success';
	public static final String PAYMENT_CONDITION_DOUBLE_PAYMENT = 'double_payment';

	public static final String REPAYMENT_STATUS_APPROVED = 'Approved';
	public static final String REPAYMENT_STATUS_RETAINED = 'Retained';
	public static final String REPAYMENT_STATUS_REISSUED = 'Reissued';
	public static final String REPAYMENT_STATUS_PRODUCED = 'Produced';
	public static final String REPAYMENT_STATUS_SUCCESSFUL = 'Successful';
	public static final String REPAYMENT_STATUS_REJECTED = 'Rejected';
	public static final String REPAYMENT_STATUS_CANCELLED = 'Cancelled';
	public static final String REPAYMENT_STATUS_UNCLAIMED_MONEY_RTBA = 'Unclaimed Money - RTBA';
	public static final String REPAYMENT_STATUS_UNCLAIMED_MONEY_SRO = 'Unclaimed Money - SRO';
	public static final String REPAYMENT_STATUS_RETURNED = 'Returned';
	public static final String REPAYMENT_STATUS_STOPPED = 'Stopped';
	public static final String REPAYMENT_STATUS_PRESENTED = 'Presented';

	public static final String REPAYMENT_STANDARDISED_STATUS_PENDING = 'Pending';
	public static final String REPAYMENT_STANDARDISED_STATUS_SUCCESSFUL = 'Successful';
	public static final String REPAYMENT_STANDARDISED_STATUS_FAILED = 'Failed';
	public static final String REPAYMENT_STANDARDISED_STATUS_REJECTED = 'Rejected';

	public static final String REPAYMENT_METHOD_CHEQUE = 'Cheque';
	public static final String REPAYMENT_METHOD_DIRECT_CREDIT = 'Direct Credit';
	public static final String REPAYMENT_METHOD_INTERNATIONAL_DIRECT_CREDIT = 'International Direct Credit';
	public static final String REPAYMENT_METHOD_AUSTRALIAN_DIRECT_CREDIT = 'Direct Debit'; //RDM: temporarily set to Direct Debit for testing purposes

	public static final String REPAYMENT_PAYEE_TYPE_HOMES_VICTORIA = 'Homes Victoria';
	public static final String REPAYMENT_PAYEE_TYPE_RENTAL_PROVIDER = 'Rental Provider';
	public static final String REPAYMENT_PAYEE_TYPE_RENTER = 'Renter';
	public static final String REPAYMENT_PAYEE_TYPE_RBIIA = 'RBIIA';

	public static final String REPAYMENT_RECIPIENT_TYPE_EMAIL = 'Email';
	public static final String REPAYMENT_RECIPIENT_TYPE_PHONE = 'Phone';

	public static final String REPAYMENT_TYPE_INTERNATIONAL_FEE = 'International Fee';

	public static final String BANK_ACCOUNT_STATEMENT_TRANSACTION_RECONCILIATION_STATUS_NOT_STARTED = 'Not Started';
	public static final String BANK_ACCOUNT_STATEMENT_TRANSACTION_RECONCILIATION_STATUS_IN_PROGRESS = 'In Progress';
	public static final String BANK_ACCOUNT_STATEMENT_TRANSACTION_RECONCILIATION_STATUS_COMPLETE = 'Complete';
	public static final String BANK_ACCOUNT_STATEMENT_TRANSACTION_RECONCILIATION_STATUS_ERROR = 'Error';

	public static final String BANK_ACCOUNT_STATEMENT_RECONCILIATION_STATUS_COMPLETE = 'Complete';
	public static final String BANK_ACCOUNT_STATEMENT_RECONCILIATION_STATUS_ERROR = 'Error';

	/* Content workspace */
	public static final String POSTAL_NOTIFICATION_CONTENT_WORKSPACE = 'POST';
	public static final String TEST_POSTAL_NOTIFICATION_CONTENT_WORKSPACE = 'POST_TEST';

	/* Westpac Integration */
	public static final String WESTPAC_ERROR_GENERAL = 'The service is currently unavailable.';
	public static final String WESTPAC_ERROR_BOND_CREATION = 'The payment was processed, but an error occurred in bond creation.';

	public static final String WESTPAC_RESPONSE_STATUS_CREATED = '201';
	public static final String WESTPAC_RESPONSE_STATUS_PRECONDITION_FAILED = '412';
	public static final String WESTPAC_RESPONSE_STATUS_CODE_UNAUTHORIZED = '401';
	public static final String WESTPAC_RESPONSE_STATUS_UNPROCESSABLE_ENTITY = '422';
	public static final String WESTPAC_RESPONSE_STATUS_APPROVED = 'Approved';
	public static final String WESTPAC_RESPONSE_STATUS_APPROVED_CONDITIONAL = 'Approved*';
	public static final String WESTPAC_RESPONSE_STATUS_PENDING = 'Pending';
	public static final String WESTPAC_RESPONSE_STATUS_DECLINED = 'Declined';
	public static final String WESTPAC_RESPONSE_STATUS_VOIDED = 'Voided';
	public static final String WESTPAC_RESPONSE_STATUS_SUSPENDED = 'Suspended';

	/* Pending Payment Settings */

	public static final Integer DEFAULT_ALLOW_CLAIM_DAYS_AFTER_LODGEMENT = 8;
	public static final Integer DEFAULT_PENDING_PAYMENT_EXPIRY_DAYS = 8;
	public static final Integer DEFAULT_PAYMENT_URL_EXPIRY_HOURS = 48;

	//Claims
	public static final String COURT_ORDER_VCAT_ORDER = 'VCAT order';
	public static final String COURT_ORDER_OTHER_COURT_ORDER = 'Other court order';
	/* Logger Tags */

	public static final String TAG_WESTPAC_API = 'Westpac API';
	public static final String TAG_HTTP_CALLOUT = 'HTTP Callout';
	public static final String TAG_BOND_PAYMENT = 'Bond Payment';
	public static final String TAG_INBOUND_EMAIL = 'Inbound Email Handler';
	public static final String TAG_BPAY_API = 'BPAY API';
	public static final String TAG_SERVICE_VICTORIA_API = 'Service Victoria API';
	public static final String TAG_IDV_RENTER_CALLBACK = 'IDV Renter Callback';

	public static final List<String> TAGS_API_WESTPAC = new List<String>{
		TAG_WESTPAC_API,
		TAG_HTTP_CALLOUT
	};

	/* Log Messages */

	public static final String LOG_PAGE_MAKE_BOND_PAYMENT = 'Exception on Make a Bond Payment page';
	public static final String LOG_INBOUND_EMAIL_HANDLER = 'Exception on SF inbound email handler';

	/* RTBA Groups/Queues */

	public static final String QUEUE_RTBA_REVIEW_NAME = 'RTBA Review Queue';
	public static final String QUEUE_SBA_REVIEW_NAME = 'SBA Review Queue';

	/* Case Management */

	public static final String CASE_DUPLICATE_PAYMENT_SUBJECT = 'Duplicate Payment';
	public static final String CASE_RP_INITIATED_HV_CLAIM_VCAT_ORDER = 'RP Initiated Claim with Uploaded VCAT Order';
	public static final String CASE_RP_INITIATED_HV_CLAIM_OTHER_COURT_ORDER = 'RP Initiated HV Funded Claim with Other Court Order';
	public static final String CASE_RP_INITIATED_PRIVATE_CLAIM_OTHER_COURT_ORDER = 'RP Initiated Privately Funded Claim with Other Court Order';
	public static final String CASE_RP_INITIATED_PRIVATE_CLAIM_VCAT_SUBJECT = 'RP Initiated Privately Funded Claim with VCAT Order';
	public static final String CASE_BOND_CLAIM_RP_OR_RENTER_WITH_CONSENT = 'Bond-Claim - Rental Provider / Renter with consent';
	public static final String CASE_SUBJECT_POSTAL_NOTIFICATION = 'Postal Notification';
	public static final String CASE_RENTER_INITIATED_PRIVATE_CLAIM_VCAT_SUBJECT = 'Renter Initiated Claim with Uploaded VCAT Order';
	public static final String CASE_RENTER_INITIATED_PRIVATE_CLAIM_OTHER_COURT_ORDER_SUBJECT = 'Renter Initiated Claim with Uploaded Other Court Order';
	public static final String CASE_RENTER_INITIATED_CLAIM_DISPUTE_WITH_COURT_REFERENCE_NUMBER_SUBJECT = 'RIC Dispute with Uploaded Court Document';
	public static final String CASE_RENTAL_PROVIDER_TRANSFER_WITH_UPLOADED_DOCUMENT_SUBJECT = 'Rental Provider Transfer with Uploaded Document';
	public static final String CASE_RENTER_REMOVAL_WITH_SUPPORTING_DOCUMENT_SUBJECT = 'Renter Removal with Supporting Document';
	public static final String CASE_BOND_MERGE_WITH_SUPPORTING_DOCUMENT_SUBJECT = 'Bond merge using uploaded documents';
	public static final String CASE_BOND_DETAILS_CORRECTION = 'Bond Details Correction with Uploaded Document';

	/* PayPal Integration */
	public static final String PAYPAL_RESPONSE_STATUS_CREATED = '201';
	public static final String PAYPAL_RESPONSE_STATUS_BAD_REQUEST = '400';
	public static final String PAYPAL_RESPONSE_STATUS_INTERNAL_SERVER_ERROR = '500';

	public static final String PAYPAL_ERROR_GENERAL = 'The service is currently unavailable.';

	/* Rental Provider Transfer */
	public static final Integer DEFAULT_RP_TRANSFER_PENDING_WITH_TRANSFEREE_EXPIRY_DAYS = 5;

	/**Email Titles used in MC */
	public static final String EMAIL_TITLE_BC_BOND_FINALISED_BY_VCAT_ORDER_RENTER_EMAIL = 'BC - Bond Finalised by VCAT Order - Renter Email';
	public static final String EMAIL_TITLE_BC_VCAT_RETAINED_BOND_PAYMENT_RENTER_EMAIL = 'BC - VCAT Retained Bond Payment - Renter Email';
	public static final String EMAIL_TITLE_RIC_RETAINED_REPAYMENT_UNREG_RP = 'RIC - Retained repayment - Unreg. RP';
	public static final String EMAIL_TITLE_RIC_FINALISED_UNREG_RP = 'RIC - Finalised - Unreg. RP';
	public static final String EMAIL_TITLE_OTHER_RENTER_VALIDATED_VCAT_ORDER_ACCEPTED_REPAYMENT_AMOUNTS = 'RIC-Private-Validated VCAT Order-Accepted Other Renter Email-8180-AC1';
	public static final String EMAIL_TITLE_BC_FINALISED_CLAIM_RENTERS_NOT_CONTACTABLE = 'BC - Finalised Claim Renters not contactable - Renter Email';
	public static final String EMAIL_TITLE_RIC_RESPONSE_PERIOD_EXPIRED_UNREG_RP = 'RIC-Without Consent-Response Period Expired-Txn Cancelled-UnRegRP Email-9409';
	public static final String EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_RENTER = 'RIC-Without Consent-All parties status Agreed-Renter Notification- 8436-AC2';
	public static final String EMAIL_TITLE_RIC_WITHOUT_CONSENT_RENTER_NOTIFICATION = 'Renter Initiated - Without Consent Private Bond -Renter Email Noti-4666';
	public static final String EMAIL_TITLE_RIC_WITHOUT_CONSENT_UNREG_RP_BOUNCED_EMAIL_NOTIFICATION = 'RIC- Without Consent-Private-UnregRP-EmailNotification-6939';
	public static final String EMAIL_TITLE_RIC_WITHOUT_CONSENT_RP_BOUNCED_EMAIL_NOTIFICATION = 'RIC-WithoutConsent-Private Bond-RegiRPEmailNotification-4665';
	public static final String EMAIL_TITLE_RIC_HV_BOND_RP_NOTIFICATION = 'Claim - Renter Initiated - Homes Victoria - Registered RP Email Notification';
	public static final String EMAIL_TITLE_RIC_HV_BOND_UNREG_RP_NOTIFICATION = 'RIC-HVBond-Unregistered-RPEmail-16515AC2';
	public static final String EMAIL_TITLE_RIC_HV_BOND_UNREG_RP_NOTIFICATION2 = 'Claim - Renter Initiated - Homes Victoria - UnRegistered RP Email Notification';
	public static final String EMAIL_TITLE_RIC_HV_BOND_RENTER_NOTIFICATION = 'RIC-HVBond-RenterEmail-16513';
	public static final String EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_WITH_BANK_DETAILS = 'RIC-Without Consent-All parties Agreed-UnReg RP Notification-8424-AC1';
	public static final String EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_WITHOUT_BANK_DETAILS = 'RIC-Without Consent-All parties Agreed-UnReg RP Notification-8424-AC2';
	public static final String EMAIL_TITLE_REPAYMENT_EXCEPTION_CLAIM_RENTER_NOTIFICATION = 'Repayment Exception - Claims Transaction - Renter Email Notification';
	public static final String EMAIL_TITLE_REPAYMENT_EXCEPTION_CLAIM_RP_NOTIFICATION = 'Repayment Exception - Claims Transaction - RP Email Notification';
	public static final String EMAIL_TITLE_REPAYMENT_EXCEPTION_RR_RP_NOTIFICATION = 'Repayment Exception - RR Transaction - RP Email Notification';
	public static final String EMAIL_TITLE_REPAYMENT_EXCEPTION_RR_RENTER_NOTIFICATION = 'Repayment Exception - RR Transaction - Renter Email Notification';
	public static final String DATE_TIME_FORMAT = 'dd/MM/yyyy hh:mm:ss a';

	/* MC Journey Key  */
	public static final String MC_JOURNEY_KEY_BLANK = 'Blank';
	public static final String MC_JOURNEY_KEY_SINGLE_RENTER_BPAY_INVOICE = 'Single Renter BPAY Invoice';
	public static final String MC_JOURNEY_KEY_RENTER_INITIATED_CLAIM_VERIFICATION = 'Renter Initiated Claim Verification';
	public static final String MC_JOURNEY_KEY_RETAINED_REPAYMENT_VERIFICATION = 'Retained Repayment Verification';

	/* MC Journey Name - matches entries in MC_Journey_Mapping__mdt */
	public static final String MC_JOURNEY_NAME_RPIC_FINALISED_CLAIM_BY_AGREEMENT = 'RPIC-FinalisedClaim-byAgreement-16491';
	public static final String MC_JOURNEY_NAME_RPIC_FINALISED_CLAIM_RENTERS_NOT_CONTACTABLE = 'RPIC-FinalisedClaim-Renters-not-contactable-16493';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_INITIATOR_CANCELS_EMAIL_TO_TRANSFEREE = 'RP Transfer-With Consent-Initiating RP Cancels Tx-Email to RP Transferee-9479';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_INITIATOR_CANCELS_EMAIL_TO_INITIATOR = 'RP Transfer - Initiating RP Cancels Transaction - Email Noti Initiating RP-9467';
	public static final String MC_JOURNEY_NAME_BOND_MERGE_RECEIPT = 'Bond Merge Receipt';
	public static final String MC_JOURNEY_NAME_RENTER_TRANSFER_RESUBMIT_FIRST_EMAIL = 'RTransfer-ResubCancelledTimedout RT byAgreement -RenterFirstEmailNoti -9321';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_WC_INITIAL_TO_TRANSFEREE = 'RP Transfer-With Consent-First Email Notification to RP Transferee-3901';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_REJECTED = 'RPT-Transferee Review-Reject Transfer-Txn Cancelled Email to RPs-8108';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_TRANSFEREE_REVIEW_TIMED_OUT_EMAIL_TO_RP_TRANSFEREE = 'RP Transfer - Transferee Review - Timed Out - Email to RP Transferee-9481';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_TRANSFEREE_REVIEW_TIMED_OUT_EMAIL_TO_TRANSFEROR = 'RP Transfer - Transferee Review - Timed Out - Email Noti to RP Transferor 9473';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_FINALISED_NOTIFICATIONS = 'NotificationsFinalisedRPTransferTransaction-14786';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEROR = 'RP Transfer - With Consent - Transfer Finalised - Email to RP Transferor-4576';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEREE = 'RP-Transfer-Finalised-Transferee-8725';
	public static final String MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_RENTERS = 'RP Transfer-With Consent-Transfer Finalised-Receipt Email to Renters-8109';
	public static final String MC_JOURNEY_NAME_RTRANSFER_REVIEW_RENTER_REMINDER_TIMEOUT = 'RTransfer-Review-Renter-Reminder_Timeout-3359';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RPREVIEW_EMAIL_NOTIFICATIONS_TO_RP = 'RTransfer-RPReview-EmailNotifcations-to-RP-5117';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RPREVIEW_EMAIL_NOTIFICATIONS_TO_RENTERS = 'RTransfer-RPReview-EmailNotifcations-to-Renters-5164';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_EMAIL_NOTIFICATIONS_TO_RP = 'RenterTransfer-RenterReview-ChangeRequestSubmitted-EmailtoRP-3511';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_EMAIL_NOTIFICATIONS_TO_RENTERS = 'RTransfer-Change-Request-Renter-5072';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_SMS_NOTIFICATIONS_TO_RENTERS = 'RTransfer-RenterReview-ChangeRequestSubmitted-RenterSMS-5074';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_EMAIL_NOTIFICATIONS_TO_RP = 'RTransfer-Transaction-cancelled-after-declining-Terms-of-Use-Email-to-RP-4843';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_EMAIL_NOTIFICATIONS_TO_RENTERS = 'RTransfer - By Agree- RNotif-Tx Can Email after declining Terms of Use-3699';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_ACCEPTED_SMS_TO_RENTERS = 'RTransfer-ByAgreement-TransactionFinalisationSMS-5037';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_SMS_NOTIFICATIONS_TO_RENTERS = 'Rntr Trnsfr - By Agrmt- Rntr noti- Tx Can SMS after declining Terms of Use-3694';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_7DREMINDER_EMAIL_RENTER_BOUNCED_TO_RP = 'RTransfer-7DReminder-EmailBounced-CancellationEmail-to-RP-9281';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RREVIEW_7DREMINDER_EMAIL_RENTER_BOUNCED_TO_RENTERS = 'RTransfer-7DReminder-EmailBounced-CancellationEmail-to-Renter-9284';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RPCANCELS_TRANSACTION_EMAIL_NOTIFICATIONS_TO_RENTERS = 'RTransfer-RPCancelsTransaction-EmailNotifcations-to-Renters-6451';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RP_SUBMITTED_EMAIL_BOUNCED_TO_RENTER = 'RTransfer-RP-Submitted-EmailBounced-to-Renter-9276';
	public static final String MC_JOURNEY_NAME_RTRANSFER_FIRST_EMAIL_TO_RENTER_BOUNCED_TXN_CANCELLATION_EMAIL_TO_RP = 'RT - First Email to Renter Bounced - Txn Cancellation-Email to RP-9323';
	public static final String MC_JOURNEY_NAME_RTRANSFER_BY_AGREEMENT_TRANSFER_RECEIPT_TO_RP = 'RTransfer-By-Agreement-Transfer-Receipt-to-RP-5044';
	public static final String MC_JOURNEY_NAME_RTRANSFER_BY_AGREEMENT_TRANSFER_RECEIPT_TO_RENTERS = 'RTransfer-By-Agreement-Transfer-Receipt-to-Renter-5042';
	public static final String MC_JOURNEY_NAME_RTRANSFER_TRANSACTION_CANCEL = 'InternalUser-RTransfer-TransactionCancel-12787';
	public static final String MC_JOURNEY_NAME_RTRANSFER_RPRESUBMITTED_FIRST_EMAIL_BOUNCED_CANCELLATION_EMAIL = 'RTransfer-RPResubmitted-FirstEmailBounced-CancellationEmail-to-Renter-9324';
	public static final String MC_JOURNEY_NAME_RT_CANCELLED_BY_RP_SMS_TO_RENTER = 'Renter Transfer - RP Cancels Transaction - SMS Notification to Renters-6454';
	public static final String MC_JOURNEY_NAME_RIC_HVBOND_RP_NOTIFICATION = 'RIC-HVBond-RPNotification-16515';
	public static final String MC_JOURNEY_NAME_RIC_HVBOND_RENTER_NOTIFICATION = 'RIC-HVBond-RenterNotification-16513';
	public static final String MC_JOURNEY_NAME_BOND_STATUS_ACTIVE_TO_CANCELLED = 'InternalUser-Bond-StatusChange-ActiveToCancelled-16708';
	public static final String MC_JOURNEY_NAME_REPAYMENT_PAIN_EXCEPTION = 'Repay excpt - PAIN 2 or 3 unsuccessful repayment - SMS noti to Txp - 17185';
	public static final String MC_JOURNEY_NAME_RIC_PRIVATE_WO_CONSENT_RP = 'RIC-WithoutConsentPrivateBond-(RP)Notification-16507';
	public static final String MC_JOURNEY_NAME_RIC_PRIVATE_WO_CONSENT_RENTER = 'RIC-WithoutConsentPrivateBond-EmailtoRenter-16511';
	public static final String MC_JOURNEY_NAME_RIC_PRIVATE_VCAT_OTHER_RENTER_ZERO = 'RIC-Private-ValidVCATOrder-AcceptedRenterRepaymentAmount-OtherRenterEmail-16509';
	public static final String MC_JOURNEY_NAME_RIC_PRIVATE_VCAT_REG_RP = 'RIC-Private-Validated-VCAT-Order-Accepted-Repayment-Amounts-RPEmail-16501';
	public static final String MC_JOURNEY_NAME_RIC_PRIVATE_VCAT_UNREG_RP = 'RIC-WithoutConsentPrivateValidatedVCATOrderAccepted-EmailtoUnregRP-16499';
	public static final String MC_JOURNEY_NAME_RIC_PRIVATE_VCAT_INITIATOR = 'RIC-Private-VCAT-Order-Accepted-Repay-Amounts-InitRenterEmail-16505';
	public static final String MC_JOURNEY_NAME_RPC_PRIV_VALVCAT = 'RPIC-RPClaimsSuccessfulSubmission-VCAT-Private-Renter-RP-Notifications-16495';
	public static final String MC_JOURNEY_NAME_RPC_HV_FULL_RETURN = 'RPIC-HV-Full-Refund-Bond-16431';
	public static final String MC_JOURNEY_RENTER_RESUBMIT_UNEDITED_EMAIL_NOTIFICATIONS_TO_RENTERS = 'RTransfer-Change request rejected-RP Review Process-9314';
	public static final String MC_JOURNEY_RENTER_RESUBMIT_UNEDITED_SMS_NOTIFICATIONS_TO_RENTERS = 'RTransfer-Unedited-Renter-Transfer-Renter-SMS-9317';
	public static final String MC_JOURNEY_RENTER_RESUBMIT_CANCEL_OR_TIMEOUT_SMS_NOTIFICATIONS_TO_RENTERS = 'RTransfer-Resubmitted-ByAgreement-RenterSMS-7233';
	public static final String MC_JOURNEY_RENTER_RESUBMIT_EDITED_SMS_NOTIFICATIONS_TO_RENTERS = 'RTransfer-Edited-RenterAcceptance-5421';

	/* Listview Name  */
	public static final String LISTVIEW_NAME_RENTAL_PROVIDER_TRANSFERS = 'RENTAL_PROVIDER_TRANSFER';

	/* External User Groups  */
	public static final String EXTERNAL_USER_GROUP_ATO = 'ATO';
	public static final String EXTERNAL_USER_GROUP_CAV = 'CAV';
	public static final String EXTERNAL_USER_GROUP_HOMES_VICTORIA = 'Homes Victoria';
	public static final String EXTERNAL_USER_GROUP_REGIONAL_SERVICES = 'Regional Services';
	public static final String EXTERNAL_USER_GROUP_RENTER_TASK_FORCE = 'Renter Task Force';
	public static final String EXTERNAL_USER_GROUP_SRO = 'SRO';
	public static final String EXTERNAL_USER_GROUP_VCAT = 'VCAT';

	public static final List<String> EXTERNAL_USER_GROUPS = new List<String>{
		EXTERNAL_USER_GROUP_ATO,
		EXTERNAL_USER_GROUP_CAV,
		EXTERNAL_USER_GROUP_HOMES_VICTORIA,
		EXTERNAL_USER_GROUP_REGIONAL_SERVICES,
		EXTERNAL_USER_GROUP_RENTER_TASK_FORCE,
		EXTERNAL_USER_GROUP_SRO,
		EXTERNAL_USER_GROUP_VCAT
	};
	/* Bond Recoupment Status Values  */
	public static final String BOND_RECOUPMENT_STATUS_PENDING_REVIEW = 'Pending Review';
	public static final String BOND_RECOUPMENT_STATUS_PENDING_APPROVAL = 'Pending Approval';
	public static final String BOND_RECOUPMENT_STATUS_APPROVED = 'Approved';
	public static final String BOND_RECOUPMENT_STATUS_PROCESSED = 'Processed';
}