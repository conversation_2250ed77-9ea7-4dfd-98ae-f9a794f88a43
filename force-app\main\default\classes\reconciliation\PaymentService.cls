public with sharing class PaymentService {
	public static void runDirectDebitReconciliation(
		Bank_Account_Statement_Transaction__c bankTrans
	) {
		String query =
			'SELECT Id, Payment_Amount__c, Reconciled_Amount__c, Bond__c ' +
			'FROM Payment__c ' +
			'WHERE Status__c = :status ' +
			'AND DAY_ONLY(Westpac_Disbursement_File_Date__c) = :date ' +
			'AND Payment_Method__c = :method ' +
			'AND Westpac_Disbursement_File_Name__c != NULL ' +
			'AND Bank_Account_Statement_Transaction__c = NULL';

		Map<String, Object> bindVariables = new Map<String, Object>{
			'status' => Constants.PAYMENT_STATUS_SUCCESSFUL,
			'method' => Constants.PAYMENT_PAYMENT_METHOD_DIRECT_DEBIT,
			'date' => bankTrans.Bank_Account_Statement__r.Balance_Date__c
		};

		List<Payment__c> directDebitPayments = Database.queryWithBinds(
			query,
			bindVariables,
			AccessLevel.SYSTEM_MODE
		);

		paymentsReconciliation(bankTrans, directDebitPayments);
	}

	// todo: Commented while finding a way to reconcile International DC Returns. Currently, it is not possible to detect these from BAST.
	// public static void runIntDCReturnRecon(
	// 	Bank_Account_Statement_Transaction__c bankTrans
	// ) {
	// 	String query =
	// 		'SELECT Id, Payment_Amount__c, Reconciled_Amount__c, Bond__c ' +
	// 		'FROM Payment__c ' +
	// 		'WHERE Status__c = :status ' +
	// 		'AND Payment_Method__c = :method ' +
	// 		'AND Original_Repayment__c != NULL ' +
	// 		'AND Original_Repayment__r.Repayment_Method__c = :repaymentMethod ' +
	// 		'AND Bank_Account_Statement_Transaction__c = NULL';

	// 	Map<String, Object> bindVariables = new Map<String, Object>{
	// 		'status' => Constants.PAYMENT_STATUS_SUCCESSFUL,
	// 		'method' => Constants.PAYMENT_PAYMENT_METHOD_DIRECT_DEBIT,
	// 		'repaymentMethod' => Constants.REPAYMENT_METHOD_INTERNATIONAL_DIRECT_CREDIT
	// 	};

	// 	List<Payment__c> intDCReturns = Database.queryWithBinds(
	// 		query,
	// 		bindVariables,
	// 		AccessLevel.SYSTEM_MODE
	// 	);

	// 	paymentsReconciliation(bankTrans, intDCReturns);
	// }

	// todo: replace with next function
	// public static void runAusDCReturnRecon(
	// 	Bank_Account_Statement_Transaction__c bankTrans
	// ) {
	// 	String query =
	// 		'SELECT Id, Payment_Amount__c, Reconciled_Amount__c, Bond__c ' +
	// 		'FROM Payment__c ' +
	// 		'WHERE Status__c = :status ' +
	// 		'AND Payment_Method__c = :method ' +
	// 		'AND Original_Repayment__c != NULL ' +
	// 		'AND Original_Repayment__r.Repayment_Method__c = :repaymentMethod ' +
	// 		'AND Bank_Account_Statement_Transaction__c = NULL';

	// 	Map<String, Object> bindVariables = new Map<String, Object>{
	// 		'status' => Constants.PAYMENT_STATUS_SUCCESSFUL,
	// 		'method' => Constants.PAYMENT_PAYMENT_METHOD_DIRECT_DEBIT,
	// 		'repaymentMethod' => Constants.REPAYMENT_METHOD_DIRECT_CREDIT
	// 	};

	// 	List<Payment__c> ausDCReturns = Database.queryWithBinds(
	// 		query,
	// 		bindVariables,
	// 		AccessLevel.SYSTEM_MODE
	// 	);

	// 	paymentsReconciliation(bankTrans, ausDCReturns);
	// }

	public static void runAusDCReturnRecon(
		Bank_Account_Statement_Transaction__c bankTrans
	) {
		String query =
			'SELECT Id, Amount__c, Reconciled_Amount__c, Bond__c, Repayment_Type__c ' +
			'FROM Repayment__c ' +
			'WHERE Status__c = :status ' +
			'AND Repayment_Method__c = :method ' +
			'AND Repayment_Reference_Number__c = :referenceNumber ' +
			'AND Bank_Account_Statement_Transaction__c != NULL';

		Map<String, Object> bindVariables = new Map<String, Object>{
			'status' => Constants.REPAYMENT_STATUS_SUCCESSFUL,
			'method' => Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			'referenceNumber' => bankTrans.End_To_End_Identification__c
		};

		List<Repayment__c> ausDCReturnRepayments = Database.queryWithBinds(
			query,
			bindVariables,
			AccessLevel.SYSTEM_MODE
		);

		// todo create a payment record for the repayment
		// Original Repayment = the Repayment found above

Bond = Repayment Bond

Amount = Repayment Amount

Method = Direct Debit

Status = Successful

Transaction = Repayment Transaction

Bank_Account_Statement_Transaction__c = the incoming Bank_Account_Statement_Transaction__c.Id



		// repaymentsRecon(bankTrans, ausDCReturnRepayments);

		// // setting status and running the corresponding flow trigger to create Payment and send notifications
		// for (Repayment__c repayment : ausDCReturnRepayments) {
		// 	repayment.Status__c = Constants.REPAYMENT_STATUS_RETURNED;
		// }
		// update ausDCReturnRepayments;
	}

	public static void runCCRecon(
		Bank_Account_Statement_Transaction__c bankTrans
	) {
		String query =
			'SELECT Id, Payment_Amount__c, Reconciled_Amount__c, Bond__c ' +
			'FROM Payment__c ' +
			'WHERE Status__c = :status ' +
			'AND Settlement_Date__c = :date ' + //this is a large assumption which we don't know is valid
			'AND (Payment_Method__c = :method ' +
			'OR Payment_Method__c = :secMethod) ' +
			'AND Bank_Account_Statement_Transaction__c = NULL';

		Map<String, Object> bindVariables = new Map<String, Object>{
			'status' => Constants.PAYMENT_STATUS_SUCCESSFUL,
			'method' => Constants.PAYMENT_PAYMENT_METHOD_CREDIT_CARD,
			'secMethod' => Constants.PAYMENT_PAYMENT_METHOD_DEBIT_CARD,
			'date' => bankTrans.Bank_Account_Statement__r.Balance_Date__c
		};

		List<Payment__c> directDebitPayments = Database.queryWithBinds(
			query,
			bindVariables,
			AccessLevel.SYSTEM_MODE
		);

		paymentsReconciliation(bankTrans, directDebitPayments);
	}

	public static void paymentsReconciliation(
		Bank_Account_Statement_Transaction__c bankTrans,
		List<Payment__c> payments
	) {
		try {
			Decimal totalReconciledAmount = 0;
			Set<Id> bondIds = new Set<Id>();

			Logger.info('Payments to reconcile: ' + payments, payments);

			for (Payment__c payment : payments) {
				payment.Reconciled_Amount__c = payment.Payment_Amount__c != null
					? payment.Payment_Amount__c
					: 0;
				payment.Bank_Account_Statement_Transaction__c = bankTrans.Id;
				totalReconciledAmount += payment.Reconciled_Amount__c;
				bondIds.add(payment.Bond__c);
			}

			Decimal amountDifference =
				bankTrans.Reconciliation_Difference__c - totalReconciledAmount;

			if (amountDifference > 0) {
				CasesService.createCaseForReconciliationError(
					amountDifference,
					bankTrans.Id
				);
			} else if (amountDifference < 0) {
				for (Payment__c payment : payments) {
					payment.Reconciled_Amount__c = 0;
					payment.Bank_Account_Statement_Transaction__c = null;
				}

				if (bankTrans.Reconciliation_Difference__c != 0) {
					CasesService.createCaseForReconciliationError(
						bankTrans.Reconciliation_Difference__c,
						bankTrans.Id
					);
				}
			}

			TriggerHandler.bypass('PaymentTriggerHandler');
			update payments;
			TriggerHandler.clearbypass('PaymentTriggerHandler');

			BondController.updateReconciledAmount(bondIds);
			BankAccountStmtTransactionController.updateReconciledAmount(
				new Set<Id>{ bankTrans.Id }
			);
		} catch (Exception e) {
			Logger.error('Error on payments reconciliation: ' + e, e);
		}
	}
}