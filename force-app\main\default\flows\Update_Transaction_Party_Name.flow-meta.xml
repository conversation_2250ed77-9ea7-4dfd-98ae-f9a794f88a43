<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Sets the TP prefix for a RP initiated claim transaction</description>
        <name>Claim_TX_Parties_Name_2</name>
        <label>Claim TX Parties Name 2</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>TX_Party_Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!Prefix_For_RP_Initiated_Claims}{!$Record.Transaction_Party_Auto_Number__c}</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Transaction_Party_Name</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets the TP prefix for a renter initiated claim transaction</description>
        <name>Claim_TX_Parties_RIC</name>
        <label>Claim TX Parties RIC</label>
        <locationX>314</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>TX_Party_Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>RIC{!$Record.Transaction_Party_Auto_Number__c}</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Transaction_Party_Name</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets the TP prefix for a non claim transaction</description>
        <name>Non_Claim_TX_Parties_Name</name>
        <label>Non Claim TX Parties Name</label>
        <locationX>578</locationX>
        <locationY>539</locationY>
        <assignmentItems>
            <assignToReference>TX_Party_Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NonTransactionParties</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Transaction_Party_Name</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>Prefix_For_Bond_Amendment</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BA</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction Parties Prefix for Bond Lodgement</description>
        <name>Prefix_For_Bond_Lodgement</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BL</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction prefix for bond merge</description>
        <name>Prefix_For_Bond_Merge</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BM</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction Parties Prefix for Modification Bond Lodgement</description>
        <name>Prefix_For_Modification_Bond_Lodgement</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BL</stringValue>
        </value>
    </constants>
    <constants>
        <description>Prefix for renter transfer</description>
        <name>Prefix_For_Renter_Transfer</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RT</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction Parties Prefix for RP Initiated Claims</description>
        <name>Prefix_For_RP_Initiated_Claims</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BC</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction Parties Prefix for RP Transfer</description>
        <name>Prefix_For_RP_Transfer</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RPT</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Determine the type of transaction</description>
        <name>Type_Based_TX_Parties</name>
        <label>Type Based TX_Parties</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <defaultConnector>
            <targetReference>Non_Claim_TX_Parties_Name</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Non Claim Type</defaultConnectorLabel>
        <rules>
            <name>Claim_with_Rental_Provider_Initiated_Claim</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Transaction.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>CLAIMS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Transaction.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Claim_TX_Parties_Name_2</targetReference>
            </connector>
            <label>Claim with Rental Provider Initiated Claim</label>
        </rules>
        <rules>
            <name>Claim_with_Renter_Initiated_Claim</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Transaction.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>CLAIMS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Transaction.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Claim_TX_Parties_RIC</targetReference>
            </connector>
            <label>Claim with Renter Initiated Claim</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>NonTransactionParties</name>
        <dataType>String</dataType>
        <expression>CASE(
{!Get_Transaction.Type__c}, &apos;Bond Lodgement&apos;, {!Prefix_For_Bond_Lodgement},
&apos;Modification Bond Lodgement&apos;, {!Prefix_For_Modification_Bond_Lodgement},
&apos;Claims&apos;, &apos;CL&apos;,
&apos;Rental Provider Transfer&apos;, {!Prefix_For_RP_Transfer},
&apos;Renter Transfer&apos;, {!Prefix_For_Renter_Transfer},
&apos;Bond Merge&apos;, {!Prefix_For_Bond_Merge},
&apos;Bond Amendment&apos;, {!Prefix_For_Bond_Amendment},
&apos;&apos;
)+{!$Record.Transaction_Party_Auto_Number__c}</expression>
    </formulas>
    <interviewLabel>Update Transaction Party Name {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Transaction Party Name</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Gets the transaction details.</description>
        <name>Get_Transaction</name>
        <label>Get Transaction</label>
        <locationX>314</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Type_Based_TX_Parties</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction__r.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Transaction__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Type__c</queriedFields>
        <queriedFields>Origin__c</queriedFields>
        <queriedFields>Role_Initiating_Claim__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update the transaction name with the correct number and prefix.</description>
        <name>Update_Transaction_Party_Name</name>
        <label>Update Transaction Party Name</label>
        <locationX>314</locationX>
        <locationY>731</locationY>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>TX_Party_Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Transaction</targetReference>
        </connector>
        <object>Transaction_Party__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>TX_Party_Name</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
