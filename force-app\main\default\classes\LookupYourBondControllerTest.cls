@isTest
public class LookupYourBondControllerTest {
	@TestSetup
	static void setup() {
		TestDataFactory.assignSiteGuestUserPermissions();
	}

	@isTest
	public static void searchBond_bond_true() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Bond_Party__c bondParty = TestDataFactory.createBondParty(
			bond,
			Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert bondParty;

		bond = [SELECT Id, Name FROM Bond__c WHERE Id = :bond.Id LIMIT 1];

		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = bond.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL;
			searchParams.familyName = bondParty.Family_Name__c;
			searchParams.firstName = bondParty.First_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(searchResult.foundRecord, 'Should have found bond');
			Assert.isNotNull(
				searchResult.redirect?.pageName,
				'Matching info. Should have returned page name to redirect user.'
			);
			Assert.isNotNull(
				searchResult.redirect?.encryptedId,
				'Matching info. Should have found bond.'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_bond_false() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Bond_Party__c bondParty = TestDataFactory.createBondParty(
			bond,
			Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert bondParty;

		bond = [SELECT Id, Name FROM Bond__c WHERE Id = :bond.Id LIMIT 1];

		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = bond.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL;
			searchParams.familyName = 'incorrect' + bondParty.Family_Name__c;
			searchParams.firstName = bondParty.First_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isFalse(
				searchResult.foundRecord,
				'Should NOT have found bond'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_true() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		transactionParty.Review_URL__c = 'http://test.com';
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				transactionParty.Review_URL__c,
				searchResult.redirect.url,
				'Matching transaction party info. Should have returned transaction party review URL'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_status_failed() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FAILED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Your_Bond_Message_When_Transaction_Status_Is_Failed,
				'Transaction with status failed. Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_removed_renter() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS;
		insert tx;
		Transaction_Party__c tp1 = TestDataFactory.createTransactionParty(tx);
		Transaction_Party__c tp2 = TestDataFactory.createTransactionParty(tx);
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert new List<Transaction_Party__c>{ tp1, tp2, transactionParty };
		transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_REMOVED;
		transactionParty.Reason_for_Removal__c = 'Testing';
		update transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];

		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				Label.Message_Lookup_Bond_When_Transaction_Party_Removed,
				searchResult.message,
				'Renter has been removed. Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_cancelled_tc_rejected() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		tx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_TC_REJECTED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];

		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_On_Bond_Search_If_Transaction_Sub_Status_Is_TC_Rejected,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_cancelled_email_bounced() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		tx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_EMAIL_BOUNCED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];

		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_On_Bond_Search_If_Transaction_Sub_Status_Is_Email_Bounced,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_rp_cancelled() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		tx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RENTAL_PROVIDER_CANCELLED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];

		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_On_Bond_Search_If_Transaction_Sub_Status_Is_RP_Cancelled,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_cancelled_payment_timedout() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		tx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RENTER_PAYMENT_TIMED_OUT;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Message_Lookup_Bond_When_Renter_Payment_Timed_Out,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_pending_with_rp() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];

		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Message_When_View_Transaction_If_Pending_With_RP_For_Renter_Change_Request,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_timedout() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_TIMEDOUT;
		tx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RENTER_TIMED_OUT;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Message_Lookup_Bond_When_Transaction_Timed_Out,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_timedout_claim() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c claimTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		claimTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_CLAIM;
		claimTx.Bond__c = bond.Id;
		claimTx.Status__c = Constants.TRANSACTION_STATUS_TIMEDOUT;
		claimTx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RENTAL_PROVIDER_TIMED_OUT;
		claimTx.Role_Initiating_Claim__c = Constants.TRANSACTION_ROLE_INITIATING_CLAIM_BY_RENTAL_PROVIDER;
		insert claimTx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			claimTx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Message_Lookup_Bond_When_Claim_Transaction_Timed_Out,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_pending_payment() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_PAYMENT;
		tx.Payment_URL_Expiry_Date__c = Datetime.now().addDays(1);
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		transactionParty.Payment_URL__c = 'http://test.com';
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Payment_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				transactionParty.Payment_URL__c,
				searchResult.redirect.url,
				'Matching transaction party info. Should have returned transaction party payment URL'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_pending_payment_expired() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_PAYMENT;
		tx.Payment_URL_Expiry_Date__c = Datetime.now().addDays(-1);
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Message_Lookup_Bond_When_Transaction_Payment_Period_Expired,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_finalised_ibl() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Message_Lookup_Bond_When_Lodgement_Transaction_Finalised,
				'Should have returned the expected message'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_renterTransferTransactionPartyNumber_true() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		//Create a renter transfer transaction, transaction number starts With('RT')
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS;
		tx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		tx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		insert tx;

		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;
		transactionParty.Review_URL__c = 'http://test.com';
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c, Party_Status__c, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction party'
			);
			Assert.areEqual(
				transactionParty.Review_URL__c,
				searchResult.redirect.url,
				'Matching transaction party info. Should have returned transaction party review URL'
			);
		}

		Test.stopTest();
	}

	@isTest
	public static void searchBond_transactionPartyNumber_false() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_EMAIL_BOUNCED;
		transactionParty.Review_URL__c = 'http://test.com';
		insert transactionParty;
		transactionParty = [
			SELECT id, Name, Company_Name__c
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName =
				'incorrect' + transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isFalse(
				searchResult.foundRecord,
				'Should NOT have found transaction party'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void testEncryptionDecrytion() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Test.startTest();
		String encryptedReference = LookupYourBondController.doEncryption(
			bond.Id
		);
		Id encryptedBondId = LookupYourBondController.decryptString(
			encryptedReference
		);
		Test.stopTest();
		Assert.areEqual(encryptedBondId, bond.Id, 'Should be the same bond id');
	}

	@isTest
	public static void searchBond_transaction_number_for_RIC() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RENTER_CANCELLED;
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		transactionParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_tp_number_for_RIC() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;

		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;

		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;

		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		insert ric;

		// ensure Transaction name is different from the Party name
		ric.Name = 'RIC0';
		update ric;

		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RENTER_CANCELLED;
		insert ricParty;

		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		transactionParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];

		LookupYourBondController.SearchResult searchResult;
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
		}
		Test.stopTest();

		Assert.isFalse(
			searchResult.foundRecord,
			'Should NOT have found transaction because we used transaction party number instead of transaction number for a Renter Initiated Claim'
		);
	}

	@isTest
	public static void searchBond_transaction_for_IBL() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;

		// ensure Transaction name is different from the Party name
		tx.Name = 'BL0';
		update tx;

		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;

		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;

		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		insert ric;

		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RENTER_CANCELLED;
		insert ricParty;

		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		transactionParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];

		LookupYourBondController.SearchResult searchResult;
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
		}

		Test.stopTest();

		Assert.isFalse(
			searchResult.foundRecord,
			'Should NOT have found transaction because we used transaction number instead of transaction party number for a bond lodgement transaction'
		);
	}

	@isTest
	public static void searchBond_tp_number_for_IBL() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RENTER_CANCELLED;
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		transactionParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :transactionParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = transactionParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = transactionParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction party number for a bond lodgement transaction'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_transaction_for_RPClaim() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;

		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;

		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;

		Transaction__c rpClaim = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		rpClaim.Role_Initiating_Claim__c = Constants.TRANSACTION_ROLE_INITIATING_CLAIM_BY_RENTAL_PROVIDER;
		rpClaim.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert rpClaim;

		// ensure Claim Transaction name is different from the Claim Party name
		rpClaim.Name = 'RIC0';
		update rpClaim;

		Transaction_Party__c rpClaimParty = TestDataFactory.createTransactionParty(
			rpClaim
		);
		insert rpClaimParty;

		rpClaimParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :rpClaimParty.Id
		];

		LookupYourBondController.SearchResult searchResult;
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = rpClaimParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = rpClaimParty.Company_Name__c;

			searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
		}

		Test.stopTest();

		Assert.isFalse(
			searchResult.foundRecord,
			'Should NOT have found transaction because we used transaction number ' +
				rpClaimParty.Transaction__r.Name +
				' instead of transaction party number for a RP initiated claim'
		);
	}

	@isTest
	public static void searchBond_tp_number_for_RPClaim() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c rpClaim = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		rpClaim.Role_Initiating_Claim__c = Constants.TRANSACTION_ROLE_INITIATING_CLAIM_BY_RENTAL_PROVIDER;
		rpClaim.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert rpClaim;
		Transaction_Party__c rpClaimParty = TestDataFactory.createTransactionParty(
			rpClaim
		);
		insert rpClaimParty;
		rpClaimParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :rpClaimParty.Id
		];
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = rpClaimParty.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = rpClaimParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction party number ' +
					rpClaimParty.Name +
					'for a RP initiated claim'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC6_usingAnotherTP() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Status__c = Constants.TRANSACTION_STATUS_PENDING_RESPONSE;
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		Transaction_Party__c anotherRicParty = TestDataFactory.createTransactionParty(
			ric,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		anotherRicParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_DISPUTED;
		insert new List<Transaction_Party__c>{ ricParty, anotherRicParty };
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.isNotNull(
				searchResult.redirect.encryptedId,
				'Should have returned the encrypted Id for the transaction'
			);
			Assert.areEqual(
				PublicTransactionSummaryController.getIdFromToken(
					TokenUtility.decodeToken(searchResult.redirect.encryptedId)
				),
				ric.Id,
				'Should have returned the encrypted Id for the transaction'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC6() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Status__c = Constants.TRANSACTION_STATUS_PENDING_RESPONSE;
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_DISPUTED;
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.isNotNull(
				searchResult.redirect.encryptedId,
				'Should have returned the encrypted Id for the transaction'
			);
			Assert.areEqual(
				PublicTransactionSummaryController.getIdFromToken(
					TokenUtility.decodeToken(searchResult.redirect.encryptedId)
				),
				ric.Id,
				'Should have returned the encrypted Id for the transaction'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC7() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		ric.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_DISPUTED;
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_If_RIC_Cancelled_and_Party_Disputed,
				'Should have returned the correct message'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC8() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		ric.Status__c = Constants.TRANSACTION_STATUS_RTBA_CANCELLED;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_POST_RETURNED;
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_If_RIC_RTBA_Cancelled_and_Party_Post_Returned,
				'Should have returned the correct message'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC9() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		ric.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		ricParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RENTER_CANCELLED;
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_If_RIC_Cancelled_by_Renter,
				'Should have returned the correct message'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC10() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		ric.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RTBA;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_If_RIC_Pending_with_RTBA,
				'Should have returned the correct message'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC11() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		ric.Status__c = Constants.TRANSACTION_STATUS_RTBA_REJECTED;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_If_RIC_RTBA_Rejected,
				'Should have returned the correct message'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC12() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today().addMonths(-1);
		ric.Status__c = Constants.TRANSACTION_STATUS_PENDING_RESPONSE;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Lookup_Bond_Message_If_RIC_Expired,
				'Should have returned the correct message'
			);
		}
		Test.stopTest();
	}

	@isTest
	public static void searchBond_CARRP_11032_AC13() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;
		Transaction_Party__c transactionParty = TestDataFactory.createTransactionParty(
			tx
		);
		insert transactionParty;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c ric = TestDataFactory.createRICTransaction(
			rentalProvider,
			bond.Id
		);
		ric.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		ric.Submission_Date__c = Date.today();
		ric.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert ric;
		Transaction_Party__c ricParty = TestDataFactory.createTransactionParty(
			ric
		);
		insert ricParty;
		ricParty = [
			SELECT Id, Name, Company_Name__c, Transaction__r.Name, Review_URL__c
			FROM Transaction_Party__c
			WHERE Id = :ricParty.Id
		];
		// Set up mock HTTP response for reCAPTCHA
		Test.setMock(
			HttpCalloutMock.class,
			new GoogleReCaptchaTest.MockHttpResponseGenerator()
		);

		Test.startTest();
		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			LookupYourBondController.SearchParameters searchParams = new LookupYourBondController.SearchParameters();
			searchParams.bondOrTransactionPartyNumber = ricParty.Transaction__r.Name;
			searchParams.renterType = Constants.BOND_PARTY_RENTER_TYPE_COMPANY;
			searchParams.companyName = ricParty.Company_Name__c;

			LookupYourBondController.SearchResult searchResult = LookupYourBondController.searchBondOrTransaction(
				searchParams,
				'testRecaptchaToken'
			);
			Assert.isTrue(
				searchResult.foundRecord,
				'Should have found transaction because we used transaction number for a Renter Initiated Claim'
			);
			Assert.areEqual(
				searchResult.message,
				Label.Message_Lookup_Bond_When_RP_Claim_Transaction_Finalised,
				'Should have returned the correct message'
			);
		}
		Test.stopTest();
	}
}