@isTest
private class RepaymentSearchInvocableTest {
	// create test setup
	@TestSetup
	static void setup() {
		Account newRP = TestDataFactory.createRentalProviderAccount();
		insert newRP;

		Transaction__c tx = TestDataFactory.createTransaction(newRP);
		insert tx;

		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;

		Repayment__c repayment = TestDataFactory.createRepayment(
			bond.Id,
			Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			500.00
		);
		repayment.Status__c = Constants.REPAYMENT_STATUS_SUCCESSFUL;
		insert repayment;
	}

	@IsTest
	static void searchRepayments_shouldReturnRepaymentRecord() {
		// Given
		Bond__c bond = [SELECT Id, Name FROM Bond__c LIMIT 1];
		Repayment__c repayment = [
			SELECT
				Id,
				Name,
				Repayment_Date__c,
				Amount__c,
				Repayment_Method__c,
				Payee_Type__c,
				Bond__r.Name
			FROM Repayment__c
			LIMIT 1
		];

		RepaymentSearchInvocable.SearchRequest request = new RepaymentSearchInvocable.SearchRequest();
		request.repaymentAmount = 500.00;
		request.bondNumber = bond.Name;
		request.repaymentDate = System.Today();
		request.repaymentNumber = repayment.Name;
		request.repaymentMethod = Constants.REPAYMENT_METHOD_DIRECT_CREDIT;

		List<RepaymentSearchInvocable.SearchRequest> requests = new List<RepaymentSearchInvocable.SearchRequest>();
		requests.add(request);

		// When
		Test.startTest();

		List<List<Repayment__c>> results = RepaymentSearchInvocable.searchRepayments(
			requests
		);

		Test.stopTest();

		// Then
		Assert.areEqual(1, results.size(), 'Results should be returned');
		Assert.areEqual(1, results[0].size(), 'Results should be returned');
	}
}