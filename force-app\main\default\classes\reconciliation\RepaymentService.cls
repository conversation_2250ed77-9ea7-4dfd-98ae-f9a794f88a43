public with sharing class RepaymentService {
	public static void runDirectDebitReturnReconciliation(
		Bank_Account_Statement_Transaction__c bankTrans
	) {
		String query =
			'SELECT Id, Amount__c, Payment_Amount__c, Reconciled_Amount__c, Bond__c, Repayment_Type__c ' +
			'FROM Repayment__c ' +
			'WHERE Status__c = :status ' +
			'AND Repayment_Method__c = :method ' +
			'AND Original_Payment__c != NULL ' +
			'AND Original_Payment__r.Status__c = :paymentStatus ' +
			'AND Bank_Account_Statement_Transaction__c = NULL ' +
			'AND Original_Payment__r.Westpac_DERPs_File_Name__c != NULL ' +
			'AND Original_Payment__r.Westpac_DERPs_Generated_Date__c = :date ';

		Map<String, Object> bindVariables = new Map<String, Object>{
			'status' => Constants.REPAYMENT_STATUS_SUCCESSFUL,
			'method' => Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			'paymentStatus' => Constants.PAYMENT_STATUS_RETURNED,
			'date' => bankTrans.Bank_Statement_Date__c
		};

		List<Repayment__c> directDebitReturns = Database.queryWithBinds(
			query,
			bindVariables,
			AccessLevel.SYSTEM_MODE
		);

		repaymentsRecon(bankTrans, directDebitReturns);
	}

	public static void runIntDCRecon(
		Bank_Account_Statement_Transaction__c bankTrans
	) {
		String query =
			'SELECT Id, Amount__c, Reconciled_Amount__c, Bond__c, Repayment_Type__c ' +
			'FROM Repayment__c ' +
			'WHERE Status__c = :status ' +
			'AND Repayment_Reference_Number__c = :referenceNumber ' +
			'AND Repayment_Method__c = :method ' +
			'AND Bank_Account_Statement_Transaction__c = NULL';

		Map<String, Object> bindVariables = new Map<String, Object>{
			'status' => Constants.REPAYMENT_STATUS_SUCCESSFUL,
			'method' => Constants.REPAYMENT_METHOD_INTERNATIONAL_DIRECT_CREDIT,
			'referenceNumber' => bankTrans.End_To_End_Identification__c
		};

		List<Repayment__c> intDCRepayments = Database.queryWithBinds(
			query,
			bindVariables,
			AccessLevel.SYSTEM_MODE
		);

		repaymentsRecon(bankTrans, intDCRepayments);
	}

	public static void runAusDCRecon(
		Bank_Account_Statement_Transaction__c bankTrans
	) {
		String query =
			'SELECT Id, Amount__c, Reconciled_Amount__c, Bond__c, Repayment_Type__c ' +
			'FROM Repayment__c ' +
			'WHERE Status__c = :status ' +
			'AND Repayment_Reference_Number__c = :referenceNumber ' +
			'AND Repayment_Method__c = :method ' +
			'AND Bank_Account_Statement_Transaction__c = NULL';

		Map<String, Object> bindVariables = new Map<String, Object>{
			'status' => Constants.REPAYMENT_STATUS_SUCCESSFUL,
			'method' => Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			'referenceNumber' => bankTrans.End_To_End_Identification__c
		};

		List<Repayment__c> ausDCRepayments = Database.queryWithBinds(
			query,
			bindVariables,
			AccessLevel.SYSTEM_MODE
		);
		repaymentsRecon(bankTrans, ausDCRepayments);
	}

	public static void runAusDCReturnRecon(
		Bank_Account_Statement_Transaction__c bankTrans
	) {
		String query =
			'SELECT Id, Amount__c, Reconciled_Amount__c, Bond__c, Repayment_Type__c ' +
			'FROM Repayment__c ' +
			'WHERE Status__c = :status ' +
			'AND Repayment_Method__c = :method ' +
			'AND Repayment_Reference_Number__c = :referenceNumber ' + // find by end to end id
			'AND Bank_Account_Statement_Transaction__c = NULL';

		Map<String, Object> bindVariables = new Map<String, Object>{
			'status' => Constants.REPAYMENT_STATUS_SUCCESSFUL,
			'method' => Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			'referenceNumber' => bankTrans.End_To_End_Identification__c
		};

		List<Repayment__c> ausDCReturnRepayments = Database.queryWithBinds(
			query,
			bindVariables,
			AccessLevel.SYSTEM_MODE
		);

		repaymentsRecon(bankTrans, ausDCReturnRepayments);

		// setting status and running the corresponding flow trigger to create Payment and send notifications
		for (Repayment__c repayment : ausDCReturnRepayments) {
			repayment.Status__c = Constants.REPAYMENT_STATUS_RETURNED;
		}
		update ausDCReturnRepayments;
	}

	public static void repaymentsRecon(
		Bank_Account_Statement_Transaction__c bankTrans,
		List<Repayment__c> inputRepayments
	) {
		try {
			Decimal totalReconciledAmount = 0;
			Set<Id> bondIds = new Set<Id>();
			List<Repayment__c> repayments = new List<Repayment__c>();

			Logger.info(
				'Repayments to reconcile: ' + inputRepayments,
				inputRepayments
			);

			for (Repayment__c repayment : inputRepayments) {
				if (repayment.Repayment_Type__c == 'Bulk Repayment') {
					List<Repayment__c> childRepayments = getChildRepayments(
						repayment.Id
					);
					repayments.addAll(childRepayments);
				}
				repayments.add(repayment);
			}

			for (Repayment__c repayment : repayments) {
				if (repayment.Repayment_Type__c == 'Bulk Repayment') {
					repayment.Reconciled_Amount__c = 0;
				} else {
					repayment.Reconciled_Amount__c = repayment.Amount__c * -1 ??
						0;
				}
				repayment.Bank_Account_Statement_Transaction__c = bankTrans.Id;
				totalReconciledAmount += repayment.Reconciled_Amount__c;
				bondIds.add(repayment.Bond__c);
			}

			Decimal amountDifference =
				bankTrans.Reconciliation_Difference__c - totalReconciledAmount;

			if (amountDifference < 0) {
				CasesService.createCaseForReconciliationError(
					amountDifference,
					bankTrans.Id
				);
			} else if (amountDifference > 0) {
				for (Repayment__c repayment : repayments) {
					repayment.Reconciled_Amount__c = 0;
					repayment.Bank_Account_Statement_Transaction__c = null;
				}

				if (bankTrans.Reconciliation_Difference__c != 0) {
					CasesService.createCaseForReconciliationError(
						bankTrans.Transaction_Amount_Balance_Impact__c,
						bankTrans.Id
					);
				}
			}

			Logger.info('Repayments to update: ' + repayments, repayments);
			Logger.info(
				'Bank Account Statement Transaction to update: ' + bankTrans,
				bankTrans
			);

			TriggerHandler.bypass('RepaymentTriggerHandler');
			update repayments;
			TriggerHandler.clearbypass('RepaymentTriggerHandler');

			BondController.updateReconciledAmount(bondIds);
			BankAccountStmtTransactionController.updateReconciledAmount(
				new Set<Id>{ bankTrans.Id }
			);
		} catch (Exception e) {
			Logger.error('Error running reconciliation: ' + e, e);
		}
	}

	private static List<Repayment__c> getChildRepayments(Id parentRepaymentId) {
		return [
			SELECT
				Id,
				Amount__c,
				Reconciled_Amount__c,
				Bond__c,
				Repayment_Type__c
			FROM Repayment__c
			WHERE
				Parent_Bulk_Repayment__c = :parentRepaymentId
				AND Bank_Account_Statement_Transaction__c = NULL
		];
	}
}