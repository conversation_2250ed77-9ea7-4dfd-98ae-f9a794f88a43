<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Repayment__c.Incorrect_Repayment</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Incorrect_Repayment}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Repayment__c.Amend_repayment_amount</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Reconciliation}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Reference_Number__c</fieldItem>
                <identifier>RecordRepayment_Reference_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payee_Type__c</fieldItem>
                <identifier>RecordPayee_Type_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payee__c</fieldItem>
                <identifier>RecordPayee_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Transaction__c</fieldItem>
                <identifier>RecordTransaction_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Amount__c</fieldItem>
                <identifier>RecordAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Method__c</fieldItem>
                <identifier>RecordRepayment_Method_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BSB__c</fieldItem>
                <identifier>RecordBSB_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Account_Number__c</fieldItem>
                <identifier>RecordAccount_Number_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bank_Account_Name__c</fieldItem>
                <identifier>RecordBank_Account_Name_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Number__c</fieldItem>
                <identifier>RecordCheque_Number_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Issue_Date__c</fieldItem>
                <identifier>RecordCheque_Issue_Date_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Present_Date__c</fieldItem>
                <identifier>RecordCheque_Present_Date_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Original_Payment__c</fieldItem>
                <identifier>RecordOriginal_Payment__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Settlement_Date__c</fieldItem>
                <identifier>RecordSettlement_Date__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Receipt_Number__c</fieldItem>
                <identifier>RecordReceipt_Number__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Amount__c</fieldItem>
                <identifier>RecordPayment_Amount__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Refund_Surcharge_Amount__c</fieldItem>
                <identifier>RecordRefund_Surcharge_Amount__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Response_Description__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Response_Description__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Summary_Code__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Summary_Code__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Response_Code__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Response_Code__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Card_Scheme__c</fieldItem>
                <identifier>RecordCard_Scheme__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuickStream_Created_Date_Time__c</fieldItem>
                <identifier>RecordQuickStream_Created_Date_Time__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CAF_File_Name__c</fieldItem>
                <identifier>RecordCAF_File_Name__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CAF_File_Date__c</fieldItem>
                <identifier>RecordCAF_File_Date__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Type__c</fieldItem>
                <identifier>RecordRepayment_Type__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Repayment_Reference__c</fieldItem>
                <identifier>RecordInternational_Bank_Repayment_Reference__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Name__c</fieldItem>
                <identifier>RecordInternational_Bank_Name__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Country__c</fieldItem>
                <identifier>RecordInternational_Bank_Country__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Unstructured_Remittance_Info__c</fieldItem>
                <identifier>RecordUnstructured_Remittance_Info__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SWIFT_Code__c</fieldItem>
                <identifier>RecordSWIFT_Code_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IBAN__c</fieldItem>
                <identifier>RecordIBAN_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d72313a9-1dbd-4761-a4cd-2983913165b5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status__c</fieldItem>
                <identifier>RecordStatus_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.Edit_Repayment_Status}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status__c</fieldItem>
                <identifier>RecordStatus_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.Edit_Repayment_Status}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Status_Date_Time__c</fieldItem>
                <identifier>RecordRepayment_Status_Date_Time_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Status__c</fieldItem>
                <identifier>RecordCheque_Status_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Status_Date_Time__c</fieldItem>
                <identifier>RecordCheque_Status_Date_Time_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Forwarding_Address__c</fieldItem>
                <identifier>RecordForwarding_Address__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Comments__c</fieldItem>
                <identifier>RecordComments_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Parent_Repayment__c</fieldItem>
                <identifier>RecordParent_Repayment__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Attempt_Number__c</fieldItem>
                <identifier>RecordRepayment_Attempt_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment_Reason__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_Reason_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Incorrect_Repayment__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment_Fee_Waived__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_Fee_Waived_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Incorrect_Repayment__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Partial_Bond_Refund_Reason__c</fieldItem>
                <identifier>RecordPartial_Bond_Refund_Reason_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Building_Number__c</fieldItem>
                <identifier>RecordInternational_Address_Building_Number__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Street__c</fieldItem>
                <identifier>RecordInternational_Address_Street__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Post_Code__c</fieldItem>
                <identifier>RecordInternational_Address_Post_Code__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_City__c</fieldItem>
                <identifier>RecordInternational_Address_City__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Country__c</fieldItem>
                <identifier>RecordInternational_Address_Country__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-137f2f3a-01ea-45b3-a209-7afb105f71f4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d72313a9-1dbd-4761-a4cd-2983913165b5</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-137f2f3a-01ea-45b3-a209-7afb105f71f4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ae529d50-ffd1-4f60-892c-27a604c70364</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-ae529d50-ffd1-4f60-892c-27a604c70364</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>@@@SFDCInformationSFDC@@@</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>force:recordDetailPanelMobile</componentName>
                <identifier>force_recordDetailPanelMobile</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-a0691c31-8ef1-441c-8ed6-2064efca5416</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Repayment__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Histories</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e317deda-bcc0-4baf-acd1-596d75ad0fe7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>NewCase</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Repayment__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Cases__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.CASE_NUMBER</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.SUBJECT</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.PRIORITY</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.STATUS</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.CREATED_DATE</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Cases</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>CASES.CREATED_DATE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b3dd6688-f847-4fed-b0c0-c0162db8e6bc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a0691c31-8ef1-441c-8ed6-2064efca5416</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e317deda-bcc0-4baf-acd1-596d75ad0fe7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Change History</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b3dd6688-f847-4fed-b0c0-c0162db8e6bc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.cases</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9d3fe2fb-b92b-465f-990a-19cbd8ea17bb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-9d3fe2fb-b92b-465f-990a-19cbd8ea17bb</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Repayment Record Page</masterLabel>
    <sobjectType>Repayment__c</sobjectType>
    <template>
        <name>flexipage:recordHomeSimpleViewTemplate</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
