<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Repayment__c.Incorrect_Repayment</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Incorrect_Repayment}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Reference_Number__c</fieldItem>
                <identifier>RecordRepayment_Reference_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payee_Type__c</fieldItem>
                <identifier>RecordPayee_Type_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payee__c</fieldItem>
                <identifier>RecordPayee_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Transaction__c</fieldItem>
                <identifier>RecordTransaction_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Amount__c</fieldItem>
                <identifier>RecordAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Method__c</fieldItem>
                <identifier>RecordRepayment_Method_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Type__c</fieldItem>
                <identifier>RecordRepayment_Type__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Partial_Bond_Refund_Reason__c</fieldItem>
                <identifier>RecordPartial_Bond_Refund_Reason_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d72313a9-1dbd-4761-a4cd-2983913165b5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status__c</fieldItem>
                <identifier>RecordStatus_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Status_Date_Time__c</fieldItem>
                <identifier>RecordRepayment_Status_Date_Time_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Comments__c</fieldItem>
                <identifier>RecordComments_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Parent_Repayment__c</fieldItem>
                <identifier>RecordParent_Repayment__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Attempt_Number__c</fieldItem>
                <identifier>RecordRepayment_Attempt_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment_Reason__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_Reason_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Incorrect_Repayment__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment_Fee_Waived__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_Fee_Waived_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Incorrect_Repayment__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-137f2f3a-01ea-45b3-a209-7afb105f71f4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d72313a9-1dbd-4761-a4cd-2983913165b5</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-137f2f3a-01ea-45b3-a209-7afb105f71f4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ae529d50-ffd1-4f60-892c-27a604c70364</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Forwarding_Address__c</fieldItem>
                <identifier>RecordForwarding_Address__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a33c0451-e9d0-46ba-a27d-eef3cb27ca03</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Building_Number__c</fieldItem>
                <identifier>RecordInternational_Address_Building_Number__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Street__c</fieldItem>
                <identifier>RecordInternational_Address_Street__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Post_Code__c</fieldItem>
                <identifier>RecordInternational_Address_Post_Code__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_City__c</fieldItem>
                <identifier>RecordInternational_Address_City__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Country__c</fieldItem>
                <identifier>RecordInternational_Address_Country__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c653c2e8-d876-4c77-b900-4cd1a4ccb433</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a33c0451-e9d0-46ba-a27d-eef3cb27ca03</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column15</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c653c2e8-d876-4c77-b900-4cd1a4ccb433</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column16</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-d0c69ff2-5028-4bac-bf83-ae5d78b1d663</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Bank_Reference__c</fieldItem>
                <identifier>RecordRepayment_Bank_Reference_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bank_Account_Name__c</fieldItem>
                <identifier>RecordBank_Account_Name_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Account_Number__c</fieldItem>
                <identifier>RecordAccount_Number_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BSB__c</fieldItem>
                <identifier>RecordBSB_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-1dc9b3a6-455b-4ed0-b1f3-7f50463c33a7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Repayment_Reference__c</fieldItem>
                <identifier>RecordInternational_Bank_Repayment_Reference__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Name__c</fieldItem>
                <identifier>RecordInternational_Bank_Name__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IBAN__c</fieldItem>
                <identifier>RecordIBAN_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SWIFT_Code__c</fieldItem>
                <identifier>RecordSWIFT_Code_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Unstructured_Remittance_Info__c</fieldItem>
                <identifier>RecordUnstructured_Remittance_Info__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Country__c</fieldItem>
                <identifier>RecordInternational_Bank_Country__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d03cb502-776d-4544-9439-b4b40b034819</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1dc9b3a6-455b-4ed0-b1f3-7f50463c33a7</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d03cb502-776d-4544-9439-b4b40b034819</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-75177734-1275-4195-bcbb-de664dcabbfc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Present_Date__c</fieldItem>
                <identifier>RecordCheque_Present_Date_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Issue_Date__c</fieldItem>
                <identifier>RecordCheque_Issue_Date_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-bbba11de-bc3b-4b41-b674-f27b93752762</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Number__c</fieldItem>
                <identifier>RecordCheque_Number_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Status__c</fieldItem>
                <identifier>RecordCheque_Status_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Status_Date_Time__c</fieldItem>
                <identifier>RecordCheque_Status_Date_Time_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-0bf9d0a7-1fc6-4a10-b011-e0375f3c9e48</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-bbba11de-bc3b-4b41-b674-f27b93752762</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-0bf9d0a7-1fc6-4a10-b011-e0375f3c9e48</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column12</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-374f66d2-5bc8-4bbe-aa62-6f268ef6f96d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Original_Payment__c</fieldItem>
                <identifier>RecordOriginal_Payment__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Amount__c</fieldItem>
                <identifier>RecordPayment_Amount__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-fd52ad97-89eb-4ac2-8528-893d3ffba8d6</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Receipt_Number__c</fieldItem>
                <identifier>RecordReceipt_Number__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Settlement_Date__c</fieldItem>
                <identifier>RecordSettlement_Date__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b6f582c4-beb9-42ea-9ed1-d06808ea1d96</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fd52ad97-89eb-4ac2-8528-893d3ffba8d6</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b6f582c4-beb9-42ea-9ed1-d06808ea1d96</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column14</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-00a5a859-70cd-46e5-a633-c6ba364c298c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CAF_File_Name__c</fieldItem>
                <identifier>RecordCAF_File_Name__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CAF_File_Date__c</fieldItem>
                <identifier>RecordCAF_File_Date__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Refund_Surcharge_Amount__c</fieldItem>
                <identifier>RecordRefund_Surcharge_Amount__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Card_Scheme__c</fieldItem>
                <identifier>RecordCard_Scheme__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-e95fed78-0791-4c4f-8f12-dd182652bd29</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuickStream_Created_Date_Time__c</fieldItem>
                <identifier>RecordQuickStream_Created_Date_Time__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Summary_Code__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Summary_Code__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Response_Code__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Response_Code__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Response_Description__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Response_Description__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c360f02c-bed8-44ea-9d10-a913809bb63f</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e95fed78-0791-4c4f-8f12-dd182652bd29</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c360f02c-bed8-44ea-9d10-a913809bb63f</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-2f4e5837-bc0c-4cc6-be78-937efae155cb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Parent_Bulk_Repayment__c</fieldItem>
                <identifier>RecordParent_Bulk_Repayment_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.UCM_Run_Status__c</fieldItem>
                <identifier>RecordUCM_Run_Status_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-fbe9514e-9ca6-452b-b82a-0ca91d074e89</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Unclaimed_Money_Repayment__c</fieldItem>
                <identifier>RecordUnclaimed_Money_Repayment_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-069fb5b3-d408-4646-ba88-892555084bb0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fbe9514e-9ca6-452b-b82a-0ca91d074e89</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-069fb5b3-d408-4646-ba88-892555084bb0</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-3a98deab-7193-4640-a70c-4a1e01b5c6fc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Address_Line_3__c</fieldItem>
                <identifier>RecordAddress_Line_3_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Reasonable_expenses__c</fieldItem>
                <identifier>RecordReasonable_expenses_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-47386179-ad59-4baf-b5bb-2748022d1168</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Description__c</fieldItem>
                <identifier>RecordDescription_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2a44a1ab-913f-40a2-af37-7cb8b0aa819b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-47386179-ad59-4baf-b5bb-2748022d1168</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2a44a1ab-913f-40a2-af37-7cb8b0aa819b</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-6200cc81-c4c5-41e5-a24d-43d5addacfba</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-ae529d50-ffd1-4f60-892c-27a604c70364</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>@@@SFDCInformationSFDC@@@</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-d0c69ff2-5028-4bac-bf83-ae5d78b1d663</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Address Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-75177734-1275-4195-bcbb-de664dcabbfc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Payment Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-374f66d2-5bc8-4bbe-aa62-6f268ef6f96d</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Cheque Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-00a5a859-70cd-46e5-a633-c6ba364c298c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Credit Card Refund Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-2f4e5837-bc0c-4cc6-be78-937efae155cb</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Integration Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-3a98deab-7193-4640-a70c-4a1e01b5c6fc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Unclaimed Money</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-6200cc81-c4c5-41e5-a24d-43d5addacfba</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>UCM Report Fileds</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>force:recordDetailPanelMobile</componentName>
                <identifier>force_recordDetailPanelMobile</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-a0691c31-8ef1-441c-8ed6-2064efca5416</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Repayment__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Child_Repayments_Bulk__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>Bond__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payee__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Amount__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Repayments (Parent Bulk Repayment)</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>Amount__c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-12abde5f-a1b0-49b4-aaca-0beb35faa051</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Repayment__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Histories</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e317deda-bcc0-4baf-acd1-596d75ad0fe7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>NewCase</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Repayment__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Cases__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.CASE_NUMBER</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.SUBJECT</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.PRIORITY</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.STATUS</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.CREATED_DATE</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Cases</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>CASES.CREATED_DATE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b3dd6688-f847-4fed-b0c0-c0162db8e6bc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a0691c31-8ef1-441c-8ed6-2064efca5416</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-12abde5f-a1b0-49b4-aaca-0beb35faa051</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Child Repayments</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e317deda-bcc0-4baf-acd1-596d75ad0fe7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Change History</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b3dd6688-f847-4fed-b0c0-c0162db8e6bc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.cases</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9d3fe2fb-b92b-465f-990a-19cbd8ea17bb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-9d3fe2fb-b92b-465f-990a-19cbd8ea17bb</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>UCM Repayment Record Page</masterLabel>
    <sobjectType>Repayment__c</sobjectType>
    <template>
        <name>flexipage:recordHomeSimpleViewTemplate</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
