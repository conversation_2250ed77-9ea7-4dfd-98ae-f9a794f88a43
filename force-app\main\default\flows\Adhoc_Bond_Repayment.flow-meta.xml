<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <description>Logs Error to the Bond Record.</description>
        <name>Log_Error_To_Bond</name>
        <label>Log Error To Bond</label>
        <locationX>578</locationX>
        <locationY>1106</locationY>
        <actionName>FlowRecordLogEntry</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Repayment_Record_Creation_Fail_Screen</targetReference>
        </connector>
        <dataTypeMappings>
            <typeName>T__record</typeName>
            <typeValue>Bond__c</typeValue>
        </dataTypeMappings>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>flowName</name>
            <value>
                <stringValue>Adho<PERSON>_<PERSON>_Repayment</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>message</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>record</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>saveLog</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>FlowRecordLogEntry</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Assigns Repayment Amount to the Repayment Record.</description>
        <name>Assign_Repayment_Amount</name>
        <label>Assign Repayment Amount</label>
        <locationX>446</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>repaymentRecord.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Repayment_Amount</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Repayment_Amount_Less_Than_Bond_Amounts</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Creates default values for the Repayment Record.</description>
        <name>Create_Repayment_Record</name>
        <label>Create Repayment Record</label>
        <locationX>446</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>repaymentRecord.Bond__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Payee__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payeeAccountId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Payee_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>BondPartiesDataTable.firstSelectedRow.Role__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Repayment_Method__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Direct Credit</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Repayment_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Bond Repayment</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Repayment_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>todaysDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Payer__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>RTBA</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentRecord.Partial_Bond_Refund_Reason__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PartialBondRefundReasonPicklist</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Confirm_Details_Screen_Display</targetReference>
        </connector>
    </assignments>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <description>Repayment Amount must be LESS THAN both Bond Current Amount and Bond Reconciled Amount.</description>
        <name>Check_Repayment_Amount_Less_Than_Bond_Amounts</name>
        <label>Check Repayment Amount Less Than Bond Amounts</label>
        <locationX>446</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Repayment_Amount_To_Large_Error_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Repayment_Amount_Less_Than_Bond_Amounts</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>repaymentRecord.Amount__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>recordId.Current_Bond_Amount__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>repaymentRecord.Amount__c</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <elementReference>recordId.Reconciled_Amount__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Repayment_Record_DML</targetReference>
            </connector>
            <label>Repayment Amount Less Than Bond Amounts</label>
        </rules>
    </decisions>
    <description>This Screen Flow walks a User through creating a Repayment to a Bond Party. It bypassing the normal Claims process (which closes the Bond) to support special circumstances where monies from the Bond need to be transferred to Bond Party. An example if duplicate BPAY Payments.

Does not support International Payments.

Does not support Repayments to RBIIA or SRO.</description>
    <dynamicChoiceSets>
        <name>partialBondRefundReason</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Partial_Bond_Refund_Reason__c</picklistField>
        <picklistObject>Repayment__c</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>newCurrentBondAmount</name>
        <dataType>Currency</dataType>
        <expression>{!recordId.Current_Bond_Amount__c} - {!repaymentRecord.Amount__c}</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>payeeAccountId</name>
        <dataType>String</dataType>
        <expression>IF(
ISPICKVAL({!BondPartiesDataTable.firstSelectedRow.Role__c}, &quot;Renter&quot;), 
{!BondPartiesDataTable.firstSelectedRow.Renter__r.AccountId},
IF(ISPICKVAL({!BondPartiesDataTable.firstSelectedRow.Role__c}, &quot;Rental Provider&quot;),{!BondPartiesDataTable.firstSelectedRow.Rental_Provider__c},
&apos;&apos;
)
)</expression>
    </formulas>
    <formulas>
        <name>todaysDate</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Adhoc Bond Repayment {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Adhoc Bond Repayment</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Creates the Repayment Record.</description>
        <name>Create_Repayment_Record_DML</name>
        <label>Create Repayment Record</label>
        <locationX>50</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Update_Bond_Current_Amount</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Roll_Back_DML_Statements</targetReference>
        </faultConnector>
        <inputReference>repaymentRecord</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Gets Bond Parties that are active against this Bond.</description>
        <name>Get_Bond_Parties</name>
        <label>Get Bond Parties</label>
        <locationX>446</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Choose_Bond_Party_Screen_Display</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Bond_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordRollbacks>
        <description>Rolls Back Prior DML Statements</description>
        <name>Roll_Back_DML_Statements</name>
        <label>Roll Back DML Statements</label>
        <locationX>578</locationX>
        <locationY>998</locationY>
        <connector>
            <targetReference>Log_Error_To_Bond</targetReference>
        </connector>
    </recordRollbacks>
    <recordUpdates>
        <description>Updates Bond Current Amount to be minus the Repayment Amount.</description>
        <name>Update_Bond_Current_Amount</name>
        <label>Update Bond Current Amount</label>
        <locationX>50</locationX>
        <locationY>998</locationY>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Roll_Back_DML_Statements</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Current_Bond_Amount__c</field>
            <value>
                <elementReference>newCurrentBondAmount</elementReference>
            </value>
        </inputAssignments>
        <object>Bond__c</object>
    </recordUpdates>
    <screens>
        <description>Contains a Data Table to choose a Bond Party to make a Repayment for.</description>
        <name>Choose_Bond_Party_Screen_Display</name>
        <label>Choose Bond Party Screen Display</label>
        <locationX>446</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Create_Repayment_Record</targetReference>
        </connector>
        <fields>
            <name>ChooseBondPartyDisplayText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;Please choose a Bond Party to make a Repayment for.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>BondPartiesDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Bond_Party__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Bond Parties</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Bond_Parties</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-aba6&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Bond Party Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Role__c&quot;,&quot;guid&quot;:&quot;column-69b8&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Role&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;RTBA_Registered_Name__c&quot;,&quot;guid&quot;:&quot;column-ce9b&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;RTBA Registered Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Renter_Name__c&quot;,&quot;guid&quot;:&quot;column-642a&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Renter Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Email_Address__c&quot;,&quot;guid&quot;:&quot;column-8051&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Email Address&quot;,&quot;type&quot;:&quot;email&quot;},{&quot;apiName&quot;:&quot;Mobile_Number__c&quot;,&quot;guid&quot;:&quot;column-d856&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Mobile Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Comments__c&quot;,&quot;guid&quot;:&quot;column-7cb5&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:6,&quot;label&quot;:&quot;Comments&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Shows Bond Details, Repayment Details, and enables the User to enter Domestic Banking Details and Repayment Amount.</description>
        <name>Confirm_Details_Screen_Display</name>
        <label>Confirm Details Screen Display</label>
        <locationX>446</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Assign_Repayment_Amount</targetReference>
        </connector>
        <fields>
            <name>ConfirmDetailsDisplayText</name>
            <fieldText>&lt;p&gt;Please ensure that Repayment Amount is LESS THAN both the Bond Current Amount and Bond Reconciled Amount.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;For full refunds please use the Claims process.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>BondDetails2</name>
            <fieldText>Bond Details</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>BondDetails2_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>BondDetailsLeftDisplay2</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Number:&lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt; {!recordId.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Status: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;{!recordId.Status__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>BondDetails2_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>BondDetailsRightDisplay2</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Current Amount: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;{!recordId.Current_Bond_Amount__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Reconciled Amount: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;{!recordId.Reconciled_Amount__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Repayment_Details</name>
            <fieldText>Repayment Details</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Repayment_Details_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>RepaymentDetailsDisplayText</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Bond: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;{!repaymentRecord.Bond__r.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Status: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;{!repaymentRecord.Status__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Repayment Type:&lt;/strong&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt; {!repaymentRecord.Repayment_Type__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Repayment Method:&lt;/strong&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt; {!repaymentRecord.Repayment_Method__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Payee Type:&lt;/strong&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt; {!repaymentRecord.Payee_Type__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Payee:&lt;/strong&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt; {!repaymentRecord.Payee__r.Account_Name_Formula__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt;Partial Refund Reason:&lt;/strong&gt;&lt;span style=&quot;font-size: 18px; color: rgb(0, 0, 0);&quot;&gt; {!repaymentRecord.Partial_Bond_Refund_Reason__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Repayment_Details_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>repaymentRecord.Bank_Account_Name__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>repaymentRecord.BSB__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>repaymentRecord.Account_Number__c</objectFieldReference>
                </fields>
                <fields>
                    <name>Repayment_Amount</name>
                    <dataType>Currency</dataType>
                    <fieldText>Repayment Amount</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <scale>0</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Initial display screen to show Bond Details, intro text, and choose the Reason for Adhoc Repayments.</description>
        <name>Initial_Display_Screen</name>
        <label>Initial Display Screen</label>
        <locationX>446</locationX>
        <locationY>134</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Get_Bond_Parties</targetReference>
        </connector>
        <fields>
            <name>BondDetails</name>
            <fieldText>Bond Details</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>BondDetails_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>BondDetailsLeftDisplay</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Number:&lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt; {!recordId.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Status: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;{!recordId.Status__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>BondDetails_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>BondDetailsRightDisplay</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Current Amount: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;{!recordId.Current_Bond_Amount__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Bond Reconciled Amount: &lt;/strong&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;{!recordId.Reconciled_Amount__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>InitialDisplayMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;Hello! Welcome to the Adboc Bond Repayment Screen Flow where it is possible to create Repayments to Partial Refund a Bond.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;For Full Bond Refunds please use the Claims Process.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;You will be able to select the Reason for this Partial Bond Refund, then which Bond Party to transfer the funds to, and finalise the Repayment record.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;em style=&quot;font-size: 18px;&quot;&gt;Please note that International Direct Credits are not supported for Adhoc Bond Repayments.&lt;/em&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 18px;&quot;&gt;Please choose a Reason for this Partial Refund below:&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>PartialBondRefundReasonPicklist</name>
            <choiceReferences>partialBondRefundReason</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Partial Bond Refund Reason</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Displays error message text.</description>
        <name>Repayment_Amount_To_Large_Error_Screen</name>
        <label>Repayment Amount To Large Error Screen</label>
        <locationX>842</locationX>
        <locationY>890</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Confirm_Details_Screen_Display</targetReference>
        </connector>
        <fields>
            <name>RepaymentAmountTooLargeErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 18px;&quot;&gt;Repayment Amount is too large. Please reduce below both the Bond Current Amount and Bond Reconciled Amount.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Shows error message and loops back to enable User to change Repayment Details.</description>
        <name>Repayment_Record_Creation_Fail_Screen</name>
        <label>Repayment Record Creation Fail Screen</label>
        <locationX>578</locationX>
        <locationY>1214</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Confirm_Details_Screen_Display</targetReference>
        </connector>
        <fields>
            <name>RepaymentRecordCreationFailMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 18px;&quot;&gt;Oops! Something went wrong. Please let your Salesforce administrator know!&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0); font-size: 18px;&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Shows a success message.</description>
        <name>Success_Screen</name>
        <label>Success Screen</label>
        <locationX>50</locationX>
        <locationY>1106</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>SuccessMessageText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(79, 176, 1); font-size: 18px;&quot;&gt;Congrats! Repayment created and Bond updated successfully!&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Initial_Display_Screen</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond__c</objectType>
    </variables>
    <variables>
        <name>repaymentRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Repayment__c</objectType>
    </variables>
</Flow>
