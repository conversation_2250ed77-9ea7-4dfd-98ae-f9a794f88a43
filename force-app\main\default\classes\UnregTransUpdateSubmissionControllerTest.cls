@IsTest
private class UnregTransUpdateSubmissionControllerTest {
	@TestSetup
	static void setup() {
		Account rp = TestDataFactory.createRentalProviderAccount();
		rp.Registered_Online__c = true;
		insert rp;

		Contact rpc = TestDataFactory.createContact(rp);
		rpc.Email = '<EMAIL>';
		insert rpc;

		User rpUser = TestDataFactory.createRentalProviderUser(rpc);
		insert rpUser;

		//insert IBL transaction
		Transaction__c tx = TestDataFactory.createTransaction(rp);
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		insert tx;

		Bond__c createdBond = TestDataFactory.createBond(tx);
		createdBond.Bond_Paid_By__c = Constants.TRANSACTION_BOND_PAID_BY_RENTAL_PROVIDER;
		insert new List<Bond__c>{ createdBond };

		//insert claim tx
		Transaction__c txClaim = TestDataFactory.createTransaction(rp);
		txClaim.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_CLAIM;
		txClaim.Status__c = Constants.TRANSACTION_STATUS_PENDING_RESPONSE;
		txClaim.Role_Initiating_Claim__c = Constants.TRANSACTION_ROLE_INITIATING_CLAIM_BY_RENTER;
		txClaim.Bond__c = createdBond.Id;
		txClaim.Repayment_Basis__c = Constants.TRANSACTION_REPAYMENT_WITHOUT_CONSENT;
		txClaim.Type__c = Constants.TRANSACTION_TYPE_CLAIMS;
		insert txClaim;

		Transaction_Party__c tpRenter = TestDataFactory.createTransactionParty(
			txClaim
		);
		tpRenter.Is_Claim_Initiator__c = true;
		Transaction_Party__c tpRenter2 = TestDataFactory.createTransactionParty(
			txClaim,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		tpRenter2.Claim_Pay_Amount__c = 0;
		Transaction_Party__c tpRP = TestDataFactory.createTransactionParty(
			txClaim
		);
		tpRP.Role__c = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		tpRP.Rental_Provider__c = rp.Id;
		insert new List<Transaction_Party__c>{ tpRenter, tpRP, tpRenter2 };

		TestDataFactory.assignSiteGuestUserPermissions();
	}

	@IsTest
	static void testBehaviorAccept0Success() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			UnregTransUpdateSubmissionController.acceptClaim(token);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_AGREED,
			tpAfter.Party_Status__c,
			'Should be accepted'
		);
	}

	@IsTest
	static void testBehaviorAcceptNonZeroSuccessAU() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		tp.Claim_Pay_Amount__c = 2.0;
		update tp;

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		//setup banking details
		UnregTransUpdateSubmissionController.BankingDetails bankingDetails = new UnregTransUpdateSubmissionController.BankingDetails();
		UnregTransUpdateSubmissionController.ForwardingAddress address = bankingDetails.forwardingAddress = new UnregTransUpdateSubmissionController.ForwardingAddress();
		address.additionalStreetDetails = 'Unit 1';
		address.country = 'AUSTRALIA';
		address.postcode = '3000';
		address.state = 'VIC';
		address.street = '1 Elizabeth Street';
		address.suburb = 'Melbourne';
		bankingDetails.repaymentMethod = Constants.REPAYMENT_METHOD_DIRECT_CREDIT;
		bankingDetails.bankBSB = '123456';
		bankingDetails.accountName = 'Test';
		bankingDetails.bankAccountNumber = '*********';

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			UnregTransUpdateSubmissionController.acceptClaimWithBanking(
				token,
				bankingDetails
			);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT
				Party_Status__c,
				Claim_Repayment_Method__c,
				Claim_Repayment_Account_Number__c,
				Claim_Repayment_BSB__c,
				Claim_Repayment_Bank_Account_Name__c,
				Claim_Repayment_IBAN__c,
				Claim_Repayment_SWIFT_Code__c,
				Forwarding_Additional_Address_Details__c,
				Forwarding_Street_Address__c,
				Forwarding_Address_Suburb__c,
				Forwarding_Address_State__c,
				Forwarding_Address_Postcode__c,
				Forwarding_Address_Country__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_AGREED,
			tpAfter.Party_Status__c,
			'Should be accepted'
		);
		Assert.areEqual(
			Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			tpAfter.Claim_Repayment_Method__c,
			'Expected repaymentMethod set to direct credit'
		);
		Assert.areEqual(
			bankingDetails.accountName,
			tpAfter.Claim_Repayment_Bank_Account_Name__c,
			'Expected account name set on options'
		);
		Assert.areEqual(
			bankingDetails.bankAccountNumber,
			tpAfter.Claim_Repayment_Account_Number__c,
			'Expected bankAccountNumber set on options'
		);
		Assert.areEqual(
			bankingDetails.bankBSB,
			tpAfter.Claim_Repayment_BSB__c,
			'Expected bankBSB set on options'
		);
		Assert.areEqual(
			bankingDetails.repaymentMethod,
			tpAfter.Claim_Repayment_Method__c,
			'Expected repaymentMethod set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.additionalStreetDetails,
			tpAfter.Forwarding_Additional_Address_Details__c,
			'Expected additionalStreetDetails set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.street,
			tpAfter.Forwarding_Street_Address__c,
			'Expected country set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.suburb,
			tpAfter.Forwarding_Address_Suburb__c,
			'Expected postcode set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.state,
			tpAfter.Forwarding_Address_State__c,
			'Expected state set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.postcode,
			tpAfter.Forwarding_Address_Postcode__c,
			'Expected street set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.country,
			tpAfter.Forwarding_Address_Country__c,
			'Expected suburb set on options'
		);
		Assert.isNull(
			tpAfter.Claim_Repayment_SWIFT_Code__c,
			'Expected blank swift'
		);
		Assert.isNull(tpAfter.Claim_Repayment_IBAN__c, 'Expected blank iban');
	}

	@IsTest
	static void testBehaviorAcceptNonZeroSuccessIntl() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		tp.Claim_Pay_Amount__c = 2.0;
		update tp;

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		//setup banking details
		UnregTransUpdateSubmissionController.BankingDetails bankingDetails = new UnregTransUpdateSubmissionController.BankingDetails();
		UnregTransUpdateSubmissionController.ForwardingAddress address = bankingDetails.forwardingAddress = new UnregTransUpdateSubmissionController.ForwardingAddress();
		address.additionalStreetDetails = 'Unit 1';
		address.country = 'AUSTRALIA';
		address.postcode = '3000';
		address.state = 'VIC';
		address.street = '1 Elizabeth Street';
		address.suburb = 'Melbourne';
		bankingDetails.repaymentMethod = Constants.REPAYMENT_METHOD_INTERNATIONAL_DIRECT_CREDIT;
		bankingDetails.swiftCode = 'ABCDEF8R7G3';
		bankingDetails.iban = 'AB12abc';
		bankingDetails.accountName = 'Test';
		bankingDetails.intlAccountNumber = '*********';

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			UnregTransUpdateSubmissionController.acceptClaimWithBanking(
				token,
				bankingDetails
			);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT
				Party_Status__c,
				Claim_Repayment_Method__c,
				Claim_Repayment_Account_Number__c,
				Claim_Repayment_BSB__c,
				Claim_Repayment_Bank_Account_Name__c,
				Claim_Repayment_IBAN__c,
				Claim_Repayment_SWIFT_Code__c,
				Forwarding_Additional_Address_Details__c,
				Forwarding_Street_Address__c,
				Forwarding_Address_Suburb__c,
				Forwarding_Address_State__c,
				Forwarding_Address_Postcode__c,
				Forwarding_Address_Country__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_AGREED,
			tpAfter.Party_Status__c,
			'Should be accepted'
		);
		Assert.areEqual(
			Constants.REPAYMENT_METHOD_INTERNATIONAL_DIRECT_CREDIT,
			tpAfter.Claim_Repayment_Method__c,
			'Expected repaymentMethod set to international direct credit'
		);
		Assert.areEqual(
			bankingDetails.accountName,
			tpAfter.Claim_Repayment_Bank_Account_Name__c,
			'Expected account name set on options'
		);
		Assert.areEqual(
			bankingDetails.intlAccountNumber,
			tpAfter.Claim_Repayment_Account_Number__c,
			'Expected bankAccountNumber set on options'
		);
		Assert.areEqual(
			bankingDetails.swiftCode,
			tpAfter.Claim_Repayment_SWIFT_Code__c,
			'Expected swift set on options'
		);
		Assert.areEqual(
			bankingDetails.iban,
			tpAfter.Claim_Repayment_IBAN__c,
			'Expected swift set on options'
		);
		Assert.areEqual(
			bankingDetails.repaymentMethod,
			tpAfter.Claim_Repayment_Method__c,
			'Expected repaymentMethod set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.additionalStreetDetails,
			tpAfter.Forwarding_Additional_Address_Details__c,
			'Expected additionalStreetDetails set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.street,
			tpAfter.Forwarding_Street_Address__c,
			'Expected country set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.suburb,
			tpAfter.Forwarding_Address_Suburb__c,
			'Expected postcode set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.state,
			tpAfter.Forwarding_Address_State__c,
			'Expected state set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.postcode,
			tpAfter.Forwarding_Address_Postcode__c,
			'Expected street set on options'
		);
		Assert.areEqual(
			bankingDetails.forwardingAddress.country,
			tpAfter.Forwarding_Address_Country__c,
			'Expected suburb set on options'
		);
		Assert.isNull(tpAfter.Claim_Repayment_BSB__c, 'Expected blank bsb');
	}

	@IsTest
	static void testBehaviorWrongToken() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.acceptClaim(
					PublicTransactionSummaryController.createIdToken(
						tp.Transaction__c
					)
				);
				Assert.fail('Should have thrown');
			} catch (AuraHandledException e) {
				Assert.areEqual(
					'Invalid token',
					e.getMessage(),
					'Expected bad token message'
				);
			}
			Test.stopTest();
		}
	}

	@IsTest
	static void testBehaviorCancelledRenter() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		//mark it as cancelled
		update new Transaction__c(
			Id = tp.Transaction__c,
			Status__c = Constants.TRANSACTION_STATUS_CANCELLED
		);
		Transaction_Party__c initiator = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Is_Claim_Initiator__c = TRUE
		];
		initiator.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RENTER_CANCELLED;
		update initiator;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.acceptClaim(token);
				Assert.fail('Should have thrown');
			} catch (AuraHandledException e) {
				Assert.areEqual(
					'CANCELLED_RENTER',
					e.getMessage(),
					'Expected cancelled message'
				);
			}
			Test.stopTest();
		}
	}

	@IsTest
	static void testBehaviorCancelledOther() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		//mark it as cancelled
		update new Transaction__c(
			Id = tp.Transaction__c,
			Status__c = Constants.TRANSACTION_STATUS_CANCELLED
		);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.acceptClaim(token);
				Assert.fail('Should have thrown');
			} catch (AuraHandledException e) {
				Assert.areEqual(
					'CANCELLED_OTHER',
					e.getMessage(),
					'Expected cancelled message'
				);
			}
			Test.stopTest();
		}
	}

	@IsTest
	static void testBehaviorCancelledRTBA() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		//mark it as cancelled
		update new Transaction__c(
			Id = tp.Transaction__c,
			Status__c = Constants.TRANSACTION_STATUS_RTBA_CANCELLED
		);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.acceptClaim(token);
				Assert.fail('Should have thrown');
			} catch (AuraHandledException e) {
				Assert.areEqual(
					'CANCELLED_RTBA',
					e.getMessage(),
					'Expected cancelled message'
				);
			}
			Test.stopTest();
		}
	}

	@IsTest
	static void testBehaviorAlreadyFinalised() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		//mark it as cancelled
		update new Transaction__c(
			Id = tp.Transaction__c,
			Status__c = Constants.TRANSACTION_STATUS_FINALISED
		);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.acceptClaim(token);
				Assert.fail('Should have thrown');
			} catch (AuraHandledException e) {
				Assert.areEqual(
					'FINALISED',
					e.getMessage(),
					'Expected already finalised message'
				);
			}
			Test.stopTest();
		}
	}

	@IsTest
	static void testCourtOrderDispute() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		Transaction_Party__c claimsInitiatingRenter = CasesService.getClaimsInitiatingRenter(
			tpList
		);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		GuestFileUploadAndPreviewController.TransientClaimFileNumber claimFileNumber = GuestFileUploadAndPreviewController.createTransientClaimFileNumber();
		ContentVersion testFile = new ContentVersion(
			ContentLocation = 'S',
			PathOnClient = 'testing.pdf',
			Title = 'Testing Files',
			VersionData = Blob.valueOf('test'),
			Source__c = 'Other',
			Transient_Claim_File_Number_fileupload__c = claimFileNumber.transientFileNumber,
			NetworkId = RTBAOnlineHelper.networkId
		);
		System.runAs(TestDataFactory.getRentalProviderUser()) {
			insert testFile;
		}

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = '1234';
		options.claimFileToken = claimFileNumber;
		options.claimFiles = new List<String>{ testFile.Id.toString() };
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_OTHER_COURT_ORDER;
		options.comments = 'this is a comment';

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c, Transaction__r.Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RTBA,
			tpAfter.Transaction__r.Status__c,
			'Expected transaction to be pending with rtba'
		);
		Transaction_Response__c response = [
			SELECT Id, Court_Reference_Number__c, Status__c, Comments__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_RESPONSE_STATUS_NEW,
			response.Status__c,
			'Expected new response'
		);
		Assert.areEqual(
			options.courtOrderReferenceNumber,
			response.Court_Reference_Number__c,
			'Expected ref we sent to be saved'
		);
		Assert.areEqual(
			options.comments,
			response.Comments__c,
			'Expected input comment ot be saved'
		);

		List<Case> caseList = getCasesByTransactionNumber(tp.Transaction__c);
		Assert.areEqual(1, caseList.size(), 'Expected a case');
		for (Case c : caseList) {
			System.debug(c); //NOPMD
			Assert.areEqual(
				'RIC Dispute with Uploaded Court Document',
				c.Subject,
				'Unexpected value for Subject'
			);
			Assert.areEqual(
				CaseUtility.STATUS_NEW,
				c.Status,
				'Unexpected value for Status'
			);
			Assert.areEqual(
				Constants.QUEUE_SBA_REVIEW_NAME,
				c.Owner.Name,
				'Unexpected value for Owner'
			);
			Assert.areEqual(
				CaseUtility.ASSIGNMENT_GROUP_EO_GROUP,
				c.Assigned_To_Group__c,
				'Unexpected value for Assigned_To_Group__c'
			);
			Assert.areEqual(
				CaseUtility.CATEGORY_BOND_CLAIM,
				c.Category__c,
				'Unexpected value for Category__c'
			);
			Assert.areEqual(
				CaseUtility.SUB_CATEGORY_RENTER_INITIATED_CLAIM,
				c.Sub_Category__c,
				'Unexpected value for Sub_Category__c'
			);
			Assert.areEqual(
				CaseUtility.sbaReviewQueueId,
				c.OwnerId,
				'Unexpected value for OwnerId'
			);
			Assert.areEqual(
				CaseUtility.ORIGIN_RTBA_WEBSITE,
				c.Origin,
				'Unexpected value for Origin'
			);
			Assert.areEqual(
				tp.Role__c,
				c.Initiator__c,
				'Unexpected value for Initiator__c'
			);
			Assert.areEqual(
				tp.Party_Name__c,
				c.Initiator_Name__c,
				'Unexpected value for Initiator_Name__c'
			);
			Assert.areEqual(
				tp.Email_Address__c,
				c.Initiator_Email__c,
				'Unexpected value for Initiator_Email__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Party_Name__c,
				c.Renter_Name__c,
				'Unexpected value for Renter_Name__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Mobile_Number__c,
				c.Renter_Mobile__c,
				'Unexpected value for Renter_Mobile__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Email_Address__c,
				c.Renter_Email__c,
				'Unexpected value for Renter_Email__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Bond__c,
				c.Bond_Number__c,
				'Unexpected value for Bond_Number__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Rental_Provider__c,
				c.AccountId,
				'Unexpected value for AccountId'
			);
		}
	}

	@IsTest
	static void testCourtOrderDisputeDuplicate() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		GuestFileUploadAndPreviewController.TransientClaimFileNumber claimFileNumber = GuestFileUploadAndPreviewController.createTransientClaimFileNumber();
		ContentVersion testFile = new ContentVersion(
			ContentLocation = 'S',
			PathOnClient = 'testing.pdf',
			Title = 'Testing Files',
			VersionData = Blob.valueOf('test'),
			Source__c = 'Other',
			Transient_Claim_File_Number_fileupload__c = claimFileNumber.transientFileNumber,
			NetworkId = RTBAOnlineHelper.networkId
		);
		System.runAs(TestDataFactory.getRentalProviderUser()) {
			insert testFile;
		}

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = '1234';
		options.claimFileToken = claimFileNumber;
		options.claimFiles = new List<String>{ testFile.Id.toString() };
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_OTHER_COURT_ORDER;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			try {
				UnregTransUpdateSubmissionController.submitDispute(
					token,
					options
				);
				Assert.fail('expected dupe exception');
			} catch (AuraHandledException e) {
				Assert.areEqual(
					'DUPLICATE_DISPUTE:' + tp.Party_Name__c,
					e.getMessage(),
					'Expected dupe message with party name'
				);
			}
			options.continueWithDuplicate = true;
			UnregTransUpdateSubmissionController.submitDispute(token, options);

			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		List<Transaction_Response__c> responses = [
			SELECT Id, Court_Reference_Number__c, Status__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(2, responses.size(), 'Expected 2 identical disputes');
		for (Transaction_Response__c response : responses) {
			Assert.areEqual(
				Constants.TRANSACTION_RESPONSE_STATUS_NEW,
				response.Status__c,
				'Expected new response'
			);
			Assert.areEqual(
				options.courtOrderReferenceNumber,
				response.Court_Reference_Number__c,
				'Expected ref we sent to be saved'
			);
		}
	}

	@IsTest
	static void testBehaviorExpired() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		//mark it as submitted earlier
		update new Transaction__c(
			Id = tp.Transaction__c,
			Submission_Date__c = Datetime.now().addDays(-60)
		);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.acceptClaim(token);
				Assert.fail('Should have thrown');
			} catch (AuraHandledException e) {
				Assert.areEqual(
					'EXPIRED',
					e.getMessage(),
					'Expected expired message'
				);
			}
			Test.stopTest();
		}
	}

	@IsTest
	static void testVcatDisputeSuccess() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = 'R2222/00123';
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_VCAT;

		HttpCalloutMock mock = new VCATResponseMock(
			tp.Transaction__r.Bond_Number__c,
			0
		);
		Test.setMock(HttpCalloutMock.class, mock);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c, Transaction__r.Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_RESPONSE,
			tpAfter.Transaction__r.Status__c,
			'Expected transaction to be pending response (still)'
		);
		Transaction_Response__c response = [
			SELECT
				Id,
				VCAT_Order_Number__c,
				Valid_VCAT_Order_Number__c,
				Status__c,
				Comments__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_RESPONSE_STATUS_NEW,
			response.Status__c,
			'Expected new response'
		);
		Assert.areEqual(
			options.courtOrderReferenceNumber,
			response.VCAT_Order_Number__c,
			'Expected ref we sent to be saved'
		);
		Assert.isTrue(
			response.Valid_VCAT_Order_Number__c,
			'Expected valid flag'
		);

		List<Case> caseList = getCasesByTransactionNumber(tp.Transaction__c);
		Assert.areEqual(0, caseList.size(), 'Expected no case');
	}

	@IsTest
	static void testVcatDisputeNotFound() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		Transaction_Party__c claimsInitiatingRenter = CasesService.getClaimsInitiatingRenter(
			tpList
		);

		HttpCalloutMock mock = new VCATResponseMock(
			tp.Transaction__r.Bond_Number__c,
			-2
		);
		Test.setMock(HttpCalloutMock.class, mock);

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		GuestFileUploadAndPreviewController.TransientClaimFileNumber claimFileNumber = GuestFileUploadAndPreviewController.createTransientClaimFileNumber();
		ContentVersion testFile = new ContentVersion(
			ContentLocation = 'S',
			PathOnClient = 'testing.pdf',
			Title = 'Testing Files',
			VersionData = Blob.valueOf('test'),
			Source__c = 'Other',
			Transient_Claim_File_Number_fileupload__c = claimFileNumber.transientFileNumber,
			NetworkId = RTBAOnlineHelper.networkId
		);
		System.runAs(TestDataFactory.getRentalProviderUser()) {
			insert testFile;
		}

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = 'R2222/00123';
		options.claimFileToken = claimFileNumber;
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_VCAT;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.submitDispute(
					token,
					options
				);
				Assert.fail('Expected exception');
			} catch (AuraHandledException ex) {
				Assert.areEqual(
					'VCAT_VALIDATION:not_found',
					ex.getMessage(),
					'Expected not found message'
				);
			}
			options.claimFiles = new List<String>{ testFile.Id.toString() };
			options.comments = 'this is a comment';
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c, Transaction__r.Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RTBA,
			tpAfter.Transaction__r.Status__c,
			'Expected transaction to be pending with rtba'
		);
		Transaction_Response__c response = [
			SELECT
				Id,
				VCAT_Order_Number__c,
				Valid_VCAT_Order_Number__c,
				Status__c,
				Comments__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_RESPONSE_STATUS_NEW,
			response.Status__c,
			'Expected new response'
		);
		Assert.areEqual(
			options.courtOrderReferenceNumber,
			response.VCAT_Order_Number__c,
			'Expected ref we sent to be saved'
		);
		Assert.isFalse(
			response.Valid_VCAT_Order_Number__c,
			'Valid flag not expected'
		);
		Assert.areEqual(
			options.comments,
			response.Comments__c,
			'Expected input comment to be saved'
		);

		List<Case> caseList = getCasesByTransactionNumber(tp.Transaction__c);
		Assert.areEqual(1, caseList.size(), 'Expected a case');
		for (Case c : caseList) {
			System.debug(c); //NOPMD
			Assert.areEqual(
				'RIC Dispute with Uploaded Court Document',
				c.Subject,
				'Unexpected value for Subject'
			);
			Assert.areEqual(
				CaseUtility.STATUS_NEW,
				c.Status,
				'Unexpected value for Status'
			);
			Assert.areEqual(
				Constants.QUEUE_SBA_REVIEW_NAME,
				c.Owner.Name,
				'Unexpected value for Owner'
			);
			Assert.areEqual(
				CaseUtility.ASSIGNMENT_GROUP_EO_GROUP,
				c.Assigned_To_Group__c,
				'Unexpected value for Assigned_To_Group__c'
			);
			Assert.areEqual(
				CaseUtility.CATEGORY_BOND_CLAIM,
				c.Category__c,
				'Unexpected value for Category__c'
			);
			Assert.areEqual(
				CaseUtility.SUB_CATEGORY_RENTER_INITIATED_CLAIM,
				c.Sub_Category__c,
				'Unexpected value for Sub_Category__c'
			);
			Assert.areEqual(
				CaseUtility.sbaReviewQueueId,
				c.OwnerId,
				'Unexpected value for OwnerId'
			);
			Assert.areEqual(
				CaseUtility.ORIGIN_RTBA_WEBSITE,
				c.Origin,
				'Unexpected value for Origin'
			);
			Assert.areEqual(
				tp.Role__c,
				c.Initiator__c,
				'Unexpected value for Initiator__c'
			);
			Assert.areEqual(
				tp.Party_Name__c,
				c.Initiator_Name__c,
				'Unexpected value for Initiator_Name__c'
			);
			Assert.areEqual(
				tp.Email_Address__c,
				c.Initiator_Email__c,
				'Unexpected value for Initiator_Email__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Party_Name__c,
				c.Renter_Name__c,
				'Unexpected value for Renter_Name__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Mobile_Number__c,
				c.Renter_Mobile__c,
				'Unexpected value for Renter_Mobile__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Email_Address__c,
				c.Renter_Email__c,
				'Unexpected value for Renter_Email__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Bond__c,
				c.Bond_Number__c,
				'Unexpected value for Bond_Number__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Rental_Provider__c,
				c.AccountId,
				'Unexpected value for AccountId'
			);
		}
	}

	@IsTest
	static void testVcatDisputeNoBond() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		Transaction_Party__c claimsInitiatingRenter = CasesService.getClaimsInitiatingRenter(
			tpList
		);

		HttpCalloutMock mock = new VCATResponseMock(
			tp.Transaction__r.Bond_Number__c,
			-1
		);
		Test.setMock(HttpCalloutMock.class, mock);

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		GuestFileUploadAndPreviewController.TransientClaimFileNumber claimFileNumber = GuestFileUploadAndPreviewController.createTransientClaimFileNumber();
		ContentVersion testFile = new ContentVersion(
			ContentLocation = 'S',
			PathOnClient = 'testing.pdf',
			Title = 'Testing Files',
			VersionData = Blob.valueOf('test'),
			Source__c = 'Other',
			Transient_Claim_File_Number_fileupload__c = claimFileNumber.transientFileNumber,
			NetworkId = RTBAOnlineHelper.networkId
		);
		System.runAs(TestDataFactory.getRentalProviderUser()) {
			insert testFile;
		}

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = 'R2222/00123';
		options.claimFileToken = claimFileNumber;
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_VCAT;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.submitDispute(
					token,
					options
				);
				Assert.fail('Expected exception');
			} catch (AuraHandledException ex) {
				Assert.areEqual(
					'VCAT_VALIDATION:bond_missing',
					ex.getMessage(),
					'Expected bond_missing message'
				);
			}
			options.claimFiles = new List<String>{ testFile.Id.toString() };
			options.comments = 'this is a comment';
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c, Transaction__r.Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RTBA,
			tpAfter.Transaction__r.Status__c,
			'Expected transaction to be pending with rtba'
		);
		Transaction_Response__c response = [
			SELECT
				Id,
				VCAT_Order_Number__c,
				Valid_VCAT_Order_Number__c,
				Status__c,
				Comments__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_RESPONSE_STATUS_NEW,
			response.Status__c,
			'Expected new response'
		);
		Assert.areEqual(
			options.courtOrderReferenceNumber,
			response.VCAT_Order_Number__c,
			'Expected ref we sent to be saved'
		);
		Assert.isFalse(
			response.Valid_VCAT_Order_Number__c,
			'Valid flag not expected'
		);
		Assert.areEqual(
			options.comments,
			response.Comments__c,
			'Expected input comment to be saved'
		);

		List<Case> caseList = getCasesByTransactionNumber(tp.Transaction__c);
		Assert.areEqual(1, caseList.size(), 'Expected a case');
		for (Case c : caseList) {
			System.debug(c); //NOPMD
			Assert.areEqual(
				'RIC Dispute with Uploaded Court Document',
				c.Subject,
				'Unexpected value for Subject'
			);
			Assert.areEqual(
				CaseUtility.STATUS_NEW,
				c.Status,
				'Unexpected value for Status'
			);
			Assert.areEqual(
				Constants.QUEUE_SBA_REVIEW_NAME,
				c.Owner.Name,
				'Unexpected value for Owner'
			);
			Assert.areEqual(
				CaseUtility.ASSIGNMENT_GROUP_EO_GROUP,
				c.Assigned_To_Group__c,
				'Unexpected value for Assigned_To_Group__c'
			);
			Assert.areEqual(
				CaseUtility.CATEGORY_BOND_CLAIM,
				c.Category__c,
				'Unexpected value for Category__c'
			);
			Assert.areEqual(
				CaseUtility.SUB_CATEGORY_RENTER_INITIATED_CLAIM,
				c.Sub_Category__c,
				'Unexpected value for Sub_Category__c'
			);
			Assert.areEqual(
				CaseUtility.sbaReviewQueueId,
				c.OwnerId,
				'Unexpected value for OwnerId'
			);
			Assert.areEqual(
				CaseUtility.ORIGIN_RTBA_WEBSITE,
				c.Origin,
				'Unexpected value for Origin'
			);
			Assert.areEqual(
				tp.Role__c,
				c.Initiator__c,
				'Unexpected value for Initiator__c'
			);
			Assert.areEqual(
				tp.Party_Name__c,
				c.Initiator_Name__c,
				'Unexpected value for Initiator_Name__c'
			);
			Assert.areEqual(
				tp.Email_Address__c,
				c.Initiator_Email__c,
				'Unexpected value for Initiator_Email__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Party_Name__c,
				c.Renter_Name__c,
				'Unexpected value for Renter_Name__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Mobile_Number__c,
				c.Renter_Mobile__c,
				'Unexpected value for Renter_Mobile__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Email_Address__c,
				c.Renter_Email__c,
				'Unexpected value for Renter_Email__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Bond__c,
				c.Bond_Number__c,
				'Unexpected value for Bond_Number__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Rental_Provider__c,
				c.AccountId,
				'Unexpected value for AccountId'
			);
		}
	}

	@IsTest
	static void testVcatDisputeWithdrawn() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		Transaction_Party__c claimsInitiatingRenter = CasesService.getClaimsInitiatingRenter(
			tpList
		);

		HttpCalloutMock mock = new VCATResponseMock(
			tp.Transaction__r.Bond_Number__c,
			4
		);
		Test.setMock(HttpCalloutMock.class, mock);

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		GuestFileUploadAndPreviewController.TransientClaimFileNumber claimFileNumber = GuestFileUploadAndPreviewController.createTransientClaimFileNumber();
		ContentVersion testFile = new ContentVersion(
			ContentLocation = 'S',
			PathOnClient = 'testing.pdf',
			Title = 'Testing Files',
			VersionData = Blob.valueOf('test'),
			Source__c = 'Other',
			Transient_Claim_File_Number_fileupload__c = claimFileNumber.transientFileNumber,
			NetworkId = RTBAOnlineHelper.networkId
		);
		System.runAs(TestDataFactory.getRentalProviderUser()) {
			insert testFile;
		}

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = 'R2222/00123';
		options.claimFileToken = claimFileNumber;
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_VCAT;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.submitDispute(
					token,
					options
				);
				Assert.fail('Expected exception');
			} catch (AuraHandledException ex) {
				Assert.areEqual(
					'VCAT_VALIDATION:withdrawn',
					ex.getMessage(),
					'Expected withdrawn message'
				);
			}
			options.claimFiles = new List<String>{ testFile.Id.toString() };
			options.comments = 'this is a comment';
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c, Transaction__r.Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RTBA,
			tpAfter.Transaction__r.Status__c,
			'Expected transaction to be pending with rtba'
		);
		Transaction_Response__c response = [
			SELECT
				Id,
				VCAT_Order_Number__c,
				Valid_VCAT_Order_Number__c,
				Status__c,
				Comments__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_RESPONSE_STATUS_NEW,
			response.Status__c,
			'Expected new response'
		);
		Assert.areEqual(
			options.courtOrderReferenceNumber,
			response.VCAT_Order_Number__c,
			'Expected ref we sent to be saved'
		);
		Assert.isFalse(
			response.Valid_VCAT_Order_Number__c,
			'Valid flag not expected'
		);
		Assert.areEqual(
			options.comments,
			response.Comments__c,
			'Expected input comment to be saved'
		);

		List<Case> caseList = getCasesByTransactionNumber(tp.Transaction__c);
		Assert.areEqual(1, caseList.size(), 'Expected a case');
		for (Case c : caseList) {
			System.debug(c); //NOPMD
			Assert.areEqual(
				'RIC Dispute with Uploaded Court Document',
				c.Subject,
				'Unexpected value for Subject'
			);
			Assert.areEqual(
				CaseUtility.STATUS_NEW,
				c.Status,
				'Unexpected value for Status'
			);
			Assert.areEqual(
				Constants.QUEUE_SBA_REVIEW_NAME,
				c.Owner.Name,
				'Unexpected value for Owner'
			);
			Assert.areEqual(
				CaseUtility.ASSIGNMENT_GROUP_EO_GROUP,
				c.Assigned_To_Group__c,
				'Unexpected value for Assigned_To_Group__c'
			);
			Assert.areEqual(
				CaseUtility.CATEGORY_BOND_CLAIM,
				c.Category__c,
				'Unexpected value for Category__c'
			);
			Assert.areEqual(
				CaseUtility.SUB_CATEGORY_RENTER_INITIATED_CLAIM,
				c.Sub_Category__c,
				'Unexpected value for Sub_Category__c'
			);
			Assert.areEqual(
				CaseUtility.sbaReviewQueueId,
				c.OwnerId,
				'Unexpected value for OwnerId'
			);
			Assert.areEqual(
				CaseUtility.ORIGIN_RTBA_WEBSITE,
				c.Origin,
				'Unexpected value for Origin'
			);
			Assert.areEqual(
				tp.Role__c,
				c.Initiator__c,
				'Unexpected value for Initiator__c'
			);
			Assert.areEqual(
				tp.Party_Name__c,
				c.Initiator_Name__c,
				'Unexpected value for Initiator_Name__c'
			);
			Assert.areEqual(
				tp.Email_Address__c,
				c.Initiator_Email__c,
				'Unexpected value for Initiator_Email__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Party_Name__c,
				c.Renter_Name__c,
				'Unexpected value for Renter_Name__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Mobile_Number__c,
				c.Renter_Mobile__c,
				'Unexpected value for Renter_Mobile__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Email_Address__c,
				c.Renter_Email__c,
				'Unexpected value for Renter_Email__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Bond__c,
				c.Bond_Number__c,
				'Unexpected value for Bond_Number__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Rental_Provider__c,
				c.AccountId,
				'Unexpected value for AccountId'
			);
		}
	}

	@IsTest
	static void testVcatDisputeRejected() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		Transaction_Party__c claimsInitiatingRenter = CasesService.getClaimsInitiatingRenter(
			tpList
		);

		HttpCalloutMock mock = new VCATResponseMock(
			tp.Transaction__r.Bond_Number__c,
			5
		);
		Test.setMock(HttpCalloutMock.class, mock);

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		GuestFileUploadAndPreviewController.TransientClaimFileNumber claimFileNumber = GuestFileUploadAndPreviewController.createTransientClaimFileNumber();
		ContentVersion testFile = new ContentVersion(
			ContentLocation = 'S',
			PathOnClient = 'testing.pdf',
			Title = 'Testing Files',
			VersionData = Blob.valueOf('test'),
			Source__c = 'Other',
			Transient_Claim_File_Number_fileupload__c = claimFileNumber.transientFileNumber,
			NetworkId = RTBAOnlineHelper.networkId
		);
		System.runAs(TestDataFactory.getRentalProviderUser()) {
			insert testFile;
		}

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = 'R2222/00123';
		options.claimFileToken = claimFileNumber;
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_VCAT;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.submitDispute(
					token,
					options
				);
				Assert.fail('Expected exception');
			} catch (AuraHandledException ex) {
				Assert.areEqual(
					'VCAT_VALIDATION:rejected',
					ex.getMessage(),
					'Expected rejected message'
				);
			}
			options.claimFiles = new List<String>{ testFile.Id.toString() };
			options.comments = 'this is a comment';
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c, Transaction__r.Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RTBA,
			tpAfter.Transaction__r.Status__c,
			'Expected transaction to be pending with rtba'
		);
		Transaction_Response__c response = [
			SELECT
				Id,
				VCAT_Order_Number__c,
				Valid_VCAT_Order_Number__c,
				Status__c,
				Comments__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_RESPONSE_STATUS_NEW,
			response.Status__c,
			'Expected new response'
		);
		Assert.areEqual(
			options.courtOrderReferenceNumber,
			response.VCAT_Order_Number__c,
			'Expected ref we sent to be saved'
		);
		Assert.isFalse(
			response.Valid_VCAT_Order_Number__c,
			'Valid flag not expected'
		);
		Assert.areEqual(
			options.comments,
			response.Comments__c,
			'Expected input comment to be saved'
		);

		List<Case> caseList = getCasesByTransactionNumber(tp.Transaction__c);
		Assert.areEqual(1, caseList.size(), 'Expected a case');
		for (Case c : caseList) {
			System.debug(c); //NOPMD
			Assert.areEqual(
				'RIC Dispute with Uploaded Court Document',
				c.Subject,
				'Unexpected value for Subject'
			);
			Assert.areEqual(
				CaseUtility.STATUS_NEW,
				c.Status,
				'Unexpected value for Status'
			);
			Assert.areEqual(
				Constants.QUEUE_SBA_REVIEW_NAME,
				c.Owner.Name,
				'Unexpected value for Owner'
			);
			Assert.areEqual(
				CaseUtility.ASSIGNMENT_GROUP_EO_GROUP,
				c.Assigned_To_Group__c,
				'Unexpected value for Assigned_To_Group__c'
			);
			Assert.areEqual(
				CaseUtility.CATEGORY_BOND_CLAIM,
				c.Category__c,
				'Unexpected value for Category__c'
			);
			Assert.areEqual(
				CaseUtility.SUB_CATEGORY_RENTER_INITIATED_CLAIM,
				c.Sub_Category__c,
				'Unexpected value for Sub_Category__c'
			);
			Assert.areEqual(
				CaseUtility.sbaReviewQueueId,
				c.OwnerId,
				'Unexpected value for OwnerId'
			);
			Assert.areEqual(
				CaseUtility.ORIGIN_RTBA_WEBSITE,
				c.Origin,
				'Unexpected value for Origin'
			);
			Assert.areEqual(
				tp.Role__c,
				c.Initiator__c,
				'Unexpected value for Initiator__c'
			);
			Assert.areEqual(
				tp.Party_Name__c,
				c.Initiator_Name__c,
				'Unexpected value for Initiator_Name__c'
			);
			Assert.areEqual(
				tp.Email_Address__c,
				c.Initiator_Email__c,
				'Unexpected value for Initiator_Email__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Party_Name__c,
				c.Renter_Name__c,
				'Unexpected value for Renter_Name__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Mobile_Number__c,
				c.Renter_Mobile__c,
				'Unexpected value for Renter_Mobile__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Email_Address__c,
				c.Renter_Email__c,
				'Unexpected value for Renter_Email__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Bond__c,
				c.Bond_Number__c,
				'Unexpected value for Bond_Number__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Rental_Provider__c,
				c.AccountId,
				'Unexpected value for AccountId'
			);
		}
	}

	@IsTest
	static void testVcatDisputeWrongBond() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);
		Transaction_Party__c claimsInitiatingRenter = CasesService.getClaimsInitiatingRenter(
			tpList
		);

		HttpCalloutMock mock = new VCATResponseMock('202456', 0);
		Test.setMock(HttpCalloutMock.class, mock);

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		GuestFileUploadAndPreviewController.TransientClaimFileNumber claimFileNumber = GuestFileUploadAndPreviewController.createTransientClaimFileNumber();
		ContentVersion testFile = new ContentVersion(
			ContentLocation = 'S',
			PathOnClient = 'testing.pdf',
			Title = 'Testing Files',
			VersionData = Blob.valueOf('test'),
			Source__c = 'Other',
			Transient_Claim_File_Number_fileupload__c = claimFileNumber.transientFileNumber,
			NetworkId = RTBAOnlineHelper.networkId
		);
		System.runAs(TestDataFactory.getRentalProviderUser()) {
			insert testFile;
		}

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = 'R2222/00123';
		options.claimFileToken = claimFileNumber;
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_VCAT;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.submitDispute(
					token,
					options
				);
				Assert.fail('Expected exception');
			} catch (AuraHandledException ex) {
				Assert.areEqual(
					'VCAT_VALIDATION:incorrect_bond',
					ex.getMessage(),
					'Expected incorrect_bond message'
				);
			}
			options.claimFiles = new List<String>{ testFile.Id.toString() };
			options.comments = 'this is a comment';
			UnregTransUpdateSubmissionController.submitDispute(token, options);
			Test.stopTest();
		}

		Transaction_Party__c tpAfter = [
			SELECT Party_Status__c, Transaction__r.Status__c
			FROM Transaction_Party__c
			WHERE Id = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_PARTY_STATUS_DISPUTED,
			tpAfter.Party_Status__c,
			'Should be disputed'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RTBA,
			tpAfter.Transaction__r.Status__c,
			'Expected transaction to be pending with rtba'
		);
		Transaction_Response__c response = [
			SELECT
				Id,
				VCAT_Order_Number__c,
				Valid_VCAT_Order_Number__c,
				Status__c,
				Comments__c
			FROM Transaction_Response__c
			WHERE
				Transaction__c = :tp.Transaction__c
				AND Disputing_Party__c = :tp.Id
		];
		Assert.areEqual(
			Constants.TRANSACTION_RESPONSE_STATUS_NEW,
			response.Status__c,
			'Expected new response'
		);
		Assert.areEqual(
			options.courtOrderReferenceNumber,
			response.VCAT_Order_Number__c,
			'Expected ref we sent to be saved'
		);
		Assert.isFalse(
			response.Valid_VCAT_Order_Number__c,
			'Valid flag not expected'
		);
		Assert.areEqual(
			options.comments,
			response.Comments__c,
			'Expected input comment to be saved'
		);

		List<Case> caseList = getCasesByTransactionNumber(tp.Transaction__c);
		Assert.areEqual(1, caseList.size(), 'Expected a case');
		for (Case c : caseList) {
			System.debug(c); //NOPMD
			Assert.areEqual(
				'RIC Dispute with Uploaded Court Document',
				c.Subject,
				'Unexpected value for Subject'
			);
			Assert.areEqual(
				CaseUtility.STATUS_NEW,
				c.Status,
				'Unexpected value for Status'
			);
			Assert.areEqual(
				Constants.QUEUE_SBA_REVIEW_NAME,
				c.Owner.Name,
				'Unexpected value for Owner'
			);
			Assert.areEqual(
				CaseUtility.ASSIGNMENT_GROUP_EO_GROUP,
				c.Assigned_To_Group__c,
				'Unexpected value for Assigned_To_Group__c'
			);
			Assert.areEqual(
				CaseUtility.CATEGORY_BOND_CLAIM,
				c.Category__c,
				'Unexpected value for Category__c'
			);
			Assert.areEqual(
				CaseUtility.SUB_CATEGORY_RENTER_INITIATED_CLAIM,
				c.Sub_Category__c,
				'Unexpected value for Sub_Category__c'
			);
			Assert.areEqual(
				CaseUtility.sbaReviewQueueId,
				c.OwnerId,
				'Unexpected value for OwnerId'
			);
			Assert.areEqual(
				CaseUtility.ORIGIN_RTBA_WEBSITE,
				c.Origin,
				'Unexpected value for Origin'
			);
			Assert.areEqual(
				tp.Role__c,
				c.Initiator__c,
				'Unexpected value for Initiator__c'
			);
			Assert.areEqual(
				tp.Party_Name__c,
				c.Initiator_Name__c,
				'Unexpected value for Initiator_Name__c'
			);
			Assert.areEqual(
				tp.Email_Address__c,
				c.Initiator_Email__c,
				'Unexpected value for Initiator_Email__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Party_Name__c,
				c.Renter_Name__c,
				'Unexpected value for Renter_Name__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Mobile_Number__c,
				c.Renter_Mobile__c,
				'Unexpected value for Renter_Mobile__c'
			);
			Assert.areEqual(
				claimsInitiatingRenter.Email_Address__c,
				c.Renter_Email__c,
				'Unexpected value for Renter_Email__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Bond__c,
				c.Bond_Number__c,
				'Unexpected value for Bond_Number__c'
			);
			Assert.areEqual(
				tp.Transaction__r.Rental_Provider__c,
				c.AccountId,
				'Unexpected value for AccountId'
			);
		}
	}

	@IsTest
	static void testVcatDisputeVcatUnavailable() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c tp = getNonInitiator(tpList);

		HttpCalloutMock mock = new ExceptionMock();
		Test.setMock(HttpCalloutMock.class, mock);

		String token = PublicTransactionSummaryController.createIdToken(
			tp.Transaction__c,
			tp.Id,
			TestDataFactory.userInfoObject
		);

		UnregTransUpdateSubmissionController.DisputeOptions options = new UnregTransUpdateSubmissionController.DisputeOptions();
		options.courtOrderReferenceNumber = 'R2222/00123';
		options.responseOption = Constants.TRANSACTION_REPAYMENT_BASIS_VCAT;

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			try {
				UnregTransUpdateSubmissionController.submitDispute(
					token,
					options
				);
				Assert.fail('Expected exception');
			} catch (AuraHandledException ex) {
				Assert.areEqual(
					'VCAT_UNAVAILABLE',
					ex.getMessage(),
					'Expected VCAT_UNAVAILABLE message'
				);
			}
			Test.stopTest();
		}
	}

	@IsTest
	static void testBehaviorInitiatorCancel() {
		List<Transaction_Party__c> tpList = queryTransactionParties();
		Transaction_Party__c submittingParty = getInitiator(tpList);
		String token = PublicTransactionSummaryController.createIdToken(
			submittingParty.Transaction__c,
			submittingParty.Id,
			TestDataFactory.userInfoObject
		);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			Test.startTest();
			UnregTransUpdateSubmissionController.initiatorCancel(token);
			Test.stopTest();
		}

		Transaction__c txAfter = [
			SELECT
				Status__c,
				(SELECT Id, Party_Status__c FROM Transaction_Parties__r)
			FROM Transaction__c
			WHERE Id = :submittingParty.Transaction__c
		];
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_CANCELLED,
			txAfter.Status__c,
			'Expected cancelled tx'
		);
		Boolean foundInitiator = false;
		Boolean foundNonInitiator = false;
		for (
			Transaction_Party__c transactionParty : txAfter.Transaction_Parties__r
		) {
			if (transactionParty.Id == submittingParty.Id) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_RENTER_CANCELLED,
					transactionParty.Party_Status__c,
					'Expected submittor to be Renter Cancelled'
				);
				foundInitiator = true;
			} else {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_CANCELLED,
					transactionParty.Party_Status__c,
					'Expected submittor to be Cancelled'
				);
				foundNonInitiator = true;
			}
		}
		Assert.isTrue(foundInitiator, 'Expected to have found initiator');
		Assert.isTrue(
			foundNonInitiator,
			'Expected to have found non-initiator'
		);
	}

	private class VCATResponseMock implements HttpCalloutMock {
		private final Integer bondNumber;
		private final Integer responseStatus;

		VCATResponseMock(String bondName, Integer responseStatus) {
			this.bondNumber = Integer.valueOf(bondName);
			this.responseStatus = responseStatus;
		}

		public HttpResponse respond(HttpRequest request) {
			String jsonText = [
					SELECT Body
					FROM StaticResource
					WHERE Name = 'VCATResponseStatus1'
				]
				.Body.toString();
			VCATMessageResponse payload = (VCATMessageResponse) JSON.deserialize(
				jsonText,
				VCATMessageResponse.class
			);
			payload.returnStatus = this.responseStatus;
			payload.bondDetailsPerTenant[0].bondNumberDoh = this.bondNumber;
			payload.bondDetailsPerTenant[0].bondNumberTenant = this.bondNumber;
			payload.applicationLevelBondDetails.bondNumberTenant = this.bondNumber;
			payload.payDohAmount = '$750';
			HttpResponse response = new HttpResponse();
			response.setBody(JSON.serialize(payload));
			response.setStatusCode(200);
			response.setHeader('Content-Type', 'application/json');
			return response;
		}
	}

	private class ExceptionMock implements HttpCalloutMock {
		public HttpResponse respond(HttpRequest param1) {
			throw new CalloutException('Something went horribly wrong');
		}
	}

	private static List<Case> getCasesByTransactionNumber(
		Id transactionNumber
	) {
		return [
			SELECT
				Id,
				Subject,
				Description,
				Status,
				Assigned_To_Group__c,
				Priority,
				Category__c,
				Sub_Category__c,
				Bond_Number__c,
				OwnerId,
				Origin,
				AccountId,
				Initiator__c,
				Initiator_Name__c,
				Initiator_Email__c,
				Renter_Name__c,
				Renter_Mobile__c,
				Renter_Email__c,
				TYPEOF Owner
					WHEN Group THEN Name
					WHEN User THEN Name
				END
			FROM Case
			WHERE Transaction_Number__c = :transactionNumber
		];
	}

	private static Transaction_Party__c getNonInitiator(
		List<Transaction_Party__c> partyList
	) {
		for (Transaction_Party__c tp : partyList) {
			if (!tp.Is_Claim_Initiator__c) {
				return tp;
			}
		}
		return null;
	}

	private static Transaction_Party__c getInitiator(
		List<Transaction_Party__c> partyList
	) {
		for (Transaction_Party__c tp : partyList) {
			if (tp.Is_Claim_Initiator__c) {
				return tp;
			}
		}
		return null;
	}

	private static List<Transaction_Party__c> queryTransactionParties() {
		return [
			SELECT
				Id,
				Party_Name__c,
				First_Name__c,
				Last_Name__c,
				Company_Name__c,
				Renter_Type__c,
				Transaction__c,
				Role__c,
				Email_Address__c,
				Mobile_Number__c,
				Is_Claim_Initiator__c,
				Transaction__r.Bond__c,
				Transaction__r.Rental_Provider__c,
				Transaction__r.Bond_Number__c
			FROM Transaction_Party__c
			WHERE
				Role__c = :Constants.TRANSACTION_PARTY_ROLE_RENTER
				AND Transaction__r.RecordTypeId = :Constants.RECORD_TYPE_ID_TRANSACTION_CLAIM
		];
	}
}