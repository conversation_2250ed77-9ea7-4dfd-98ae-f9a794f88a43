public with sharing class RenterTransferResubmissionController {
	@TestVisible
	private static final String SUBMISSION_TYPE_RP_REVIEW = 'RP Review';
	@TestVisible
	private static final String SUBMISSION_TYPE_UPDATE = 'Update';
	@TestVisible
	private static final String SUBMISSION_TYPE_RESUBMIT = 'Resubmit';
	private static final String EXPERIENCE_PAGE_RENTER_REVIEW = 'transfer-renter-review';
	private static final List<String> VALID_TRANSACTION_STATUSES_FOR_RESUBMIT = new List<String>{
		Constants.TRANSACTION_STATUS_CANCELLED,
		Constants.TRANSACTION_STATUS_TIMEDOUT
	};
	private static final List<String> VALID_TRANSACTION_STATUSES_FOR_UPDATE = new List<String>{
		Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
	};

	public static final String ERROR_DEFAULT = Label.Common_Error_Message_For_System_Failures;
	public static final String ERROR_BOND_SUSPENDED = Label.Renter_Transfer_Message_When_Bond_is_Suspended;
	public static final String ERROR_RP_CANCELLED = Label.RP_Review_Message_When_Resubmit_If_Transaction_Cancelled;
	public static final String ERROR_TC_REJECTED = Label.Update_Lodgement_Message_When_Resubmit_If_TC_Rejected;
	public static final String ERROR_EMAIL_BOUNCED = Label.RP_Review_Claim_Message_On_Resubmission_If_Email_Bounced;
	public static final String ERROR_FINALISED = Label.Transaction_Review_Message_When_RP_Resubmit_If_Finalised_And_Private_Fund;
	public static final String ERROR_REVISION_UPDATED = Label.Transaction_Resubmit_Message_When_Is_Updated;
	public static final String ERROR_TIME_OUT = Label.RP_Review_Claim_Message_On_Resubmission_If_Timed_Out;

	public static final String SUCCESS_MESSAGE = Label.Renter_Transfer_Message_When_Successful_Transfer_Resubmission;

	private static final WithoutSharingHelper WITHOUT_SHARING = new WithoutSharingHelper();

	public static final Map<SObjectField, SObjectField> RENTER_1_TO_REVIEW_OLD_VALUE = new Map<SObjectField, SObjectField>{
		Transaction_Party__c.Renter_Type__c => Transaction_Review__c.Renter_Type__c,
		Transaction_Party__c.Company_Name__c => Transaction_Review__c.Company_Name__c,
		Transaction_Party__c.First_Name__c => Transaction_Review__c.First_Name__c,
		Transaction_Party__c.Last_Name__c => Transaction_Review__c.Last_Name__c,
		Transaction_Party__c.Email_Address__c => Transaction_Review__c.Email_Address__c,
		Transaction_Party__c.Mobile_Number__c => Transaction_Review__c.Mobile_Number__c,
		Transaction_Party__c.Date_of_Birth__c => Transaction_Review__c.Date_of_Birth__c,
		Transaction_Party__c.Renter_position__c => Transaction_Review__c.Renter_position__c
	};

	/** List of Renter revised fields, starting with Renter 1 at index 0 */
	public static final List<Map<SObjectField, SObjectField>> RENTER_N_FIELD_TO_REVIEW_OLD_VALUE = new List<Map<SObjectField, SObjectField>>();

	static {
		// Add Renter 1's fields
		RENTER_N_FIELD_TO_REVIEW_OLD_VALUE.add(RENTER_1_TO_REVIEW_OLD_VALUE);

		Map<String, SObjectField> reviewFields = Transaction_Review__c.SObjectType.getDescribe()
			.fields.getMap();

		//stitch together renter 2-10 list by mangling Renter 1's
		for (Integer renterN = 2; renterN <= 20; renterN++) {
			Map<SObjectField, SObjectField> renterFields = new Map<SObjectField, SObjectField>();

			//Generate field names
			for (
				SObjectField transactionPartyField : RENTER_1_TO_REVIEW_OLD_VALUE.keySet()
			) {
				SObjectField r1ReviewField = RENTER_1_TO_REVIEW_OLD_VALUE.get(
					transactionPartyField
				);

				//map field names to the numbered versions for Renter N
				String rNReviewFieldName = r1ReviewField.getDescribe()
					.name.replace('__c', '_' + renterN + '__c');

				//Get the SObjectField versions
				SObjectField rNReviewField = reviewFields.get(
					rNReviewFieldName
				);

				//if they're both found, add to the list. Checked in test class
				if (rNReviewField != null) {
					renterFields.put(transactionPartyField, rNReviewField);
				} else if (Test.isRunningTest()) {
					System.debug( //NOPMD - test mode only, test class will throw
						LoggingLevel.WARN,
						String.format(
							'Renter field not found {0}->{1}',
							new List<String>{
								transactionPartyField.getDescribe().getName(),
								rNReviewFieldName
							}
						)
					);
				}
			}

			//add renter map to list
			RENTER_N_FIELD_TO_REVIEW_OLD_VALUE.add(renterFields);
		}
	}

	@AuraEnabled(Cacheable=false)
	public static Transaction__c getValidatedTransactionForUpdate(Id recordId) {
		Transaction__c transferTx = new Transaction__c();
		transferTx = TransferRpReviewController.getTransaction(recordId);
		validateTransactionType(transferTx);
		validateStatuses(transferTx, SUBMISSION_TYPE_UPDATE);
		return transferTx;
	}

	@AuraEnabled(Cacheable=false)
	public static QueryResponse getValidatedTransactionForResubmit(
		Id recordId,
		Id bondId
	) {
		validateInput(recordId);
		validateBondStatus(bondId);

		QueryResponse queryResponse = new QueryResponse();
		Bond__c bond = BondSummaryRentalProviderController.getBondData(bondId);
		Map<Id, Datetime> modifiedDateTimeMap = RentalProviderTransferController.getModifiedDateTimeMap(
			bond
		);

		Transaction__c transferTx = new Transaction__c();
		transferTx = TransferRpReviewController.getTransaction(recordId);
		validatePermission(SUBMISSION_TYPE_RESUBMIT);
		validateTransactionType(transferTx);
		validateStatuses(transferTx, SUBMISSION_TYPE_RESUBMIT);
		validateUploadedDocuments(transferTx);

		queryResponse.txDetails = transferTx;
		queryResponse.modifiedDateTimeMap = modifiedDateTimeMap;
		return queryResponse;
	}

	@AuraEnabled(Cacheable=false)
	public static TransferSubmissionResponse handleRpReviewTransferTxResubmit(
		ResubmissionRequest request
	) {
		TransferSubmissionResponse submitResult = new TransferSubmissionResponse();
		//Query bond and further validation for related bond
		Bond__c bondRecord = BondSummaryRentalProviderController.getBondData(
			request.txDetails.Bond__c
		);

		Transaction__c originalTransaction = TransferRpReviewController.getTransaction(
			request.txDetails.Id
		);

		validateTxBeforeResubmit(originalTransaction);
		validateBondDetailsChanged(bondRecord, request.modifiedDateTimeMap);

		Savepoint sp = Database.setSavepoint();

		List<Transaction_Party__c> tpInsertList = new List<Transaction_Party__c>();
		List<Transaction_Party__c> tpUpdateList = new List<Transaction_Party__c>();
		List<Transaction_Party__c> transactionParties = new List<Transaction_Party__c>();

		Set<Id> transactionPartyIds = new Set<Id>();
		Transaction__c newTx = new Transaction__c();
		Transaction_Party__c rpParty;
		try {
			Datetime partyStatusDate = System.now();

			//Seperate insert and update parties
			for (Transaction_Party__c transactionParty : request.tpDetails) {
				transactionPartyIds.add(transactionParty.Id);
				transactionParty.Initiate_marketing_comms__c = true;

				if (transactionParty.Id == null) {
					transactionParty.Transaction__c = request.txDetails.Id;
					transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;
					transactionParty.Mobile_Number2__c = TransactionPartyUtility.updateMobileNumber2(
						transactionParty.Mobile_Number__c
					);
					transactionParty.Party_Status_Date__c = partyStatusDate;
					transactionParty.Initiate_marketing_comms__c = true;
					tpInsertList.add(transactionParty);
				} else {
					if (
						transactionParty.Role__c ==
						Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
					) {
						rpParty = transactionParty;
						transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
					} else {
						transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;
					}
					transactionParty.Party_Status_Date__c = partyStatusDate;
					transactionParty.Initiate_marketing_comms__c = true;
					tpUpdateList.add(transactionParty);
				}
			}

			List<Transaction_Party__c> tpRemoveList = getRemovedList(
				request.txDetails.Id,
				transactionPartyIds
			);
			update tpRemoveList;

			//New Tx
			newTx.Id = request.txDetails.Id;
			newTx.Comments__c = request.txDetails.Comments__c;
			newTx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS;
			newTx.Review_In_Progress__c = false;
			newTx.Date_Of_Transfer__c = request.txDetails.Date_Of_Transfer__c;
			newTx.Party_Name_Requesting_Change__c = rpParty.Party_Name__c;
			newTx.Revision_Number__c = TransactionPartyUtility.getNextRevisionNumber(
				request.txDetails.Id
			);
			newTx.Submission_Date__c = System.now();

			if (
				!tpRemoveList.isEmpty() ||
				!tpInsertList.isEmpty() ||
				!tpUpdateList.isEmpty()
			) {
				newTx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RESUBMISSION;
			} else {
				newTx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RESUBMISSION_NO_CHANGE;
			}

			update newTx;
			insert tpInsertList;
			update tpUpdateList;
			//add review links to parties
			transactionParties = RenterTransferController.addReviewLinkToParties(
				tpInsertList
			);
			update transactionParties;

			//SMS ON RESUBMISSION EDITED

			if (
				newTx.Sub_Status__c ==
				Constants.TRANSACTION_SUB_STATUS_RESUBMISSION &&
				newTx.Status__c ==
				Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
			) {
				for (Transaction_Party__c renter : [
					SELECT Id, Renter__c, Mobile_Number__c, Email_Address__c
					FROM Transaction_Party__c
					WHERE
						Transaction__c = :newTx.Id
						AND Party_Status__c = :Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED
				]) {
					if (String.isNotBlank(renter.Mobile_Number__c)) {
						MCJourneyController.fireJourneyEntry(
							Constants.MC_JOURNEY_RENTER_RESUBMIT_EDITED_SMS_NOTIFICATIONS_TO_RENTERS,
							renter.Renter__c,
							new List<Id>{ renter.Id, newTx.Id, bondRecord.Id },
							renter.Id
						);
					}
				}
			}

			//SEND EMAIL AND SMS ON RESUBMISSION UNEDITED

			if (
				newTx.Sub_Status__c ==
				Constants.TRANSACTION_SUB_STATUS_RESUBMISSION_NO_CHANGE &&
				newTx.Status__c ==
				Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
			) {
				for (Transaction_Party__c renter : [
					SELECT Id, Renter__c, Mobile_Number__c, Email_Address__c
					FROM Transaction_Party__c
					WHERE
						Transaction__c = :newTx.Id
						AND Party_Status__c = :Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED
				]) {
					if (String.isBlank(renter.Email_Address__c)) {
						Logger.error('Missing email address').setRecord(renter);
						Logger.saveLog();
					} else {
						MCJourneyController.fireJourneyEntry(
							Constants.MC_JOURNEY_RENTER_RESUBMIT_UNEDITED_EMAIL_NOTIFICATIONS_TO_RENTERS,
							renter.Renter__c,
							new List<Id>{ renter.Id, newTx.Id, bondRecord.Id },
							renter.Id
						);
					}
					if (String.isBlank(renter.Mobile_Number__c)) {
						Logger.error('Missing mobile number').setRecord(renter);
						Logger.saveLog();
					} else {
						MCJourneyController.fireJourneyEntry(
							Constants.MC_JOURNEY_RENTER_RESUBMIT_UNEDITED_SMS_NOTIFICATIONS_TO_RENTERS,
							renter.Renter__c,
							new List<Id>{ renter.Id, newTx.Id, bondRecord.Id },
							renter.Id
						);
					}
				}
			}

			TransactionPartyUtility.upsertRentersPersonAccount(
				transactionParties
			);

			List<Transaction_Review__c> reviews = deactivateReviews(
				originalTransaction.Transaction_Reviews__r
			);
			Transaction_Review__c newTransactionReview = createNewTransactionReview(
				originalTransaction,
				newTx,
				tpUpdateList,
				tpInsertList,
				tpRemoveList
			);
			if (!reviews.isEmpty()) {
				newTransactionReview.Change_Requested_By_Renter__c = reviews[0]
					.Id;
			}
			reviews.add(newTransactionReview);
			upsert reviews;
		} catch (Exception e) {
			Database.rollback(sp);
			throw AuraHelper.throwToAura(e.getMessage(), e);
		}

		Transaction__c transferTxReturn = RentalProviderTransferController.transactionToReturn(
			newTx
		);

		submitResult.transactionNumber = transferTxReturn.Name;
		submitResult.success = Label.Renter_Transfer_Message_When_Successful_Transfer_Resubmission;

		return submitResult;
	}

	@AuraEnabled(Cacheable=false)
	public static TransferSubmissionResponse validateUpdateTransaction(
		ResubmissionRequest request
	) {
		TransferSubmissionResponse result = new TransferSubmissionResponse();

		Bond__c bondRecord = BondSummaryRentalProviderController.getBondData(
			request.txDetails.Bond__c
		);

		validateBondSuspended(bondRecord, ERROR_BOND_SUSPENDED);

		Transaction__c transactionRecord = TransferRpReviewController.getTransaction(
			request.txDetails.Id
		);

		TransferRenterReviewController.validateRPCancelled(
			transactionRecord,
			ERROR_RP_CANCELLED
		);
		TransferRenterReviewController.validateTCRejected(
			transactionRecord,
			ERROR_TC_REJECTED
		);
		TransferRenterReviewController.validateEmailBounced(
			transactionRecord,
			ERROR_EMAIL_BOUNCED
		);

		TransferRenterReviewController.validateFinalised(
			transactionRecord,
			ERROR_FINALISED
		);

		TransferRenterReviewController.validateRevisionNumber(
			transactionRecord,
			Integer.valueOf(request.txDetails.Revision_Number__c),
			ERROR_REVISION_UPDATED
		);

		TransferRenterReviewController.validateTimedOut(
			transactionRecord,
			ERROR_TIME_OUT
		);

		if (isTransactionPendingWithRP(transactionRecord)) {
			return result;
		}

		return updateTransaction(request);
	}

	@AuraEnabled(Cacheable=false)
	public static TransferSubmissionResponse updateTransaction(
		ResubmissionRequest request
	) {
		TransferSubmissionResponse result = new TransferSubmissionResponse();

		Transaction__c transferTx = new Transaction__c();
		Transaction__c originalTransaction = TransferRpReviewController.getTransaction(
			request.txDetails.Id
		);
		List<Transaction_Party__c> tpInsertList = new List<Transaction_Party__c>();
		List<Transaction_Party__c> tpUpdateList = new List<Transaction_Party__c>();

		Set<Id> transactionPartyIds = new Set<Id>();

		Savepoint sp = Database.setSavepoint();

		Datetime statusDate = System.now();

		try {
			for (Transaction_Party__c transactionParty : request.tpDetails) {
				transactionPartyIds.add(transactionParty.Id);

				if (transactionParty.Id == null) {
					tpInsertList.add(
						setIncomingPartiesForUpdate(
							transactionParty,
							statusDate,
							request.txDetails.Id
						)
					);
				} else {
					tpUpdateList.add(
						setExistingPartiesForUpdate(
							transactionParty,
							statusDate
						)
					);
				}
			}

			List<Transaction_Party__c> tpRemoveList = getRemovedList(
				request.txDetails.Id,
				transactionPartyIds
			);

			transferTx = setUpdatedTransaction(request.txDetails, statusDate);

			update transferTx;
			update tpRemoveList;
			insert tpInsertList;
			update tpUpdateList;
			//add review links to parties
			List<Transaction_Party__c> transactionParties = RenterTransferController.addReviewLinkToParties(
				tpInsertList
			);
			update transactionParties;

			TransactionPartyUtility.upsertRentersPersonAccount(
				transactionParties
			);

			WITHOUT_SHARING.upsertTransactionParties(transactionParties);

			List<Transaction_Review__c> reviews = deactivateReviews(
				originalTransaction.Transaction_Reviews__r
			);
			reviews.add(
				createNewTransactionReview(
					originalTransaction,
					transferTx,
					tpUpdateList,
					tpInsertList,
					tpRemoveList
				)
			);
			upsert reviews;
		} catch (Exception e) {
			Database.rollback(sp);
			throw AuraHelper.throwToAura(e.getMessage(), e);
		}

		Transaction__c transferTxReturn = RentalProviderTransferController.transactionToReturn(
			transferTx
		);

		result.transactionNumber = transferTxReturn.Name;
		result.success = SUCCESS_MESSAGE;

		return result;
	}

	private static List<Transaction_Party__c> getRemovedList(
		Id txId,
		Set<Id> transactionPartyIds
	) {
		//Removed tp's in review
		List<Transaction_Party__c> tpRemoveList = [
			SELECT
				Id,
				Name,
				Renter__c,
				Role__c,
				First_Name__c,
				Last_Name__c,
				Renter_Type__c,
				Company_Name__c,
				Mobile_Number__c,
				Email_Address__c,
				Date_of_Birth__c,
				Mobile_Number2__c,
				Party_Status__c,
				Party_Name__c,
				Renter_position__c
			FROM Transaction_Party__c
			WHERE
				Transaction__c = :txId
				AND Id NOT IN :transactionPartyIds
				AND Role__c = :Constants.TRANSACTION_PARTY_ROLE_RENTER
				AND Party_Status__c != :Constants.TRANSACTION_PARTY_STATUS_REMOVED
		];
		for (Transaction_Party__c transactionParty : tpRemoveList) {
			transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_REMOVED;
			transactionParty.Reason_for_Removal__c = 'RP updated tp in review';
		}

		return tpRemoveList;
	}

	private static void validateTxBeforeResubmit(Transaction__c transferTx) {
		if (ClaimRPReviewController.isCancelledByOtherRP(transferTx)) {
			throw AuraHelper.throwToAura(
				Label.RP_Review_Message_When_Resubmit_If_Transaction_Cancelled
			);
		}
		if (ClaimRPReviewController.isEmailBounced(transferTx)) {
			throw AuraHelper.throwToAura(
				Label.RP_Review_Claim_Message_On_Resubmission_If_Email_Bounced
			);
		}
		if (ClaimRPReviewController.isTimedOut(transferTx)) {
			throw AuraHelper.throwToAura(
				Label.RP_Review_Claim_Message_On_Resubmission_If_Timed_Out
			);
		}
		if (ClaimRPReviewController.isPendingWithRenters(transferTx)) {
			throw AuraHelper.throwToAura(
				Label.RP_Review_Message_When_Resubmit_If_Pending_With_Renters
			);
		}
	}

	private static void validateBondStatus(Id bondId) {
		List<RenterTransferController.ValidationResponse> validationResult = RenterTransferController.validateBond(
			new List<Id>{ bondId }
		);
		if (validationResult == null || validationResult.isEmpty()) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}
		if (validationResult[0].errorMessage != null) {
			throw AuraHelper.throwToAura(validationResult[0].errorMessage);
		}
	}

	private static void validateInput(Id recordId) {
		if (recordId.getSObjectType() != Schema.Transaction__c.SObjectType) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}
	}

	@TestVisible
	private static void validateStatuses(
		Transaction__c transactionRecord,
		String submissionType
	) {
		if (
			submissionType == SUBMISSION_TYPE_RESUBMIT &&
			!VALID_TRANSACTION_STATUSES_FOR_RESUBMIT.contains(
				transactionRecord.Status__c
			)
		) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}

		if (
			submissionType == SUBMISSION_TYPE_UPDATE &&
			!VALID_TRANSACTION_STATUSES_FOR_UPDATE.contains(
				transactionRecord.Status__c
			)
		) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}
	}

	@TestVisible
	private static void validateUploadedDocuments(
		Transaction__c transactionRecord
	) {
		if (
			FileUploadAndPreviewController.getRelatedFiles(
				transactionRecord.Id
			) != null
		) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}
	}

	@TestVisible
	private static void validateTransactionType(
		Transaction__c transactionRecord
	) {
		if (
			transactionRecord.RecordTypeId !=
			Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER ||
			transactionRecord.Type__c !=
			Constants.TRANSACTION_TYPE_RENTER_TRANSFER
		) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}
	}

	@TestVisible
	private static void validatePermission(String submissionType) {
		if (
			submissionType == SUBMISSION_TYPE_RESUBMIT &&
			!UserManagementUtility.checkPermission(
				Constants.CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION
			)
		) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}
	}

	@TestVisible
	private static void validateBondDetailsChanged(
		Bond__c bondRecord,
		Map<Id, Datetime> oldModifiedDateTimeMap
	) {
		if (bondRecord == null) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}

		Map<Id, Datetime> newModifiedDateTimeMap = RentalProviderTransferController.getModifiedDateTimeMap(
			bondRecord
		);
		if (
			RentalProviderTransferController.isBondDetailsChanged(
				oldModifiedDateTimeMap,
				newModifiedDateTimeMap
			)
		) {
			throw AuraHelper.throwToAura(
				Label.Renter_Transfer_Message_When_Bond_Details_Changed_In_Resubmission
			);
		}
	}

	@AuraEnabled(Cacheable=false)
	public static TransferSubmissionResponse handleRenterTransferResubmit(
		ResubmissionRequest request
	) {
		//Query bond and further validation for related bond
		Bond__c bondRecord = BondSummaryRentalProviderController.getBondData(
			request.txDetails.Bond__c
		);
		validateBondStatus(bondRecord.Id);
		validateBondDetailsChanged(bondRecord, request.modifiedDateTimeMap);

		TransferSubmissionResponse submitResult = new TransferSubmissionResponse();
		Savepoint sp = Database.setSavepoint();
		Transaction__c transferTx = new Transaction__c();
		try {
			transferTx = RenterTransferController.createTransaction(
				bondRecord,
				RenterTransferController.TRANSFER_OPTIONS_RENTER_TRANSFER
			);
			transferTx.Comments__c = request.txDetails.Comments__c;
			transferTx.Date_Of_Transfer__c = request.txDetails.Date_Of_Transfer__c;
			insert transferTx;

			List<Transaction_Party__c> transactionParties = createTransactionParties(
				transferTx,
				request.tpDetails
			);
			WITHOUT_SHARING.upsertTransactionParties(transactionParties);

			//add review links to parties
			transactionParties = RenterTransferController.addReviewLinkToParties(
				transactionParties
			);
			update transactionParties;

			// Create/Update Renters Person Account
			TransactionPartyUtility.upsertRentersPersonAccount(
				transactionParties
			);

			if (
				transferTx.Status__c ==
				Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
			) {
				for (Transaction_Party__c transactionParty : [
					SELECT Id, Renter__c, Mobile_Number__c, Email_Address__c
					FROM Transaction_Party__c
					WHERE
						Transaction__c = :transferTx.Id
						AND Party_Status__c = :Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED
						AND ((Email_Address__c != NULL
						AND Email_Address__c != '')
						OR (Mobile_Number__c != NULL
						AND Mobile_Number__c != ''))
				]) {
					if (String.isNotBlank(transactionParty.Mobile_Number__c)) {
						// SMS Notification Rp Resubmit On Cancellation/timeout

						MCJourneyController.fireJourneyEntry(
							Constants.MC_JOURNEY_RENTER_RESUBMIT_CANCEL_OR_TIMEOUT_SMS_NOTIFICATIONS_TO_RENTERS,
							transactionParty.Renter__c,
							new List<Id>{
								transactionParty.Id,
								transferTx.Id,
								bondRecord.Id
							},
							transactionParty.Id
						);
					}
					if (String.isNotBlank(transactionParty.Email_Address__c)) {
						MCJourneyController.fireJourneyEntry(
							Constants.MC_JOURNEY_NAME_RENTER_TRANSFER_RESUBMIT_FIRST_EMAIL,
							transactionParty.Renter__c,
							new List<Id>{
								transactionParty.Id,
								transferTx.Id,
								bondRecord.Id
							},
							transactionParty.Id
						);
					}
				}
			}
		} catch (Exception e) {
			Database.rollback(sp);
			throw AuraHelper.throwToAura(e.getMessage(), e);
		}

		Transaction__c transferTxReturn = RentalProviderTransferController.transactionToReturn(
			transferTx
		);

		submitResult.transactionNumber = transferTxReturn.Name;
		submitResult.success = Label.Renter_Transfer_Message_When_Successful_Transfer_Resubmission;

		return submitResult;
	}

	private static List<Transaction_Party__c> createTransactionParties(
		Transaction__c transferTx,
		List<Transaction_Party__c> revisedParties
	) {
		List<Transaction_Party__c> newTransactionPartiesList = new List<Transaction_Party__c>();
		for (Transaction_Party__c tp : revisedParties) {
			if (
				tp.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER ||
				tp.Party_Status__c == Constants.TRANSACTION_PARTY_STATUS_REMOVED
			) {
				continue;
			}
			Transaction_Party__c newRenter = tp.clone(false, true);
			newRenter.Transaction__c = transferTx.Id;
			newRenter.Mobile_Number2__c = TransactionPartyUtility.updateMobileNumber2(
				newRenter.Mobile_Number__c
			);
			newRenter.Party_Status_Date__c = System.now();
			newRenter.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;
			newTransactionPartiesList.add(newRenter);
		}

		Transaction_Party__c txPartyForRentalProvider = TransactionPartyUtility.createTransactionPartyForRentalProvider(
			RentalProviderTransferController.currentUser,
			transferTx.Id
		);
		txPartyForRentalProvider.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;

		newTransactionPartiesList.add(txPartyForRentalProvider);
		return newTransactionPartiesList;
	}

	private static void validateBondSuspended(
		Bond__c bond,
		String errorMessage
	) {
		if (bond.Status__c == Constants.BOND_STATUS_SUSPENDED) {
			throw AuraHelper.throwToAura(errorMessage);
		}
	}

	private static Boolean isTransactionPendingWithRP(
		Transaction__c transactionRecord
	) {
		if (
			transactionRecord.Status__c ==
			Constants.TRANSACTION_STATUS_PENDING_WITH_RP
		) {
			return true;
		}

		return false;
	}

	private static Transaction__c setUpdatedTransaction(
		Transaction__c txDetails,
		Datetime statusDate
	) {
		Transaction__c transferTx = new Transaction__c();

		transferTx.Id = txDetails.Id;
		transferTx.Comments__c = txDetails.Comments__c;
		transferTx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS;
		transferTx.Review_In_Progress__c = false;
		transferTx.Date_Of_Transfer__c = txDetails.Date_Of_Transfer__c;
		transferTx.Revision_Number__c = TransactionPartyUtility.getNextRevisionNumber(
			txDetails.Id
		);
		transferTx.Submission_Date__c = statusDate;

		return transferTx;
	}

	private static Transaction_Party__c setIncomingPartiesForUpdate(
		Transaction_Party__c transactionParty,
		Datetime partyStatusDate,
		Id transactionId
	) {
		transactionParty.Transaction__c = transactionId;
		transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;
		transactionParty.Mobile_Number2__c = TransactionPartyUtility.updateMobileNumber2(
			transactionParty.Mobile_Number__c
		);
		transactionParty.Party_Status_Date__c = partyStatusDate;
		transactionParty.Initiate_marketing_comms__c = true;

		return transactionParty;
	}

	private static Transaction_Party__c setExistingPartiesForUpdate(
		Transaction_Party__c transactionParty,
		Datetime partyStatusDate
	) {
		if (
			transactionParty.Role__c ==
			Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
		) {
			transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		} else {
			transactionParty.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;
		}
		transactionParty.Party_Status_Date__c = partyStatusDate;
		transactionParty.Initiate_marketing_comms__c = true;

		return transactionParty;
	}

	/**
	 * Creates a new Transaction Review record for the updated Transaction record.
	 *
	 * @param retrievedTransaction - The original Transaction record.
	 * @param updatedTransaction - The updated Transaction record.
	 * @param stayingRenterList - The list of renters that are staying in the updated Transaction.
	 * @param newlyAddedRenters - The list of renters that are newly added to the updated Transaction.
	 * @param removedRenters - The list of renters that are removed from the updated Transaction.
	 */
	private static Transaction_Review__c createNewTransactionReview(
		Transaction__c retrievedTransaction,
		Transaction__c updatedTransaction,
		List<Transaction_Party__c> stayingRenterList,
		List<Transaction_Party__c> newlyAddedRenters,
		List<Transaction_Party__c> removedRenters
	) {
		Id rpId = findRpId(retrievedTransaction.Transaction_Parties__r);
		AuraHelper.failIf(rpId == null, 'RP not found');

		Transaction_Review__c transactionReview = new Transaction_Review__c();
		transactionReview.Transaction__c = retrievedTransaction.Id;
		transactionReview.Active_Review__c = true;
		transactionReview.Comments__c = updatedTransaction.Comments__c;
		transactionReview.Submission_Date__c = Datetime.now();
		transactionReview.Transaction_Party__c = rpId;
		transactionReview.Reviewed_By_Transaction_Party__c = rpId;
		transactionReview.Revision__c = Constants.TRANSACTION_REVIEW_REVISION_AMENDED;

		//find Transaction__c differences
		if (
			retrievedTransaction.Date_Of_Transfer__c !=
			updatedTransaction.Date_Of_Transfer__c
		) {
			transactionReview.Date_Of_Transfer__c = retrievedTransaction.Date_Of_Transfer__c;
			transactionReview.Revised_Date_Of_Transfer__c = updatedTransaction.Date_Of_Transfer__c;
		}

		//find TP differences
		Map<Id, Transaction_Party__c> retrievedTransactionParties = new Map<Id, Transaction_Party__c>(
			retrievedTransaction.Transaction_Parties__r
		);

		// Process staying renters
		Integer existingTpIdx = 0;
		for (Transaction_Party__c transactionParty : stayingRenterList) {
			if (
				transactionParty.Role__c !=
				Constants.TRANSACTION_PARTY_ROLE_RENTER
			) {
				continue; //skip RP
			}
			Map<SObjectField, SObjectField> fieldsToCheck = RENTER_N_FIELD_TO_REVIEW_OLD_VALUE[
				existingTpIdx
			];
			Map<SObjectField, SObjectField> reviewOldToRevisedField = TransactionTrackHistoryController.RENTER_N_FIELD_TO_REVISED[
				existingTpIdx
			];
			for (SObjectField tpField : fieldsToCheck.keySet()) {
				Object oldValue = retrievedTransactionParties.get(
						transactionParty.Id
					)
					.get(tpField);
				Object newValue = transactionParty.get(tpField);
				if (oldValue == newValue) {
					continue;
				}
				SObjectField reviewOldField = fieldsToCheck.get(tpField);
				SObjectField reviewRevisedField = reviewOldToRevisedField.get(
					reviewOldField
				);
				oldValue = parseDateForSObjectField(oldValue, reviewOldField);
				newValue = parseDateForSObjectField(
					newValue,
					reviewRevisedField
				);
				transactionReview.put(reviewOldField, oldValue);
				transactionReview.put(reviewRevisedField, newValue);
			}
			existingTpIdx++;
		}

		Integer tpIndex = existingTpIdx;
		// Process removed renters
		for (Transaction_Party__c transactionParty : removedRenters) {
			Map<SObjectField, SObjectField> fieldsToCheck = RENTER_N_FIELD_TO_REVIEW_OLD_VALUE[
				tpIndex
			];
			for (SObjectField tpField : fieldsToCheck.keySet()) {
				Object oldValue = transactionParty.get(tpField);
				SObjectField reviewOldField = fieldsToCheck.get(tpField);
				oldValue = parseDateForSObjectField(oldValue, reviewOldField);
				transactionReview.put(reviewOldField, oldValue);
			}
			tpIndex++;
		}

		tpIndex = existingTpIdx;
		// Process newly added renters
		for (Transaction_Party__c transactionParty : newlyAddedRenters) {
			Map<SObjectField, SObjectField> fieldsToCheck = RENTER_N_FIELD_TO_REVIEW_OLD_VALUE[
				tpIndex
			];
			Map<SObjectField, SObjectField> reviewOldToRevisedField = TransactionTrackHistoryController.RENTER_N_FIELD_TO_REVISED[
				tpIndex
			];
			for (SObjectField tpField : fieldsToCheck.keySet()) {
				Object newValue = transactionParty.get(tpField);
				SObjectField reviewOldField = fieldsToCheck.get(tpField);
				SObjectField reviewRevisedField = reviewOldToRevisedField.get(
					reviewOldField
				);
				newValue = parseDateForSObjectField(
					newValue,
					reviewRevisedField
				);
				transactionReview.put(reviewRevisedField, newValue);
			}
			tpIndex++;
		}

		return transactionReview;
	}

	/**
	 * LWCs sometimes submit Datetime values for fields that are defined as Date in Salesforce.
	 * This strips the time component if necessary.
	 *
	 * @param value - The Object anticipated to be a Datetime.
	 * @param field - The SObjectField intended for the value.
	 * @return Object - If the input is a valid Datetime and the field is of type Date,
	 *                  the method returns the converted Date; otherwise, it returns
	 *                  the original value.
	 */
	@TestVisible
	private static Object parseDateForSObjectField(
		Object value,
		SObjectField field
	) {
		if (
			value == null ||
			!(value instanceof Datetime) ||
			field?.getDescribe()?.getType() != Schema.DisplayType.DATE
		) {
			return value;
		}

		return Date.valueOf(value);
	}

	private static Id findRpId(List<Transaction_Party__c> transactionParties) {
		for (Transaction_Party__c transactionParty : transactionParties) {
			if (
				transactionParty.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				return transactionParty.Id;
			}
		}
		return null;
	}

	private static List<Transaction_Review__c> deactivateReviews(
		List<Transaction_Review__c> reviews
	) {
		List<Transaction_Review__c> reviewsToUpdate = new List<Transaction_Review__c>();
		if (reviews != null) {
			for (Transaction_Review__c review : reviews) {
				review.Active_Review__c = false;
				reviewsToUpdate.add(review);
			}
		}
		return reviewsToUpdate;
	}

	private without sharing class WithoutSharingHelper {
		void upsertTransactionParties(
			List<Transaction_Party__c> transactionParties
		) {
			upsert transactionParties;
		}
	}

	public class QueryResponse {
		@AuraEnabled
		public Transaction__c txDetails { get; set; }
		@AuraEnabled
		public Map<Id, Datetime> modifiedDateTimeMap { get; set; }
	}

	public class TransferSubmissionResponse {
		//Successful message
		@AuraEnabled
		public String success;

		//Newly created transaction number
		@AuraEnabled
		public String transactionNumber;
	}

	public class ResubmissionRequest {
		@AuraEnabled
		public Transaction__c txDetails { get; set; }
		@AuraEnabled
		public List<Transaction_Party__c> tpDetails { get; set; }
		@AuraEnabled
		public Map<Id, Datetime> modifiedDateTimeMap { get; set; }
	}
}