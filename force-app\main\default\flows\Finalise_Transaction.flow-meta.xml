<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>To finalize a Bond Merge Action for the bond and bond parties, including updating the status of the current RP bond party to &apos;Inactive&apos; and creating a new bond party for the &apos;TO&apos; rental provider.</description>
        <name>Finalise_a_Bond_Merge_Action</name>
        <label>Finalise a Bond Merge Action</label>
        <locationX>6914</locationX>
        <locationY>1406</locationY>
        <actionName>BondMergeInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_BM_Finalised_Notification</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>bondId</name>
            <value>
                <elementReference>Get_BM_Transaction_Record.Bond__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>newRpId</name>
            <value>
                <elementReference>Get_Transferee_BM.Rental_Provider__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transactionId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>BondMergeInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Transfer_bonds_to_new_RP</name>
        <label>Transfer bonds to new RP</label>
        <locationX>4274</locationX>
        <locationY>1622</locationY>
        <actionName>TransfereeReviewController</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RPT_Finalised_Notification_to_RP</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>newRpId</name>
            <value>
                <elementReference>Get_Transferee_RP.Rental_Provider__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transactionId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>TransfereeReviewController</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Claim Finalised Notification</description>
        <name>Trigger_BC_Finalised_Notification</name>
        <label>Trigger BC Finalised Notification</label>
        <locationX>182</locationX>
        <locationY>1598</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_BC_Finalised</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Bond Merge Transaction Finalised Notification</description>
        <name>Trigger_BM_Finalised_Notification</name>
        <label>Trigger BM Finalised Notification</label>
        <locationX>6914</locationX>
        <locationY>1514</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_BM_Finalised</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRPType</name>
            <value>
                <stringValue>Transferor</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Rental Provider Transfer Transaction Finalised Notification to Renters (from Bond parties)</description>
        <name>Trigger_RPT_Finalised_Notification_to_Renters</name>
        <label>Trigger RPT Finalised Notification to Renters</label>
        <locationX>4274</locationX>
        <locationY>1946</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RPT_Finalised_Notification_to_Renters_Mail</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Finalised</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Rental Provider Transfer Transaction Finalised Notification to Renters (from Bond parties)</description>
        <name>Trigger_RPT_Finalised_Notification_to_Renters_Mail</name>
        <label>Trigger RPT Finalised Notification to Renters Mail</label>
        <locationX>4274</locationX>
        <locationY>2054</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RPT_Finalised_Notification_to_Transferor</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Finalised_RP_Transferee_Mail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Rental Provider Transfer Transaction Finalised Notification to Rental Provider (from Transaction Parties)</description>
        <name>Trigger_RPT_Finalised_Notification_to_RP</name>
        <label>Trigger RPT Finalised Notification to RP</label>
        <locationX>4274</locationX>
        <locationY>1730</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RPT_Finalised_Notification_to_RP_Transferee_Mail</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Finalised</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Rental Provider Transfer Transaction Finalised Notification to Rental Provider Transferee (from Transaction Parties) for Origin Mail</description>
        <name>Trigger_RPT_Finalised_Notification_to_RP_Transferee_Mail</name>
        <label>Trigger RPT Finalised Notification to RP Transferee Mail</label>
        <locationX>4274</locationX>
        <locationY>1838</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RPT_Finalised_Notification_to_Renters</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>extraFieldsJson</name>
            <value>
                <elementReference>paperFormRPTExtraFieldsFormula</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Finalised_RP_Transferee_Mail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRPType</name>
            <value>
                <stringValue>Transferee</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Rental Provider Transfer Transaction Finalised Notification to Transferor</description>
        <name>Trigger_RPT_Finalised_Notification_to_Transferor</name>
        <label>Trigger RPT Finalised Notification to Transferor</label>
        <locationX>4274</locationX>
        <locationY>2162</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Finalised_Transferor</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRPType</name>
            <value>
                <stringValue>Transferor</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Renter Transfer Transaction Finalised Notification to Renters</description>
        <name>Trigger_RT_Finalised_Notification_to_Renters</name>
        <label>Trigger RT Finalised Notification to Renters</label>
        <locationX>2426</locationX>
        <locationY>2426</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RT_Finalised</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRenterPositions</name>
            <value>
                <elementReference>TargetRenterPositionforRTNotification</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Renter Transfer Transaction Finalised Notification to RP</description>
        <name>Trigger_RT_Finalised_Notification_to_RP</name>
        <label>Trigger RT Finalised Notification to RP</label>
        <locationX>2426</locationX>
        <locationY>2318</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RT_Finalised_Notification_to_Renters</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RT_Finalised</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Renter Transfer Transaction Finalised Notification to RP</description>
        <name>Trigger_RT_Finalised_Notification_to_RP_Paper_Form</name>
        <label>Trigger RT Finalised Notification to RP - Paper Form</label>
        <locationX>1370</locationX>
        <locationY>2426</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RT_Finalised_SMS_Notification_Paper_Form</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RT_Finalised_RP</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Renter Transfer Transaction Finalised Notification to Renters</description>
        <name>Trigger_RT_Finalised_Notification_to_Staying_Incoming_Paper_FormRenters</name>
        <label>Trigger RT Finalised Notification to Staying &amp; Incoming Paper FormRenters</label>
        <locationX>1370</locationX>
        <locationY>2318</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RT_Finalised_Notification_to_RP_Paper_Form</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RT_Finalised_Staying_Incoming</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRenterPositions</name>
            <value>
                <elementReference>TargetRenterPositionforRTNotification</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Trigger_RT_Finalised_SMS_Notification_Paper_Form</name>
        <label>Trigger RT Finalised SMS Notification - Paper Form</label>
        <locationX>1370</locationX>
        <locationY>2534</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RT_Finalised_SMS</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRenterPositions</name>
            <value>
                <elementReference>TargetRenterPositionforRTNotification</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Add Target renter position into list</description>
        <name>Add_Target_Renter_Position_For_RT_Notification</name>
        <label>Add Target Renter Position For RT Notification</label>
        <locationX>1898</locationX>
        <locationY>2102</locationY>
        <assignmentItems>
            <assignToReference>TargetRenterPositionforRTNotification</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Incoming</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>TargetRenterPositionforRTNotification</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Staying</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_for_Transaction_Origin</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Account_Values</name>
        <label>Assign Account Values</label>
        <locationX>19498</locationX>
        <locationY>2486</locationY>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.FirstName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.First_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.LastName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Family_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.PersonEmail</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Email_Address__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.Company_Name__pc</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Company_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.PersonBirthdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Date_of_Birth__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.PersonMobilePhone</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Mobile_Number__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Account_Records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Account_Values_Company</name>
        <label>Assign Account Values</label>
        <locationX>20026</locationX>
        <locationY>2486</locationY>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.LastName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Company_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.PersonEmail</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Email_Address__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.Company_Name__pc</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Company_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.PersonBirthdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Date_of_Birth__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_over_Account_Details.PersonMobilePhone</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Mobile_Number__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Account_Records_Company</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Bond_Type</name>
        <label>Assign Bond Type</label>
        <locationX>4274</locationX>
        <locationY>1514</locationY>
        <assignmentItems>
            <assignToReference>v_paperFormRPTBondType</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Bond_Type.Type__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Transfer_bonds_to_new_RP</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_record_to_Collection</name>
        <label>Assign record to Collection</label>
        <locationX>19410</locationX>
        <locationY>2054</locationY>
        <assignmentItems>
            <assignToReference>rec_UpdatedBondParties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Person_Account</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Renter_Details</name>
        <label>Assign Renter Details</label>
        <locationX>17694</locationX>
        <locationY>1838</locationY>
        <assignmentItems>
            <assignToReference>All_Renter_Names</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>RenterDetails</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_the_Renter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_RP_to_Collection</name>
        <label>Assign RP to Collection</label>
        <locationX>17714</locationX>
        <locationY>1406</locationY>
        <assignmentItems>
            <assignToReference>rec_TP_Details</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Transaction_Party_Details_for_RP</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Bond_Details_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Value_to_Bond</name>
        <label>Assign Value to Bond</label>
        <locationX>14834</locationX>
        <locationY>3710</locationY>
        <assignmentItems>
            <assignToReference>Get_Bond_Details.Street_Address__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Street_Address__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Bond_Details.Suburb__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Suburb__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Bond_Details.State__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.State__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Bond_Details.Postcode__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Postcode__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Bond_Details.Additional_Address_Details__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Additional_Address_Details__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Get_Bond_Details.Country__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Country__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Bond</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Values_to_Bond_Party_0</name>
        <label>Assign Values to Bond Party</label>
        <locationX>19410</locationX>
        <locationY>1946</locationY>
        <assignmentItems>
            <assignToReference>Loop_Over_Bond_Parties.First_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_selected_TPs.First_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Over_Bond_Parties.Family_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_selected_TPs.Last_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Over_Bond_Parties.Company_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_selected_TPs.Company_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Over_Bond_Parties.Date_of_Birth__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_selected_TPs.Date_of_Birth__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Over_Bond_Parties.Email_Address__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_selected_TPs.Email_Address__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Loop_Over_Bond_Parties.Mobile_Number__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_selected_TPs.Mobile_Number__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_record_to_Collection</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>ch_AddressAmendments</name>
        <choiceText>Address Amendment</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Address Amendment</stringValue>
        </value>
    </choices>
    <constants>
        <description>Finalise Bond Claim MC Journey Name</description>
        <name>MC_Journey_Name_BC_Finalised</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Finalise-Bond-Claim-12532</stringValue>
        </value>
    </constants>
    <constants>
        <description>Bond Merge Transaction Finalised MC Journey Name</description>
        <name>MC_Journey_Name_BM_Finalised</name>
        <dataType>String</dataType>
        <value>
            <stringValue>InternalUser-FinalisedBondMergeTransaction-12847</stringValue>
        </value>
    </constants>
    <constants>
        <description>Rental Provider Transfer Transaction Finalised MC Journey Name</description>
        <name>MC_Journey_Name_RPT_Finalised</name>
        <dataType>String</dataType>
        <value>
            <stringValue>NotificationsFinalisedRPTransferTransaction-14786</stringValue>
        </value>
    </constants>
    <constants>
        <description>Bond Receipt to Transferee</description>
        <name>MC_Journey_Name_RPT_Finalised_RP_Transferee_Mail</name>
        <dataType>String</dataType>
        <value>
            <stringValue>PaperForm-RPTransfer-Bond Receipt-14579</stringValue>
        </value>
    </constants>
    <constants>
        <description>Specific to Transferor</description>
        <name>MC_Journey_Name_RPT_Finalised_Transferor</name>
        <dataType>String</dataType>
        <value>
            <stringValue>PaperForm-RPTransfer-Transferor Notification to Case Initiator-14576</stringValue>
        </value>
    </constants>
    <constants>
        <description>Finalised Renter Transfer Transaction Journey Name</description>
        <name>MC_Journey_Name_RT_Finalised</name>
        <dataType>String</dataType>
        <value>
            <stringValue>InternalUser-RenterTransfer-TransactionFinalised-12780</stringValue>
        </value>
    </constants>
    <constants>
        <name>MC_Journey_Name_RT_Finalised_RP</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RTransfer-By-Agreement-Transfer-Receipt-to-RP-5044</stringValue>
        </value>
    </constants>
    <constants>
        <name>MC_Journey_Name_RT_Finalised_SMS</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RTransfer-ByAgreement-TransactionFinalisationSMS-5037</stringValue>
        </value>
    </constants>
    <constants>
        <name>MC_Journey_Name_RT_Finalised_Staying_Incoming</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RTransfer-By-Agreement-Transfer-Receipt-to-Renter-5042</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Check_for_Bond_party_Record</name>
        <label>Check for Bond party Record</label>
        <locationX>18442</locationX>
        <locationY>1622</locationY>
        <defaultConnector>
            <targetReference>Loop_Over_selected_TPs</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Bond_Party_Record_differs</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>rec_BondParties</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Over_Bond_Parties</targetReference>
            </connector>
            <label>Bond Party Record differs</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_for_Homes_Victoria_or_Private_Transaction</name>
        <label>Check for Homes Victoria or Private Transaction</label>
        <locationX>17714</locationX>
        <locationY>2114</locationY>
        <defaultConnector>
            <targetReference>Create_Bond</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Private</defaultConnectorLabel>
        <rules>
            <name>Homes_Victoria</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Funding_Source__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Homes Victoria</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Bond_HomesVictoria</targetReference>
            </connector>
            <label>Homes Victoria</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_for_Transaction_Origin</name>
        <label>Check for Transaction Origin</label>
        <locationX>1898</locationX>
        <locationY>2210</locationY>
        <defaultConnector>
            <targetReference>Trigger_RT_Finalised_Notification_to_RP</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Origin - RTBA Website</defaultConnectorLabel>
        <rules>
            <name>Origin_Mail</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_RT_Finalised_Notification_to_Staying_Incoming_Paper_FormRenters</targetReference>
            </connector>
            <label>Origin - Mail</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Account_Details_Match_Bond_Party_Record</name>
        <label>Check if Account Details Match Bond Party Record</label>
        <locationX>20026</locationX>
        <locationY>2378</locationY>
        <defaultConnector>
            <targetReference>Loop_over_Account_Details</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Record_Matches</name>
            <conditionLogic>(1 OR 2 OR 3 OR 4 OR 5 OR (6 AND 7)) AND 8</conditionLogic>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.FirstName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.First_Name__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.LastName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Family_Name__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.PersonEmail</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Email_Address__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.PersonMobilePhone</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Mobile_Number__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.PersonBirthdate</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Date_of_Birth__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.Company_Name__pc</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Company_Name__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.Company_Name__pc</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.FirstName</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Account_Values</targetReference>
            </connector>
            <label>Record Matches</label>
        </rules>
        <rules>
            <name>Company_Record</name>
            <conditionLogic>(1 AND 5 AND 6) OR 3 OR 4 OR 2</conditionLogic>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.Company_Name__pc</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.PersonMobilePhone</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Mobile_Number__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.PersonEmail</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Email_Address__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.PersonBirthdate</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Date_of_Birth__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.LastName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Bond_Parties.Family_Name__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_over_Account_Details.FirstName</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Account_Values_Company</targetReference>
            </connector>
            <label>Company Record</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Address_to_be_updated</name>
        <label>Check if Address to be updated</label>
        <locationX>15230</locationX>
        <locationY>3494</locationY>
        <defaultConnector>
            <targetReference>Finalise_Transaction_0</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Address_to_be_updated_on_Bond</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Amendments</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Bond_Details</targetReference>
            </connector>
            <label>Address to be updated on Bond</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Agreed_status_TP_exists</name>
        <label>Check if Agreed status TP exists</label>
        <locationX>17714</locationX>
        <locationY>2786</locationY>
        <defaultConnector>
            <targetReference>Finalise_Transaction_Bond_Lodgement</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Agreed_status</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>rec_TP_Details</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Through_TPs</targetReference>
            </connector>
            <label>Agreed status</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Bond_party_Record_Differs</name>
        <label>Check if Bond party Record Differs</label>
        <locationX>19674</locationX>
        <locationY>1838</locationY>
        <defaultConnector>
            <targetReference>Loop_Over_Bond_Parties</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Bond_Party_Record_does_not_differ</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Bond_Parties.Rental_Provider__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_selected_TPs.Rental_Provider__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Values_to_Bond_Party_0</targetReference>
            </connector>
            <label>Bond Party Record does not differ</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Renter_Details_exists</name>
        <label>Check if Renter Details exists</label>
        <locationX>17714</locationX>
        <locationY>1622</locationY>
        <defaultConnector>
            <targetReference>Check_for_Homes_Victoria_or_Private_Transaction</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Renter_Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>rec_RenterDetails</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Through_the_Renter</targetReference>
            </connector>
            <label>Renter Exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_the_current_TP_is_RP_or_Renter</name>
        <label>Check if the current TP is RP or Renter</label>
        <locationX>17826</locationX>
        <locationY>3002</locationY>
        <defaultConnector>
            <targetReference>Create_Bond_Party_Records_Renter</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Renter</defaultConnectorLabel>
        <rules>
            <name>Rental_Provider</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Through_TPs.Role__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Bond_Party_Records</targetReference>
            </connector>
            <label>Rental Provider</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_user_selected_records_for_Renter_Amendments</name>
        <label>Check if user selected records for Renter Amendments</label>
        <locationX>15230</locationX>
        <locationY>1298</locationY>
        <defaultConnector>
            <targetReference>Check_if_Address_to_be_updated</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Renter_Amendments_to_be_done</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>rec_SelectedTPs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Over_selected_TPs</targetReference>
            </connector>
            <label>Renter Amendments to be done</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check to ensure the user has the permissions to finalise the transaction.  This is a safety check in the event the flow is run manually and not from the action on the transaction record page.</description>
        <name>Check_Permissions</name>
        <label>Check Permissions</label>
        <locationX>16136</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Permission_Denied</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Permission Denied</defaultConnectorLabel>
        <rules>
            <name>Permission_not_Granted_RP_Transfer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Finalise_Transaction_RP_Transfer</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mail</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider Transfer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>RP_Transfer</targetReference>
            </connector>
            <label>Permission not Granted - RP Transfer</label>
        </rules>
        <rules>
            <name>Permission_Granted</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Finalise_Transaction</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider Transfer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transaction_Eligible_to_be_Finalised</targetReference>
            </connector>
            <label>Permission Granted</label>
        </rules>
        <rules>
            <name>Permission_Granted_RP_Transfer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Finalise_Transaction_RP_Transfer</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider Transfer</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transaction_Eligible_to_be_Finalised</targetReference>
            </connector>
            <label>Permission Granted - RP Transfer</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine the type of transaction, so as the correct path can be taken.</description>
        <name>Determine_the_Transaction_Type</name>
        <label>Determine the Transaction Type</label>
        <locationX>10730</locationX>
        <locationY>866</locationY>
        <defaultConnector>
            <targetReference>Transaction_Type_Cannot_Be_Cancelled</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Claims_Transaction</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claims</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Finalise_Transaction</targetReference>
            </connector>
            <label>Claims Transaction</label>
        </rules>
        <rules>
            <name>Renter_Transfer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Transfer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Finalise_Transaction_Renter_Transfer</targetReference>
            </connector>
            <label>Renter Transfer</label>
        </rules>
        <rules>
            <name>Rental_Provider_Transfer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider Transfer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Finalise_Transaction_RPT</targetReference>
            </connector>
            <label>Rental Provider Transfer</label>
        </rules>
        <rules>
            <name>Bond_Merge</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bond Merge</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Finalise_Transaction_BM</targetReference>
            </connector>
            <label>Bond Merge</label>
        </rules>
        <rules>
            <name>Bond_Amendment</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bond Amendment</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_all_Related_Transaction_Parties</targetReference>
            </connector>
            <label>Bond Amendment</label>
        </rules>
        <rules>
            <name>Bond_Lodgement</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bond Lodgement</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Renter_Details</targetReference>
            </connector>
            <label>Bond Lodgement</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if the current bond amount is $0</description>
        <name>Is_Current_Bond_Amount_0</name>
        <label>Is Current Bond Amount $0?</label>
        <locationX>182</locationX>
        <locationY>1298</locationY>
        <defaultConnector>
            <targetReference>Update_Bond_Status_Partially_Paid</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No - &gt;$0</defaultConnectorLabel>
        <rules>
            <name>Yes_CurrentBondAmt_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>NewCurrentBondAmount</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Bond_Status_Closed</targetReference>
            </connector>
            <label>Yes - $0</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if the transaction is eligible to be finalised.</description>
        <name>Transaction_Eligible_to_be_Finalised</name>
        <label>Transaction Eligible to be Finalised?</label>
        <locationX>16136</locationX>
        <locationY>542</locationY>
        <defaultConnector>
            <targetReference>Unable_To_Finalise_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Eligible</defaultConnectorLabel>
        <rules>
            <name>Eligible</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending with RTBA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Confirmation_Screen</targetReference>
            </connector>
            <label>Eligible</label>
        </rules>
    </decisions>
    <description>A flow providing the ability to finalise a transaction.</description>
    <environments>Default</environments>
    <formulas>
        <name>Bond_Party_Number</name>
        <dataType>String</dataType>
        <expression>&quot;BL&quot; &amp; TEXT(($Flow.CurrentDateTime))</expression>
    </formulas>
    <formulas>
        <name>IssueDate</name>
        <dataType>Date</dataType>
        <expression>IF(
    TEXT({!recordId.Payment_Method__c}) = &quot;Cash&quot;,
    DATETIMEVALUE(TEXT({!recordId.Payment_Date__c})),
    IF(
        TEXT({!recordId.Payment_Method__c}) = &quot;Cheque&quot;,
        DATETIMEVALUE(TEXT({!recordId.Cheque_Date__c})),
        IF(
            TEXT({!recordId.Payment_Method__c}) = &quot;Money Order&quot;,
            DATETIMEVALUE(TEXT({!recordId.Money_Order_Date__c})),
            NULL
        )
    )
)</expression>
    </formulas>
    <formulas>
        <name>paperFormRPTExtraFieldsFormula</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;Bnd_Type&quot;: &quot;&apos; &amp; {!v_paperFormRPTBondType} &amp; &apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>PaymentAmount</name>
        <dataType>Currency</dataType>
        <expression>CASE(
    {!recordId.Payment_Method__c},
    &quot;Cash&quot;, {!recordId.Cash_Amount__c},
    &quot;Cheque&quot;, {!recordId.Cheque_Amount__c},
    &quot;Money Order&quot;, {!recordId.Money_Order_Amount__c},
    0
)</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>RenterDetails</name>
        <dataType>String</dataType>
        <expression>IF(
    ISBLANK({!Loop_Through_the_Renter.Company_Name__c}),
    {!Loop_Through_the_Renter.First_Name__c} &amp; &quot; &quot; &amp; {!Loop_Through_the_Renter.Last_Name__c},
    {!Loop_Through_the_Renter.Company_Name__c}
) &amp;
IF(
    ISBLANK({!All_Renter_Names}), &quot;&quot;, &quot;, &quot;
) &amp;
{!All_Renter_Names}</expression>
    </formulas>
    <formulas>
        <name>var_RentalProviderName</name>
        <dataType>String</dataType>
        <expression>&quot;WEBL&quot; &amp; TEXT(($Flow.CurrentDateTime))</expression>
    </formulas>
    <formulas>
        <name>var_RenterName</name>
        <dataType>String</dataType>
        <expression>&quot;BL&quot; &amp; TEXT(($Flow.CurrentDateTime))</expression>
    </formulas>
    <interviewLabel>Finalise Transaction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Finalise Transaction</label>
    <loops>
        <name>Bond_Party_Actions_RTR</name>
        <label>Bond Party Actions</label>
        <locationX>1898</locationX>
        <locationY>1298</locationY>
        <collectionReference>Get_Outgoing_Renters</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Remove_Renter_from_Bond</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Incoming_Renters</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Bond_Party_Incoming_Actions</name>
        <label>Bond Party Incoming Actions</label>
        <locationX>1898</locationX>
        <locationY>1754</locationY>
        <collectionReference>rec_IncomingRenters</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Create_Bond_party_Records_for_Incoming_Renters</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Add_Target_Renter_Position_For_RT_Notification</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_over_Account_Details</name>
        <label>Loop over Account Details</label>
        <locationX>19410</locationX>
        <locationY>2270</locationY>
        <collectionReference>rec_PersonAccount</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_if_Account_Details_Match_Bond_Party_Record</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_Over_Bond_Parties</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Bond_Parties</name>
        <label>Loop Over Bond Parties</label>
        <locationX>18178</locationX>
        <locationY>1730</locationY>
        <collectionReference>rec_BondParties</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_if_Bond_party_Record_Differs</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Bond_Party_Records</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_selected_TPs</name>
        <label>Loop Over selected TPs</label>
        <locationX>15186</locationX>
        <locationY>1406</locationY>
        <collectionReference>rec_SelectedTPs</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Get_Bond_Party_Details</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_if_Address_to_be_updated</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Through_the_Renter</name>
        <label>Loop Through the Renter</label>
        <locationX>17606</locationX>
        <locationY>1730</locationY>
        <collectionReference>rec_RenterDetails</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Renter_Details</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_for_Homes_Victoria_or_Private_Transaction</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Through_TPs</name>
        <label>Loop Through TPs</label>
        <locationX>17474</locationX>
        <locationY>2894</locationY>
        <collectionReference>rec_TP_Details</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_if_the_current_TP_is_RP_or_Renter</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Finalise_Transaction_Bond_Lodgement</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Bond</name>
        <label>Create Bond</label>
        <locationX>18374</locationX>
        <locationY>2222</locationY>
        <assignRecordIdToReference>var_BondCreated.Id</assignRecordIdToReference>
        <connector>
            <targetReference>Create_payment_record_Private</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen_0</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Additional_Address_Details__c</field>
            <value>
                <elementReference>recordId.Additional_Address_Details__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Address_Line_1__c</field>
            <value>
                <elementReference>recordId.Street_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bond_Paid_By__c</field>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Country__c</field>
            <value>
                <elementReference>Country</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Current_Bond_Amount__c</field>
            <value>
                <elementReference>recordId.Bond_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DPID_Date__c</field>
            <value>
                <elementReference>recordId.DPID_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DPID_Flag__c</field>
            <value>
                <elementReference>recordId.DPID_Flag__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DPID_Source__c</field>
            <value>
                <elementReference>recordId.DPID_Source__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DPID__c</field>
            <value>
                <elementReference>recordId.DPID__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Bond_Recieved_By_Rental_Provider__c</field>
            <value>
                <elementReference>recordId.Date_Bond_Recieved_By_Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Funding_Source__c</field>
            <value>
                <elementReference>recordId.Funding_Source__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Lodgement_Amount__c</field>
            <value>
                <elementReference>recordId.Bond_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Modification_Bond_Component__c</field>
            <value>
                <elementReference>recordId.Modification_Bond_Component__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Modification_Bond_Description__c</field>
            <value>
                <elementReference>recordId.Modification_Bond_Description__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Number_of_Bedrooms__c</field>
            <value>
                <elementReference>recordId.Number_Of_Bedrooms__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Postcode__c</field>
            <value>
                <elementReference>recordId.Postcode__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Premises_Type_Other__c</field>
            <value>
                <elementReference>recordId.Premises_Type_Other__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Premises_Type__c</field>
            <value>
                <elementReference>recordId.Premises_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Record_Id.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>recordId.Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_name__c</field>
            <value>
                <elementReference>All_Renter_Names</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Source__c</field>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>State__c</field>
            <value>
                <elementReference>recordId.State__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Street_Address__c</field>
            <value>
                <elementReference>recordId.Street_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Suburb__c</field>
            <value>
                <elementReference>recordId.Suburb__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Period_months__c</field>
            <value>
                <elementReference>recordId.Tenancy_Period_months__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Start_Date__c</field>
            <value>
                <elementReference>recordId.Tenancy_Start_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Type__c</field>
            <value>
                <elementReference>recordId.Tenancy_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transaction__c</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type__c</field>
            <value>
                <elementReference>recordId.Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Voucher__c</field>
            <value>
                <elementReference>recordId.Voucher__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Weekly_Rental_Amount__c</field>
            <value>
                <elementReference>recordId.Weekly_Rental_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <object>Bond__c</object>
    </recordCreates>
    <recordCreates>
        <name>Create_Bond_HomesVictoria</name>
        <label>Create Bond</label>
        <locationX>17054</locationX>
        <locationY>2222</locationY>
        <assignRecordIdToReference>var_BondCreated.Id</assignRecordIdToReference>
        <connector>
            <targetReference>Create_payment_record</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Additional_Address_Details__c</field>
            <value>
                <elementReference>recordId.Additional_Address_Details__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Address_Line_1__c</field>
            <value>
                <elementReference>recordId.Street_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bond_Paid_By__c</field>
            <value>
                <stringValue>Homes Victoria</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Country__c</field>
            <value>
                <elementReference>Country</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Current_Bond_Amount__c</field>
            <value>
                <elementReference>recordId.Bond_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Bond_Recieved_By_Rental_Provider__c</field>
            <value>
                <elementReference>recordId.Date_Bond_Recieved_By_Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Funding_Source__c</field>
            <value>
                <elementReference>recordId.Funding_Source__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Homes_Victoria_Loan_Number__c</field>
            <value>
                <elementReference>recordId.Homes_Victoria_Loan_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Homes_Victoria_Voucher_Expiry_Date__c</field>
            <value>
                <elementReference>recordId.Homes_Victoria_Voucher_Expiry_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Homes_Victoria_Voucher_Issue_Date__c</field>
            <value>
                <elementReference>recordId.Homes_Victoria_Voucher_Issue_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Homes_Victoria_Voucher_Number__c</field>
            <value>
                <elementReference>recordId.Homes_Victoria_Voucher_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Homes_Victoria_Voucher_Status__c</field>
            <value>
                <elementReference>recordId.Homes_Victoria_Voucher_Status__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Lodgement_Amount__c</field>
            <value>
                <elementReference>recordId.Bond_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Number_of_Bedrooms__c</field>
            <value>
                <elementReference>recordId.Number_Of_Bedrooms__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Postcode__c</field>
            <value>
                <elementReference>recordId.Postcode__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Premises_Type_Other__c</field>
            <value>
                <elementReference>recordId.Premises_Type_Other__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Premises_Type__c</field>
            <value>
                <elementReference>recordId.Premises_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_Record_Id.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>recordId.Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_name__c</field>
            <value>
                <elementReference>All_Renter_Names</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Source__c</field>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>State__c</field>
            <value>
                <elementReference>recordId.State__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Street_Address__c</field>
            <value>
                <elementReference>recordId.Street_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Suburb__c</field>
            <value>
                <elementReference>recordId.Suburb__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Period_months__c</field>
            <value>
                <elementReference>recordId.Tenancy_Period_months__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Start_Date__c</field>
            <value>
                <elementReference>recordId.Tenancy_Start_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Type__c</field>
            <value>
                <elementReference>recordId.Tenancy_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transaction__c</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type__c</field>
            <value>
                <elementReference>recordId.Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Voucher__c</field>
            <value>
                <elementReference>recordId.Voucher__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Weekly_Rental_Amount__c</field>
            <value>
                <elementReference>recordId.Weekly_Rental_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <object>Bond__c</object>
    </recordCreates>
    <recordCreates>
        <name>Create_Bond_Party_Records</name>
        <label>Create Bond Party Records</label>
        <locationX>17562</locationX>
        <locationY>3110</locationY>
        <connector>
            <targetReference>Loop_Through_TPs</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen_0</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Bond__c</field>
            <value>
                <elementReference>var_BondCreated.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Commencement_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Company_Name__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email_Address__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Email_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Mobile_Number__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Mobile_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>var_RentalProviderName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Renter__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Role__c</field>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Searchable_Rented_Property_Address__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Rented_Property_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </inputAssignments>
        <object>Bond_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Bond_party_Records_for_Incoming_Renters</name>
        <label>Create Bond party Records for Incoming Renters</label>
        <locationX>1986</locationX>
        <locationY>1862</locationY>
        <connector>
            <targetReference>Bond_Party_Incoming_Actions</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Bond__c</field>
            <value>
                <elementReference>recordId.Bond__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Company_Name__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_of_Birth__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Date_of_Birth__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email_Address__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Email_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Family_Name__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Last_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>First_Name__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.First_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Mobile_Number__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Mobile_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>Bond_Party_Number</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_Type__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Renter_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter__c</field>
            <value>
                <elementReference>Bond_Party_Incoming_Actions.Renter__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Role__c</field>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </inputAssignments>
        <object>Bond_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Bond_Party_Records_Renter</name>
        <label>Create Bond Party Records Renter</label>
        <locationX>18090</locationX>
        <locationY>3110</locationY>
        <connector>
            <targetReference>Loop_Through_TPs</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen_0</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Bond__c</field>
            <value>
                <elementReference>var_BondCreated.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Company_Name__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_of_Birth__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Date_of_Birth__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email_Address__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Email_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Family_Name__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Last_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>First_Name__c</field>
            <value>
                <elementReference>Loop_Through_TPs.First_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Mobile_Number__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Mobile_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>var_RenterName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_Type__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Renter_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter__c</field>
            <value>
                <elementReference>Loop_Through_TPs.Renter__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Role__c</field>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </inputAssignments>
        <object>Bond_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_payment_record</name>
        <label>Create payment record</label>
        <locationX>17054</locationX>
        <locationY>2330</locationY>
        <connector>
            <targetReference>Update_Voucher_Status</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Update_Transaction_DML_Error_Screen_0</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Bond__c</field>
            <value>
                <elementReference>var_BondCreated.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payer__c</field>
            <value>
                <stringValue>HOMES VICTORIA</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payment_Amount__c</field>
            <value>
                <elementReference>recordId.Bond_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payment_Method__c</field>
            <value>
                <stringValue>Direct Debit</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payment_Source__c</field>
            <value>
                <stringValue>Paper</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Received_Date__c</field>
            <value>
                <elementReference>recordId.Date_Bond_Recieved_By_Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Pending</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transaction__c</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Payment__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_payment_record_Private</name>
        <label>Create payment record</label>
        <locationX>18374</locationX>
        <locationY>2330</locationY>
        <connector>
            <targetReference>Check_if_Agreed_status_TP_exists</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen_0</targetReference>
        </faultConnector>
        <inputAssignments>
            <field>Bond__c</field>
            <value>
                <elementReference>var_BondCreated.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Cheque_Drawer_Serial_Number__c</field>
            <value>
                <elementReference>recordId.Cheque_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Issue_Date__c</field>
            <value>
                <elementReference>IssueDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Money_Order_Number__c</field>
            <value>
                <elementReference>recordId.Money_Order_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payer__c</field>
            <value>
                <stringValue>RENTAL PROVIDER</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payment_Amount__c</field>
            <value>
                <elementReference>PaymentAmount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payment_Method__c</field>
            <value>
                <elementReference>recordId.Payment_Method__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Payment_Source__c</field>
            <value>
                <stringValue>Paper</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Received_Date__c</field>
            <value>
                <elementReference>recordId.Date_Bond_Recieved_By_Rental_Provider__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Pending</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transaction__c</field>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Payment__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_all_Related_Transaction_Parties</name>
        <label>Get all Related Transaction Parties</label>
        <locationX>15230</locationX>
        <locationY>974</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Transaction_Details</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Different_To_Bond_Party__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Transaction_Party__c</object>
        <outputReference>rec_Transaction_Parties</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Different_To_Bond_Party__c</queriedFields>
        <queriedFields>Bond_Number__c</queriedFields>
        <queriedFields>Party_Status__c</queriedFields>
        <queriedFields>First_Name__c</queriedFields>
        <queriedFields>Last_Name__c</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>Company_Name__c</queriedFields>
        <queriedFields>Date_of_Birth__c</queriedFields>
        <queriedFields>Email_Address__c</queriedFields>
        <queriedFields>Mobile_Number__c</queriedFields>
        <queriedFields>Rental_Provider__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Get all transaction record</description>
        <name>Get_BM_Transaction_Record</name>
        <label>Get BM Transaction Record</label>
        <locationX>6914</locationX>
        <locationY>1298</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Finalise_a_Bond_Merge_Action</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Transaction__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Bond_Details</name>
        <label>Get Bond Details</label>
        <locationX>14834</locationX>
        <locationY>3602</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Value_to_Bond</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Transaction_Details.Bond__r.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Bond__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Bond_Details_0</name>
        <label>Get Bond Details</label>
        <locationX>17714</locationX>
        <locationY>1514</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_if_Renter_Details_exists</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Roll_Back_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Bond__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Bond_Party_Details</name>
        <label>Get Bond Party Details</label>
        <locationX>18442</locationX>
        <locationY>1514</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_for_Bond_party_Record</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Transaction_Details.Bond__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </filters>
        <object>Bond_Party__c</object>
        <outputReference>rec_BondParties</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>First_Name__c</queriedFields>
        <queriedFields>Family_Name__c</queriedFields>
        <queriedFields>Email_Address__c</queriedFields>
        <queriedFields>Mobile_Number__c</queriedFields>
        <queriedFields>Date_of_Birth__c</queriedFields>
        <queriedFields>Company_Name__c</queriedFields>
        <queriedFields>Rental_Provider__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Required for MC Payload</description>
        <name>Get_Bond_Type</name>
        <label>Get Bond Type</label>
        <locationX>4274</locationX>
        <locationY>1406</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Bond_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Transfer_Transaction_Bond.Bond__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Bond__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Type__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieve Tps to update the respective BP</description>
        <name>Get_Incoming_Renters</name>
        <label>Get Incoming Renters</label>
        <locationX>1898</locationX>
        <locationY>1646</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Bond_Party_Incoming_Actions</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Renter_position__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Incoming</stringValue>
            </value>
        </filters>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </filters>
        <object>Transaction_Party__c</object>
        <outputReference>rec_IncomingRenters</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Email_Address__c</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>Date_of_Birth__c</queriedFields>
        <queriedFields>First_Name__c</queriedFields>
        <queriedFields>Last_Name__c</queriedFields>
        <queriedFields>Mobile_Number__c</queriedFields>
        <queriedFields>Renter__c</queriedFields>
        <queriedFields>Rental_Provider__c</queriedFields>
        <queriedFields>Renter_Type__c</queriedFields>
        <queriedFields>Company_Name__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Retrieve Tps to update the respective BP</description>
        <name>Get_Outgoing_Renters</name>
        <label>Get Outgoing Renter(s)</label>
        <locationX>1898</locationX>
        <locationY>1190</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Bond_Party_Actions_RTR</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Renter_position__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Leaving</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Person_Account</name>
        <label>Get Person Account</label>
        <locationX>19410</locationX>
        <locationY>2162</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_over_Account_Details</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Rental_Provider__r.Id</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputReference>rec_PersonAccount</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>FirstName</queriedFields>
        <queriedFields>LastName</queriedFields>
        <queriedFields>Company_Name__pc</queriedFields>
        <queriedFields>PersonBirthdate</queriedFields>
        <queriedFields>Office_Email__c</queriedFields>
        <queriedFields>Account_Name__pc</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>PersonEmail</queriedFields>
        <queriedFields>PersonMobilePhone</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>Get_Record_Id</name>
        <label>Get Record Id</label>
        <locationX>17714</locationX>
        <locationY>1082</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Transaction_Party_Details</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Roll_Back_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>RTBA Bond</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Renter_Details</name>
        <label>Get Renter Details</label>
        <locationX>17714</locationX>
        <locationY>974</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Record_Id</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <object>Transaction_Party__c</object>
        <outputReference>rec_RenterDetails</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>First_Name__c</queriedFields>
        <queriedFields>Last_Name__c</queriedFields>
        <queriedFields>Company_Name__c</queriedFields>
        <queriedFields>Renter_Type__c</queriedFields>
        <queriedFields>Transaction_Status__c</queriedFields>
        <queriedFields>Role__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>Get_Transaction_Details</name>
        <label>Get Transaction Details</label>
        <locationX>15230</locationX>
        <locationY>1082</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Renter_Amendments</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Bond__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Transaction__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Transaction_Party_Details</name>
        <label>Get Transaction Party Details</label>
        <locationX>17714</locationX>
        <locationY>1190</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Transaction_Party_Details_for_RP</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Roll_Back_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Party_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </filters>
        <object>Transaction_Party__c</object>
        <outputReference>rec_TP_Details</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Transaction_Status__c</queriedFields>
        <queriedFields>Role__c</queriedFields>
        <queriedFields>First_Name__c</queriedFields>
        <queriedFields>Last_Name__c</queriedFields>
        <queriedFields>Bond_Number__c</queriedFields>
        <queriedFields>Renter_Type__c</queriedFields>
        <queriedFields>Date_of_Birth__c</queriedFields>
        <queriedFields>Email_Address__c</queriedFields>
        <queriedFields>Mobile_Number__c</queriedFields>
        <queriedFields>Company_Name__c</queriedFields>
        <queriedFields>Rented_Property_Address__c</queriedFields>
        <queriedFields>Rental_Provider__c</queriedFields>
        <queriedFields>Renter__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <name>Get_Transaction_Party_Details_for_RP</name>
        <label>Get Transaction Party Details for RP</label>
        <locationX>17714</locationX>
        <locationY>1298</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_RP_to_Collection</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Roll_Back_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Party_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Paid</stringValue>
            </value>
        </filters>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Transfer_Transaction_Bond</name>
        <label>Get Transfer Transaction Bond</label>
        <locationX>4274</locationX>
        <locationY>1298</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Bond_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transfer_Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Transfer_Transaction_Bonds__c</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Bond__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get transferee details from bond merge transaction parties.</description>
        <name>Get_Transferee_BM</name>
        <label>Get Transferee RP</label>
        <locationX>6914</locationX>
        <locationY>1190</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_BM_Transaction_Record</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Rental_Provider_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Transferee</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Transferee_RP</name>
        <label>Get Transferee RP</label>
        <locationX>4274</locationX>
        <locationY>1190</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Transfer_Transaction_Bond</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Rental_Provider_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Transferee</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordRollbacks>
        <name>Roll_Back_Records</name>
        <label>Roll Back Records</label>
        <locationX>20750</locationX>
        <locationY>1190</locationY>
    </recordRollbacks>
    <recordRollbacks>
        <description>Roll back other changes if something fails after this point</description>
        <name>Rollback_records</name>
        <label>Rollback records</label>
        <locationX>6386</locationX>
        <locationY>1190</locationY>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </connector>
    </recordRollbacks>
    <recordUpdates>
        <description>Update the transaction record to finalise it. Set the correct status and add the provided comments.</description>
        <name>Finalise_Transaction</name>
        <label>Finalise Transaction</label>
        <locationX>182</locationX>
        <locationY>974</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Statuses</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the transaction record to finalise it. Set the correct status and add the provided comments.</description>
        <name>Finalise_Transaction_0</name>
        <label>Finalise Transaction</label>
        <locationX>15230</locationX>
        <locationY>4058</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Statuses_0</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the transaction record to finalise it. Set the correct status and add the provided comments.</description>
        <name>Finalise_Transaction_BM</name>
        <label>Finalise Transaction</label>
        <locationX>6914</locationX>
        <locationY>974</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Statuses_BM</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the transaction record to finalise it. Set the correct status and add the provided comments.</description>
        <name>Finalise_Transaction_Bond_Lodgement</name>
        <label>Finalise Transaction</label>
        <locationX>17714</locationX>
        <locationY>3518</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Statuses_BL</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Bond__c</field>
            <value>
                <elementReference>var_BondCreated.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the transaction record to finalise it. Set the correct status and add the provided comments.</description>
        <name>Finalise_Transaction_Renter_Transfer</name>
        <label>Finalise Transaction</label>
        <locationX>1898</locationX>
        <locationY>974</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Statuses_RTR</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_Transfer_Option__c</field>
            <value>
                <stringValue>Removal of Renter</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the transaction record to finalise it. Set the correct status and add the provided comments.</description>
        <name>Finalise_Transaction_RPT</name>
        <label>Finalise Transaction</label>
        <locationX>4274</locationX>
        <locationY>974</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Statuses_RPT</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Remove_Renter_from_Bond</name>
        <label>Remove Renter from Bond</label>
        <locationX>1986</locationX>
        <locationY>1406</locationY>
        <connector>
            <targetReference>Bond_Party_Actions_RTR</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Bond__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Renter__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Bond_Party_Actions_RTR.Renter__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Rental_Provider__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Bond_Party_Actions_RTR.Rental_Provider__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Outgoing_Closed_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Inactive</stringValue>
            </value>
        </inputAssignments>
        <object>Bond_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Account_Records</name>
        <label>Update Account Records</label>
        <locationX>19498</locationX>
        <locationY>2594</locationY>
        <connector>
            <targetReference>Loop_over_Account_Details</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_over_Account_Details.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Company_Name__pc</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FirstName</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.First_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Family_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonBirthdate</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Date_of_Birth__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonEmail</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Email_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonMobilePhone</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Mobile_Number__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Account_Records_Company</name>
        <label>Update Account Records</label>
        <locationX>20026</locationX>
        <locationY>2594</locationY>
        <connector>
            <targetReference>Loop_over_Account_Details</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_over_Account_Details.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Company_Name__pc</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonBirthdate</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Date_of_Birth__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonEmail</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Email_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonMobilePhone</field>
            <value>
                <elementReference>Loop_Over_Bond_Parties.Mobile_Number__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Bond</name>
        <label>Update Bond</label>
        <locationX>14834</locationX>
        <locationY>3818</locationY>
        <connector>
            <targetReference>Finalise_Transaction_0</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <inputReference>Get_Bond_Details</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Bond_Party_Records</name>
        <label>Update Bond Party Records</label>
        <locationX>18178</locationX>
        <locationY>3086</locationY>
        <connector>
            <targetReference>Loop_Over_selected_TPs</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <inputReference>rec_UpdatedBondParties</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Due to the Current Bond Amount being $0, the status of the bond will be set to &apos;Closed&apos;</description>
        <name>Update_Bond_Status_Closed</name>
        <label>Update Bond Status - Closed</label>
        <locationX>50</locationX>
        <locationY>1406</locationY>
        <connector>
            <targetReference>Trigger_BC_Finalised_Notification</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Bond__r.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Closed_date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Current_Bond_Amount__c</field>
            <value>
                <elementReference>NewCurrentBondAmount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Closed</stringValue>
            </value>
        </inputAssignments>
        <object>Bond__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Due to the Current Bond Amount being &gt; $0, the status of the bond will be set to &apos;Partially Paid&apos;</description>
        <name>Update_Bond_Status_Partially_Paid</name>
        <label>Update Bond Status - Partially Paid</label>
        <locationX>314</locationX>
        <locationY>1406</locationY>
        <connector>
            <targetReference>Trigger_BC_Finalised_Notification</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Bond__r.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Current_Bond_Amount__c</field>
            <value>
                <elementReference>NewCurrentBondAmount</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Partially Paid</stringValue>
            </value>
        </inputAssignments>
        <object>Bond__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Case</name>
        <label>Update Case</label>
        <locationX>17714</locationX>
        <locationY>3734</locationY>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Bond_Number__c</field>
            <value>
                <elementReference>var_BondCreated.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the statuses of all transaction parties to &quot;Agreed&quot;</description>
        <name>Update_Transaction_Party_Statuses</name>
        <label>Update Transaction Party Statuses</label>
        <locationX>182</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Create_Claim_Repayment_Records</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the statuses of all transaction parties to &quot;Agreed&quot;</description>
        <name>Update_Transaction_Party_Statuses_0</name>
        <label>Update Transaction Party Statuses</label>
        <locationX>15230</locationX>
        <locationY>4166</locationY>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <inputReference>rec_Transaction_Parties</inputReference>
    </recordUpdates>
    <recordUpdates>
        <description>Update the statuses of all transaction parties to &quot;Agreed&quot;</description>
        <name>Update_Transaction_Party_Statuses_BL</name>
        <label>Update Transaction Party Statuses</label>
        <locationX>17714</locationX>
        <locationY>3626</locationY>
        <connector>
            <targetReference>Update_Case</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Party_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Removed</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the statuses of all transaction parties to &quot;Agreed&quot;</description>
        <name>Update_Transaction_Party_Statuses_BM</name>
        <label>Update Transaction Party Statuses</label>
        <locationX>6914</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Get_Transferee_BM</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the statuses of all transaction parties to &quot;Agreed&quot;</description>
        <name>Update_Transaction_Party_Statuses_RPT</name>
        <label>Update Transaction Party Statuses</label>
        <locationX>4274</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Get_Transferee_RP</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the statuses of all transaction parties to &quot;Agreed&quot;</description>
        <name>Update_Transaction_Party_Statuses_RTR</name>
        <label>Update Transaction Party Statuses</label>
        <locationX>1898</locationX>
        <locationY>1082</locationY>
        <connector>
            <targetReference>Get_Outgoing_Renters</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Voucher</name>
        <label>Update Voucher</label>
        <locationX>17054</locationX>
        <locationY>2546</locationY>
        <connector>
            <targetReference>Check_if_Agreed_status_TP_exists</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen_0</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Voucher__r.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Voucher_Status__c</field>
            <value>
                <stringValue>Presented</stringValue>
            </value>
        </inputAssignments>
        <object>Voucher__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Voucher_Status</name>
        <label>Update Voucher Status</label>
        <locationX>17054</locationX>
        <locationY>2438</locationY>
        <connector>
            <targetReference>Update_Voucher</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Update_Transaction_DML_Error_Screen_0</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Homes_Victoria_Voucher_Status__c</field>
            <value>
                <stringValue>Presented</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <description>This screen will ask the user to confirm if they wish to continue with the finalisation of the transaction.</description>
        <name>Confirmation_Screen</name>
        <label>Confirmation Screen</label>
        <locationX>10730</locationX>
        <locationY>650</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>scrn_Comments</targetReference>
        </connector>
        <fields>
            <name>ConfirmationMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; color: rgb(23, 43, 77);&quot;&gt;You are about to finalise this transaction. Do you want to proceed?&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen will show where the user is not permitted to finalize a transaction.</description>
        <name>Permission_Denied</name>
        <label>Permission Denied</label>
        <locationX>16444</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>PermissionDeniedMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Your permissions will not allow the finalisation of this transaction. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Renter_Amendments</name>
        <label>Renter Amendments</label>
        <locationX>15230</locationX>
        <locationY>1190</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Check_if_user_selected_records_for_Renter_Amendments</targetReference>
        </connector>
        <fields>
            <name>Renter_Amendments_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Renter_Amendments_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Amendments</name>
                    <choiceReferences>ch_AddressAmendments</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Amendments</fieldText>
                    <fieldType>RadioButtons</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Renter_Amendments_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Address</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Current Rental Property Address : &lt;/strong&gt;{!recordId.Current_Rental_Property_Address__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Property Address Requested : &lt;/strong&gt;{!recordId.Property_Address_Requested__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>recordId.Property_Address_Differs_to_Bond__c</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Transaction_Parties_0</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Transaction_Party__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Renter Amendments</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>rec_Transaction_Parties</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-8e10&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Transaction Party Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;First_Name__c&quot;,&quot;guid&quot;:&quot;column-741d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;First Name&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;First Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Last_Name__c&quot;,&quot;guid&quot;:&quot;column-765b&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Family Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Company_Name__c&quot;,&quot;guid&quot;:&quot;column-a637&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Company Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Date_of_Birth__c&quot;,&quot;guid&quot;:&quot;column-8f9a&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Date of Birth&quot;,&quot;type&quot;:&quot;date-local&quot;},{&quot;apiName&quot;:&quot;Mobile_Number__c&quot;,&quot;guid&quot;:&quot;column-3a0c&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Mobile Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Email_Address__c&quot;,&quot;guid&quot;:&quot;column-34d5&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:6,&quot;label&quot;:&quot;Email Address&quot;,&quot;type&quot;:&quot;email&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>rec_SelectedTPs</assignToReference>
                <name>selectedRows</name>
            </outputParameters>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>RP_Transfer</name>
        <label>RP Transfer</label>
        <locationX>15828</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Error_Msg</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(244, 3, 3);&quot;&gt;You are not authorised to perform the finalisation of this transaction.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>The user has confirmed they wish to continue on the first screen and is now asked to provide  comments.</description>
        <name>scrn_Comments</name>
        <label>Comment Screen</label>
        <locationX>10730</locationX>
        <locationY>758</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Determine_the_Transaction_Type</targetReference>
        </connector>
        <fields>
            <name>Comments</name>
            <fieldText>Comments</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>A screen displaying the success message to the user.</description>
        <name>Success_Screen</name>
        <label>Success Screen</label>
        <locationX>10730</locationX>
        <locationY>4406</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccesMessage</name>
            <fieldText>&lt;p&gt;The transaction has been successfully finalised.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen is displayed when the type of transaction is not able to be cancelled via this flow.</description>
        <name>Transaction_Type_Cannot_Be_Cancelled</name>
        <label>Transaction Type Cannot Be Cancelled</label>
        <locationX>21278</locationX>
        <locationY>974</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>UnableToFinaliseText</name>
            <fieldText>&lt;p&gt;This type of transaction is unable to be finalised at this time. &lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Screen displayed to the user where the transaction is unable to be finalised due to it not being in the correct status.</description>
        <name>Unable_To_Finalise_Screen</name>
        <label>Unable To Finalise Screen</label>
        <locationX>21542</locationX>
        <locationY>650</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>UnableToFinaliseMessage</name>
            <fieldText>&lt;p&gt;This transaction is unable to be finalised at this time. &lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Screen to be displayed when the update of the transaction record fails.</description>
        <name>Update_Transaction_DML_Error_Screen</name>
        <label>Update Transaction DML Error Screen</label>
        <locationX>842</locationX>
        <locationY>1190</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>DML_Failure_Message</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Something went wrong. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Screen to be displayed when the update of the transaction record fails.</description>
        <name>Update_Transaction_DML_Error_Screen_0</name>
        <label>Update Transaction DML Error Screen</label>
        <locationX>17846</locationX>
        <locationY>2438</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_DML_Failure_Message</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Something went wrong. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>16010</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Permissions</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <description>Calls a sub flow to create the claim repayment records.</description>
        <name>Create_Claim_Repayment_Records</name>
        <label>Create Claim Repayment Records</label>
        <locationX>182</locationX>
        <locationY>1190</locationY>
        <connector>
            <targetReference>Is_Current_Bond_Amount_0</targetReference>
        </connector>
        <flowName>Finalise_Transaction_Claim_Repayment_Record_Creation</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>NewCurrentBondAmount</assignToReference>
            <name>NewCurrentBondAmountValue</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>All_Renter_Names</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Country</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>AU</stringValue>
        </value>
    </variables>
    <variables>
        <description>Used to hold the outcome of the calculation for the current bond amount.</description>
        <name>CurrentBondAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <description>Variable to store the individual repayment records in the loop.</description>
        <name>IndividualRepaymentRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Repayment__c</objectType>
    </variables>
    <variables>
        <name>NewCurrentBondAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>rec_BondParties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond_Party__c</objectType>
    </variables>
    <variables>
        <name>rec_IncomingRenters</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>rec_PersonAccount</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>rec_RenterDetails</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>rec_SelectedTPs</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>rec_TP_Details</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>rec_Transaction_Parties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>rec_UpdatedBondParties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond_Party__c</objectType>
    </variables>
    <variables>
        <description>The Salesforce Record ID for the record being interrogated by the flow.</description>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction__c</objectType>
    </variables>
    <variables>
        <description>A Collection for repayment records to be created</description>
        <name>RepaymentsToCreate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Repayment__c</objectType>
    </variables>
    <variables>
        <description>Target renter position for RT Finalised Notification</description>
        <name>TargetRenterPositionforRTNotification</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>v_paperFormRPTBondType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>Bond Lodgement</stringValue>
        </value>
    </variables>
    <variables>
        <name>var_BondCreated</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond__c</objectType>
    </variables>
    <variables>
        <name>var_ContactEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
