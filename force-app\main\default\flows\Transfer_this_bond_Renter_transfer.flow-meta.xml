<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionCalls>
        <description>Calls an APEX class &quot;CloneContentVersion&quot; to clone the content version for the specified document and link it to the case. It ensures that the metadata associated with the content version record is set correctly.</description>
        <name>Create_new_document_version</name>
        <label>Create new document version</label>
        <locationX>2765</locationX>
        <locationY>9248</locationY>
        <actionName>CloneContentVersion</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Loop_case_document_links</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>caseNumber</name>
            <value>
                <elementReference>Get_Case_Details.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>category</name>
            <value>
                <stringValue>Transactions - Documents</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>contentDocumentId</name>
            <value>
                <elementReference>Loop_case_document_links.ContentDocumentId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>newRecordId</name>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>status</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transactionType</name>
            <value>
                <stringValue>Renter Transfer</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>uploadDocumentTo</name>
            <value>
                <stringValue>Transactions</stringValue>
            </value>
        </inputParameters>
        <nameSegment>CloneContentVersion</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Adds the checked email address to the unique list to indicate it has been checked.</description>
        <name>Add_checked_email_to_the_unique_list</name>
        <label>Add checked email to the unique list</label>
        <locationX>2721</locationX>
        <locationY>6836</locationY>
        <assignmentItems>
            <assignToReference>var_UnqiueRenterEmailList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_All_Transaction_Parties_for_email_check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Adds the renter name to the collection for duplicate email addresses which will be used in messaging later in the flow.</description>
        <name>Add_renter_to_duplicate_renter_email_collection</name>
        <label>Add renter to duplicate renter email collection</label>
        <locationX>2589</locationX>
        <locationY>6020</locationY>
        <assignmentItems>
            <assignToReference>var_duplicateRenterTPEmailCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_All_Transaction_Parties_for_email_check</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_duplicateEmailFlag</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_the_error_text_for_the_renter_RenterCheck</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Adds the renter to the Duplicate RP email collection to be used for messaging later in the flow.</description>
        <name>Add_renter_to_Duplicate_RP_Email_Collection</name>
        <label>Add renter to duplicate RP email collection</label>
        <locationX>2589</locationX>
        <locationY>6536</locationY>
        <assignmentItems>
            <assignToReference>var_duplicateRenterTPEmailWithRP</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_All_Transaction_Parties_for_email_check</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_duplicateEmailFlag</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_the_error_text_for_the_renter_RPCheck</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Consolidate the two separate collections for staying and incoming renters into the one collection for easier management. There is a Removal first in the assignment to initialise the collection to empty. This is so as if the user clicks back through the flow makes a change, etc, that the collection is not populated with duplicate values.</description>
        <name>Assign_All_Incoming_and_Staying_TP_s</name>
        <label>Assign all incoming and staying TP&apos;s</label>
        <locationX>2633</locationX>
        <locationY>5612</locationY>
        <assignmentItems>
            <assignToReference>var_AllStayingIncomingTP</assignToReference>
            <operator>RemoveAll</operator>
            <value>
                <elementReference>var_AllStayingIncomingTP</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_AllStayingIncomingTP</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_StayingRenterParties</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_AllStayingIncomingTP</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_NewTransactionPArties</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_All_Transaction_Parties_for_email_check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the fields values for all staying transaction parties. This is is on the path where there are some leaving renters.</description>
        <name>Assign_All_Staying_Renter_Parties_LR</name>
        <label>Assign all staying renter parties</label>
        <locationX>2294</locationX>
        <locationY>3578</locationY>
        <assignmentItems>
            <assignToReference>var_StayingRenterParties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_NewRenterTransactionParty</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loops_Existing_Bond_Parties</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the field values for all staying transaction parties. This is in the path where there are no leaving renters.</description>
        <name>Assign_All_Staying_Renter_Parties_NLR</name>
        <label>Assign all staying renter parties</label>
        <locationX>2602</locationX>
        <locationY>2078</locationY>
        <assignmentItems>
            <assignToReference>var_StayingRenterParties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_NewRenterTransactionParty</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopsExistingBondParties</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Consolidate the collections for staying main collection for easier management.</description>
        <name>Assign_All_Staying_TP_s</name>
        <label>Assign all staying TP&apos;s</label>
        <locationX>2369</locationX>
        <locationY>5612</locationY>
        <assignmentItems>
            <assignToReference>var_AllStayingIncomingTP</assignToReference>
            <operator>RemoveAll</operator>
            <value>
                <elementReference>var_AllStayingIncomingTP</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_AllStayingIncomingTP</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_NewTransactionPArties</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_All_Transaction_Parties_for_email_check</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the Leaving, Staying and New transaction party collections to one main collection to be used in the DML to create the new transaction party records. This will avoid the need for multiple DML&apos;s</description>
        <name>Assign_All_TP_Collections_to_Main_TP_Collection</name>
        <label>Assign all TP collections to main TP collection</label>
        <locationX>2677</locationX>
        <locationY>7568</locationY>
        <assignmentItems>
            <assignToReference>var_AllTransactionParties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_AllStayingIncomingTP</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_AllTransactionParties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_LeavingTransactionPartiesFinal</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_all_TP_s_to_add_Txn_Number</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the count of existing bond parties</description>
        <name>Assign_Count_of_Existing_bond_Parties</name>
        <label>Assign count of existing bond parties</label>
        <locationX>2501</locationX>
        <locationY>1322</locationY>
        <assignmentItems>
            <assignToReference>var_countExistingRenterBondParties</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>var_allBondParties</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CountAllTPs</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>var_allBondParties</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>REnter_Bond_Parties_Exist</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the countof selected leaving renters.</description>
        <name>Assign_count_of_leaving_renters</name>
        <label>Assign count of leaving renters</label>
        <locationX>2272</locationX>
        <locationY>1646</locationY>
        <assignmentItems>
            <assignToReference>var_countSelectedLaveingBondParties</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Select_Leaving_Renters.selectedRows</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_leavingBondParties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Select_Leaving_Renters.selectedRows</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_CountAllTPs</assignToReference>
            <operator>Subtract</operator>
            <value>
                <elementReference>var_countSelectedLaveingBondParties</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Have_any_leaving_renters_been_chosen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the counts of pending transactions</description>
        <name>Assign_counts_of_pending_transactions</name>
        <label>Assign counts of pending transactions</label>
        <locationX>1957</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>var_countPendingTransferTransactions</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Transfer_Transaction_Records</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_countPendingNonTransferTransactions</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_Pending_Transactions_non_transfer</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Further_eligibility_checks</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the main TP details for the renter TP which will be created later in the flow</description>
        <name>Assign_Leaving_Renter_TP_Details_Single</name>
        <label>Assign leaving renter TP details - single</label>
        <locationX>2118</locationX>
        <locationY>1970</locationY>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Company_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Company_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Date_of_Birth__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Date_of_Birth__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Email_Address__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Email_Address__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.First_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.First_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Last_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Family_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Mobile_Number__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Mobile_Number__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_position__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Leaving</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Rental_Provider__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Rental_Provider__r.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Renter_Type__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Renter__r.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Role__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Renter_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_LeavingTransactionParties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_NewRenterTransactionParty</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Leaving_TP</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Leaving TP&apos;s to the finalise TP collection for leaving renters.</description>
        <name>Assign_leaving_renters_to_new_collection</name>
        <label>Assign leaving renters to final collection</label>
        <locationX>2162</locationX>
        <locationY>2378</locationY>
        <assignmentItems>
            <assignToReference>var_LeavingTransactionPartiesFinal</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>var_LeavingTransactionParties</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loops_Existing_Bond_Parties</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the data to a collection variable</description>
        <name>Assign_New_Renter_Details_to_Collection_Variable</name>
        <label>Assign new renter details to collection variable</label>
        <locationX>2391</locationX>
        <locationY>4454</locationY>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Company_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>New_Renter_Company_Name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.First_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>New_Renter_First_Name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Last_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>New_Renter_Family_Name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Email_Address__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>New_Renter_Email_2</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Mobile_Number__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>New_Renter_Mobile_Number</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Date_of_Birth__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>New_Renter_Date_of_birth</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>New_Renter_Type</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>var_RenterName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_position__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Incoming</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Role__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>frm_NewPartyName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewTransactionPArties</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>var_NewRenterTransactionParty</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_Renter_Count</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the main TP details for the staying renter TP which will be created later in the flow</description>
        <name>Assign_Staying_Renter_TP_Details_Single_All</name>
        <label>Assign staying renter TP details - single</label>
        <locationX>2602</locationX>
        <locationY>1970</locationY>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Company_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Company_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Date_of_Birth__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Date_of_Birth__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Email_Address__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Email_Address__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.First_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.First_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Last_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Family_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Mobile_Number__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Mobile_Number__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_position__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Staying</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Rental_Provider__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Rental_Provider__r.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Renter_Type__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Renter__r.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Role__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>LoopsExistingBondParties.Renter_Name__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_All_Staying_Renter_Parties_NLR</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the main TP details for the staying renter TP which will be created later in the flow</description>
        <name>Assign_Staying_Renter_TP_Details_Single_Some</name>
        <label>Assign staying renter TP details - single</label>
        <locationX>2294</locationX>
        <locationY>3470</locationY>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Company_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Company_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Date_of_Birth__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Date_of_Birth__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Email_Address__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Email_Address__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.First_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.First_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Last_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Family_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Mobile_Number__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Mobile_Number__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_position__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>staying</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Rental_Provider__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Leaving_TP.Rental_Provider__r.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Renter_Type__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Renter__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Renter__r.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Role__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_NewRenterTransactionParty.Party_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loops_Existing_Bond_Parties.Renter_Name__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_All_Staying_Renter_Parties_LR</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the single line of text in the error text for the renter.</description>
        <name>Assign_the_error_text_for_the_renter_RenterCheck</name>
        <label>Assign the error text for the renter</label>
        <locationX>2589</locationX>
        <locationY>6128</locationY>
        <assignmentItems>
            <assignToReference>var_RenterDuplicateEmailListText</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>frm_RenterDuplicateEmailREnterText</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>RP_user_emails</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the error text line for the renter.</description>
        <name>Assign_the_error_text_for_the_renter_RPCheck</name>
        <label>Assign the error text for the renter</label>
        <locationX>2589</locationX>
        <locationY>6644</locationY>
        <assignmentItems>
            <assignToReference>var_RenterDuplicateEmailRPText</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>frm_RenterDuplicateEmailRPText</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_checked_email_to_the_unique_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns Leaving TP&apos;s to the finalise TP collection for leaving renters including the addition of the reason for leaving.</description>
        <name>Assign_the_Reason_for_leaving_to_the_new_TP</name>
        <label>Assign the reason for leaving to the new TP</label>
        <locationX>2426</locationX>
        <locationY>2702</locationY>
        <assignmentItems>
            <assignToReference>Loop_New_TPs_To_assign_Reason_for_leaving.Reason_for_Removal__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Reasons_for_leaving.ReasonForLeaving</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_LeavingTransactionPartiesFinal</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_New_TPs_To_assign_Reason_for_leaving</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_New_TPs_To_assign_Reason_for_leaving</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Transaction number to the Transaction Party records</description>
        <name>Assign_Transaction_Number_to_TP_Records</name>
        <label>Assign transaction number to TP Records</label>
        <locationX>2765</locationX>
        <locationY>7784</locationY>
        <assignmentItems>
            <assignToReference>Loop_all_TP_s_to_add_Txn_Number.Transaction__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>var_AllTransactionPartiesFinal</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_all_TP_s_to_add_Txn_Number</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_all_TP_s_to_add_Txn_Number</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Increment the renter count.</description>
        <name>Increment_Renter_Count</name>
        <label>Increment renter count</label>
        <locationX>2391</locationX>
        <locationY>4562</locationY>
        <assignmentItems>
            <assignToReference>var_CountAllTPs</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Renter_Count</targetReference>
        </connector>
    </assignments>
    <choices>
        <description>A yes choice which is used for asking the user if they wish to add renters to the bond.</description>
        <name>ch_AddRentersNo</name>
        <choiceText>No</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>No</stringValue>
        </value>
    </choices>
    <choices>
        <description>A yes choice which is used for asking the user if they wish to add renters to the bond.</description>
        <name>ch_AddRentersYes</name>
        <choiceText>Yes</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Yes</stringValue>
        </value>
    </choices>
    <choices>
        <description>A choice for the type of supporting document</description>
        <name>ch_CourtReferenceNumber</name>
        <choiceText>Court reference number</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Court Reference Number</stringValue>
        </value>
    </choices>
    <choices>
        <description>A choice for the type of supporting document</description>
        <name>ch_VCATOrder</name>
        <choiceText>VCAT order</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>VCAT Order</stringValue>
        </value>
    </choices>
    <choices>
        <description>A choice for the basis of transfer.</description>
        <name>ch_WithConsent</name>
        <choiceText>With consent</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>With Consent</stringValue>
        </value>
    </choices>
    <choices>
        <description>A choice used in the basis for transfer.</description>
        <name>ch_WithoutConsent</name>
        <choiceText>Without consent</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Without Consent</stringValue>
        </value>
    </choices>
    <collectionProcessors>
        <description>Filters incoming renters from the collection so as they can be acted upon and the new person accounts and transaction parties created.</description>
        <name>Filter_Incoming_Renters</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter incoming Renters</label>
        <locationX>2677</locationX>
        <locationY>7976</locationY>
        <assignNextValueToReference>currentItem_Filter_Incoming_Renters</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>var_AllTransactionPartiesFinal</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Incoming_Renters.Renter_position__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>Incoming</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Loops_Incoming_TP_for_Person_Accounts</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <description>Filters staying and leaving  renters from the collection so as they can be acted upon and transaction parties created.</description>
        <name>Filter_Staying_and_Leaving_TP_s</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Filter staying and leaving TP&apos;s</label>
        <locationX>2677</locationX>
        <locationY>8600</locationY>
        <assignNextValueToReference>currentItem_Filter_Staying_and_Leaving_TP_s</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>var_AllTransactionPartiesFinal</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Filter_Staying_and_Leaving_TP_s.Renter_position__c</leftValueReference>
            <operator>NotEqualTo</operator>
            <rightValue>
                <stringValue>Incoming</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Create_Staying_and_Leaving_Transaction_Parties</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <description>Fileter all RP user emails where there is an email present.</description>
        <name>RP_user_emails</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>RP user emails</label>
        <locationX>2721</locationX>
        <locationY>6320</locationY>
        <assignNextValueToReference>currentItem_RP_user_emails</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_RP_user_records</collectionReference>
        <conditionLogic>or</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_RP_user_emails.Email</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</elementReference>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_RP_user_emails.Username</leftValueReference>
            <operator>Contains</operator>
            <rightValue>
                <elementReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Check_against_RP_user_emails</targetReference>
        </connector>
    </collectionProcessors>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;path&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <description>Determine of the user wishes to add another renyter.</description>
        <name>Add_Another_Renter</name>
        <label>Add another renter?</label>
        <locationX>2237</locationX>
        <locationY>4886</locationY>
        <defaultConnector>
            <targetReference>Get_RP_user_records</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No More Renters</defaultConnectorLabel>
        <rules>
            <name>Add_Another_Renter_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Do_you_wish_to_add_another_renter</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>ch_AddRentersYes</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Renter_Details_Screen</targetReference>
            </connector>
            <label>Add Another Renter</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if the user has chosen to add a new renter.</description>
        <name>Adding_new_renters</name>
        <label>Adding new renters?</label>
        <locationX>2600</locationX>
        <locationY>4238</locationY>
        <defaultConnector>
            <targetReference>Check_if_changes_have_been_made</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Add_new_renter</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Do_you_wish_to_any_new_renters_to_the_bond</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>ch_AddRentersYes</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>New_Renter_Details_Screen</targetReference>
            </connector>
            <label>Add new renter</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks to see if there all existing renters are leaving. If so, then we will not add the collection (containing a null) to the combined collection for All Staying and Leaving TP&apos;s</description>
        <name>Are_all_existing_renters_leaving</name>
        <label>Are all existing renters leaving?</label>
        <locationX>2501</locationX>
        <locationY>5504</locationY>
        <defaultConnector>
            <targetReference>Assign_All_Incoming_and_Staying_TP_s</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>None or Some leaving</defaultConnectorLabel>
        <rules>
            <name>Leaving_Renters_Exist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_countSelectedLaveingBondParties</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>var_countExistingRenterBondParties</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_All_Staying_TP_s</targetReference>
            </connector>
            <label>All Leaving</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determines if a case has been found. If not found then show and error screen and allow the user to fo back and re-enter or close the flow. If found then display a screen with the details.</description>
        <name>Case_Found</name>
        <label>Case found?</label>
        <locationX>2919</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Case_Not_Found_Error_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Case not found</defaultConnectorLabel>
        <rules>
            <name>Case_found_yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Case_Details.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Case_Details.Status</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Case_Details_Screen</targetReference>
            </connector>
            <label>Case found</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks the email address in the loop against other renters.</description>
        <name>Check_Against_other_renters</name>
        <label>Check against other renters</label>
        <locationX>2721</locationX>
        <locationY>5912</locationY>
        <defaultConnector>
            <targetReference>RP_user_emails</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No email match found</defaultConnectorLabel>
        <rules>
            <name>Email_match_found_renter</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_UnqiueRenterEmailList</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_renter_to_duplicate_renter_email_collection</targetReference>
            </connector>
            <label>Email match found</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks if the renter email matches that of an RP User or RP Org Email</description>
        <name>Check_against_RP_user_emails</name>
        <label>Check against RP user emails</label>
        <locationX>2721</locationX>
        <locationY>6428</locationY>
        <defaultConnector>
            <targetReference>Add_checked_email_to_the_unique_list</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_Matches_RP</name>
            <conditionLogic>1 AND (2 OR 3 OR 4 OR 5 OR 6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>var_BondRecordId.Rental_Provider__r.Office_Email__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>var_BondRecordId.Rental_Provider__r.Transaction_notices__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>var_BondRecordId.Rental_Provider__r.Management_notices__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>var_BondRecordId.Rental_Provider__r.Bond_receipts__c</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_All_Transaction_Parties_for_email_check.Email_Address__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>var_BondRecordId.Rental_Provider__r.PersonEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>RP_user_emails</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_renter_to_Duplicate_RP_Email_Collection</targetReference>
            </connector>
            <label>Email Matches RP</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks the value of the duplicate email flag. If it is true, then display an error page with the details. If false, then continue processing.</description>
        <name>Check_Duplicate_Email_Flag</name>
        <label>Check duplicate email flag</label>
        <locationX>2501</locationX>
        <locationY>7028</locationY>
        <defaultConnector>
            <targetReference>Confirm_Details_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No duplicate emails found</defaultConnectorLabel>
        <rules>
            <name>Duplicate_emails_found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_duplicateEmailFlag</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Duplicate_Email_Error_Screen</targetReference>
            </connector>
            <label>Duplicate emails found</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks to ensure there have been changes made to the renter. There should be either renters leaving the bond, coming onto the bond or both.</description>
        <name>Check_if_changes_have_been_made</name>
        <label>Check if changes have been made</label>
        <locationX>2809</locationX>
        <locationY>4346</locationY>
        <defaultConnector>
            <targetReference>No_Change_Error_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Renter Changes</defaultConnectorLabel>
        <rules>
            <name>Renter_changes_exist</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>var_LeavingTransactionParties</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_NewTransactionPArties</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_RP_user_records</targetReference>
            </connector>
            <label>Renter changes exist</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check if the renter count is less than 10. If so then provide the option to add additional renters.</description>
        <name>Check_if_renter_count_is_10_or_less</name>
        <label>Check if renter count is 10 or less</label>
        <locationX>2501</locationX>
        <locationY>4022</locationY>
        <defaultConnector>
            <targetReference>Get_RP_user_records</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Renter_Count_0</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_CountAllTPs</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>New_Renter_Details_Screen</targetReference>
            </connector>
            <label>Renter Count = 0</label>
        </rules>
        <rules>
            <name>Renter_Count_10</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_CountAllTPs</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <numberValue>10.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_Renters_Initial_Screen</targetReference>
            </connector>
            <label>Renter Count &lt; 10</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks the renter count to ensure we have not reached 10.</description>
        <name>Check_Renter_Count</name>
        <label>Check renter count</label>
        <locationX>2391</locationX>
        <locationY>4670</locationY>
        <defaultConnector>
            <targetReference>Get_RP_user_records</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No more renters can be added,</defaultConnectorLabel>
        <rules>
            <name>Less_than_10</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_CountAllTPs</leftValueReference>
                <operator>LessThan</operator>
                <rightValue>
                    <numberValue>10.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_More_Renters_Screen</targetReference>
            </connector>
            <label>Less than 10</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determines based on the basis of transfer if a reason for leaving is required to be entered for each leaving renter.</description>
        <name>Determine_if_reason_for_leaving_is_required</name>
        <label>Determine if reason for leaving is required</label>
        <locationX>2030</locationX>
        <locationY>2162</locationY>
        <defaultConnector>
            <targetReference>Selected_Leaving_Renters_With_Consent</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Reason no required</defaultConnectorLabel>
        <rules>
            <name>Reason_for_leaving_required</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Basis_of_transfer</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>ch_WithoutConsent</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Reason_for_Leaving</targetReference>
            </connector>
            <label>Reason required</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks to ensure the bond and other elements of the bond are in the right status and configuration for a Renter Transfer.</description>
        <name>Further_eligibility_checks</name>
        <label>Further eligibility checks</label>
        <locationX>1957</locationX>
        <locationY>458</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Homes_Victoria</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_BondRecordId.Funding_Source__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Homes Victoria</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Homes_Victoria_Funded_Error_Screen</targetReference>
            </connector>
            <label>Homes Victoria</label>
        </rules>
        <rules>
            <name>Bond_status_Closed_Cancelled</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>var_BondRecordId.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_BondRecordId.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cancelled</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Bond_Closed_Cancelled_Error_Screen</targetReference>
            </connector>
            <label>Bond status = Closed/Cancelled</label>
        </rules>
        <rules>
            <name>Bond_Status_Suspended</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_BondRecordId.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Suspended</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Bond_Suspended_Error_Screen</targetReference>
            </connector>
            <label>Bond Status = Suspended</label>
        </rules>
        <rules>
            <name>Pending_Transactions</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>var_countPendingNonTransferTransactions</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_countPendingTransferTransactions</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Pending_Transactions_Error_Screen</targetReference>
            </connector>
            <label>Pending Transactions</label>
        </rules>
        <rules>
            <name>Bond_Status_Active_Partially_Paid</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>var_BondRecordId.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>var_BondRecordId.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Partially Paid</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Case_Number_Screen</targetReference>
            </connector>
            <label>Bond Status = Active/Partially Paid</label>
        </rules>
    </decisions>
    <decisions>
        <description>DEtermin if any renters have been selected to leave the bond.</description>
        <name>Have_any_leaving_renters_been_chosen</name>
        <label>Have any leaving renters been chosen?</label>
        <locationX>2272</locationX>
        <locationY>1754</locationY>
        <defaultConnector>
            <targetReference>LoopsExistingBondParties</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Leaving Renters</defaultConnectorLabel>
        <rules>
            <name>Renters_Selected_Leaving</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_countSelectedLaveingBondParties</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Leaving_TP</targetReference>
            </connector>
            <label>Renters Selected for Leaving</label>
        </rules>
    </decisions>
    <decisions>
        <description>Looping the selected leaving renters, so as we can compare against the BP list to see if there is match. If there is no match then that BP will become a staying TP on the transfer transaction</description>
        <name>Loop_Selected_Leaving_Renters</name>
        <label>Loop selected leaving renters</label>
        <locationX>2206</locationX>
        <locationY>3362</locationY>
        <defaultConnector>
            <targetReference>Assign_Staying_Renter_TP_Details_Single_Some</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Staying TP</defaultConnectorLabel>
        <rules>
            <name>Leaving_TP</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Selected_leaving_renters_Without_Consent</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>Loops_Existing_Bond_Parties.Renter__r.Id</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Selected_Leaving_Renters_With_Consent</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>Loops_Existing_Bond_Parties.Renter__r.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loops_Existing_Bond_Parties</targetReference>
            </connector>
            <label>Leaving TP</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check to see if any renter bond parties exists on the bond.</description>
        <name>REnter_Bond_Parties_Exist</name>
        <label>Renter bond parties exist?</label>
        <locationX>2501</locationX>
        <locationY>1430</locationY>
        <defaultConnector>
            <targetReference>Check_if_renter_count_is_10_or_less</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Bond Parties Exist</defaultConnectorLabel>
        <rules>
            <name>Bond_Parties_Exist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>var_CountAllTPs</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>0.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transfer</targetReference>
            </connector>
            <label>Bond Parties Exist?</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks to see if the New TP in the loop matches the contact ID in the loop of the reasons for leaving. If so the record in the record collection for the new TP is updated with the reason for removal.</description>
        <name>TP_matches_Leaving_TP</name>
        <label>TP matches leaving TP</label>
        <locationX>2558</locationX>
        <locationY>2594</locationY>
        <defaultConnector>
            <targetReference>Loop_New_TPs_To_assign_Reason_for_leaving</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Match - New TP to Leaving TP</defaultConnectorLabel>
        <rules>
            <name>TP_matches_Leaving_TP_Outcome</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_New_TPs_To_assign_Reason_for_leaving.Renter__r.Id</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Reasons_for_leaving.SelectedLeavingRenterContactID</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_the_Reason_for_leaving_to_the_new_TP</targetReference>
            </connector>
            <label>TP matches Leaving TP</label>
        </rules>
    </decisions>
    <description>This sub-flow is called from the main &quot;Transfer this bond&quot; flow. It guides the user through lodging a renter transfer against a bond.</description>
    <dynamicChoiceSets>
        <description>A choice set for the new renter type. Driven from the &quot;Renter_Type__c&quot; field on the &quot;Transaction_Party__c&quot; object.</description>
        <name>ch_NewRenterType</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Renter_Type__c</picklistField>
        <picklistObject>Transaction_Party__c</picklistObject>
        <sortOrder>Asc</sortOrder>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <description>Determine the new party name based on the renter type</description>
        <name>frm_NewPartyName</name>
        <dataType>String</dataType>
        <expression>if({!New_Renter_Type}=&apos;Individual&apos;,
     {!New_Renter_First_Name} + &quot; &quot; + {!New_Renter_Family_Name},
      {!New_Renter_Company_Name}
)</expression>
    </formulas>
    <formulas>
        <description>Formula to calculate the link to the new transaction which gets created/</description>
        <name>frm_NewTransactionLink</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_260}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_260})) &amp; {!var_NewTransactionRecordId}</expression>
    </formulas>
    <formulas>
        <description>Formula to derive the Last name for the person account of an incoming renter.</description>
        <name>frm_PersonAccountLastName</name>
        <dataType>String</dataType>
        <expression>IF (TEXT({!Loops_Incoming_TP_for_Person_Accounts.Renter_Type__c}) = &apos;Company&apos;,
{!Loops_Incoming_TP_for_Person_Accounts.Company_Name__c}, {!Loops_Incoming_TP_for_Person_Accounts.Last_Name__c})</expression>
    </formulas>
    <formulas>
        <description>A formula to calculate the age of a new renter based on the date of birth being entered.</description>
        <name>frm_RenterAge</name>
        <dataType>Number</dataType>
        <expression>YEAR(TODAY()) -
YEAR({!New_Renter_Date_of_birth}) -
  IF(
      AND(MONTH({!New_Renter_Date_of_birth}) = 02,
               DAY({!New_Renter_Date_of_birth}) = 29,
               TODAY() &gt; DATE(YEAR(TODAY()), 02, 28)),
       0,
       IF(
       TODAY() &gt;= DATE(YEAR(TODAY()), MONTH({!New_Renter_Date_of_birth}), DAY({!New_Renter_Date_of_birth})),
       0,
       1
   )
 )</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <description>Formula to build the string for the duplicate renter name and email to be displayed in the error message for duplicate emails against another renter (staying or incoming).</description>
        <name>frm_RenterDuplicateEmailREnterText</name>
        <dataType>String</dataType>
        <expression>if ( {!Loop_All_Transaction_Parties_for_email_check.Party_Name__c} != &quot;&quot;, {!var_RenterDuplicateEmailListText} + {!Loop_All_Transaction_Parties_for_email_check.Party_Name__c}  + {!LineBreak}, {!var_RenterDuplicateEmailListText} )</expression>
    </formulas>
    <formulas>
        <description>Formula to build the string for the duplicate renter name and email to be displayed in the error message for duplicate emails against the RP or RP user.</description>
        <name>frm_RenterDuplicateEmailRPText</name>
        <dataType>String</dataType>
        <expression>if ( {!Loop_All_Transaction_Parties_for_email_check.Party_Name__c} != &quot;&quot;, {!var_RenterDuplicateEmailRPText} + {!Loop_All_Transaction_Parties_for_email_check.Party_Name__c} + &quot; - &quot; +{!Loop_All_Transaction_Parties_for_email_check.Email_Address__c} + {!LineBreak}, {!var_RenterDuplicateEmailRPText} )</expression>
    </formulas>
    <formulas>
        <description>A formula to determine the value for the &quot;Renter Transfer Option&quot;</description>
        <name>frm_RenterTransferOption</name>
        <dataType>String</dataType>
        <expression>IF ({!Basis_of_transfer} = &quot;Without Consent&quot;, &quot;Removal of Renter&quot;,
if ({!Basis_of_transfer} = &quot;With Consent&quot;, &quot;Renter Transfer&quot;, &quot;&quot;))</expression>
    </formulas>
    <formulas>
        <name>var_RenterName</name>
        <dataType>String</dataType>
        <expression>if (ispickval({!var_NewRenterSingle.Renter_Type__c} ,&quot;Individual&quot;), {!var_NewRenterSingle.First_Name__c} + &quot; &quot; + {!var_NewRenterSingle.Family_Name__c}, {!var_NewRenterSingle.Company_Name__c})</expression>
    </formulas>
    <interviewLabel>TRansfer this bond - Renter Transfer - New {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Transfer this bond - Renter Transfer</label>
    <loops>
        <description>Loops the AllTP Collection to add the transaction number which has been returned from Salesforce.</description>
        <name>Loop_all_TP_s_to_add_Txn_Number</name>
        <label>Loop all TP&apos;s to add transaction number</label>
        <locationX>2677</locationX>
        <locationY>7676</locationY>
        <collectionReference>var_AllTransactionParties</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Transaction_Number_to_TP_Records</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Filter_Incoming_Renters</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops all new transaction parties (staying and new) for duplicate email checks.</description>
        <name>Loop_All_Transaction_Parties_for_email_check</name>
        <label>Loop all transaction parties for email check</label>
        <locationX>2501</locationX>
        <locationY>5804</locationY>
        <collectionReference>var_AllStayingIncomingTP</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_Against_other_renters</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_Duplicate_Email_Flag</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops through the content document links from the case record.</description>
        <name>Loop_case_document_links</name>
        <label>Loop case document links</label>
        <locationX>2677</locationX>
        <locationY>9140</locationY>
        <collectionReference>Get_Case_Documents</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Create_new_document_version</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Case</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops the selected renters. These are renters who are leaving the bond.</description>
        <name>Loop_Leaving_TP</name>
        <label>Loop leaving TP</label>
        <locationX>2030</locationX>
        <locationY>1862</locationY>
        <collectionReference>Select_Leaving_Renters.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Leaving_Renter_TP_Details_Single</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Determine_if_reason_for_leaving_is_required</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops the selected Transaction Parties to assign their reason for leaving as entered by the user.</description>
        <name>Loop_New_TPs_To_assign_Reason_for_leaving</name>
        <label>Loop new TPs to assign reason for leaving</label>
        <locationX>2338</locationX>
        <locationY>2486</locationY>
        <collectionReference>var_LeavingTransactionParties</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>TP_matches_Leaving_TP</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_Reasons_for_leaving</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops the reasons for leaving.</description>
        <name>Loop_Reasons_for_leaving</name>
        <label>Loop reasons for leaving</label>
        <locationX>1898</locationX>
        <locationY>2378</locationY>
        <collectionReference>Repeater_Leaving_Renters.AllItems</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Loop_New_TPs_To_assign_Reason_for_leaving</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Selected_leaving_renters_Without_Consent</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops staying renters and creates their TP</description>
        <name>Loops_Existing_Bond_Parties</name>
        <label>Loops existing bond parties</label>
        <locationX>2030</locationX>
        <locationY>3254</locationY>
        <collectionReference>var_allBondParties</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Loop_Selected_Leaving_Renters</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_if_renter_count_is_10_or_less</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops the list of incoming transaction parties so as the Persona Account collection can be built to create the records.</description>
        <name>Loops_Incoming_TP_for_Person_Accounts</name>
        <label>Loops incoming TP for Person Accounts</label>
        <locationX>2677</locationX>
        <locationY>8084</locationY>
        <collectionReference>Filter_Incoming_Renters</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Create_Person_Account</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Filter_Staying_and_Leaving_TP_s</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops staying renters and creates their TP</description>
        <name>LoopsExistingBondParties</name>
        <label>Loops existing bond parties - no leaving parties</label>
        <locationX>2514</locationX>
        <locationY>1862</locationY>
        <collectionReference>var_allBondParties</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Staying_Renter_TP_Details_Single_All</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_if_renter_count_is_10_or_less</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Create a new TP for an incoming renter</description>
        <name>Create_Incoming_Renter_TP</name>
        <label>Create incoming renter TP</label>
        <locationX>2765</locationX>
        <locationY>8408</locationY>
        <connector>
            <targetReference>Loops_Incoming_TP_for_Person_Accounts</targetReference>
        </connector>
        <inputAssignments>
            <field>Company_Name__c</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_of_Birth__c</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Date_of_Birth__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email_Address__c</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Email_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>First_Name__c</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.First_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Last_Name__c</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Last_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Mobile_Number__c</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Mobile_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>var_NewPersonAccountRecordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_Type__c</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Renter_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter__c</field>
            <value>
                <elementReference>var_newPersonAccountContactIId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_position__c</field>
            <value>
                <stringValue>Incoming</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Role__c</field>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transaction__c</field>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <description>Creates a Person Account Record</description>
        <name>Create_Person_Account</name>
        <label>Create Person Account</label>
        <locationX>2765</locationX>
        <locationY>8192</locationY>
        <assignRecordIdToReference>var_NewPersonAccountRecordId</assignRecordIdToReference>
        <connector>
            <targetReference>Get_Person_ID_Contact_Record</targetReference>
        </connector>
        <inputAssignments>
            <field>Account_Type__c</field>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Company_Name__pc</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Company_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>FirstName</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.First_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LastName</field>
            <value>
                <elementReference>frm_PersonAccountLastName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonBirthdate</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Date_of_Birth__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonEmail</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Email_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PersonMobilePhone</field>
            <value>
                <elementReference>Loops_Incoming_TP_for_Person_Accounts.Mobile_Number__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>var_PersonAccountRecordTypeId</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordCreates>
    <recordCreates>
        <description>Creates the transaction Party Record for the Rental Provider</description>
        <name>Create_RP_Transaction_Party</name>
        <label>Create RP Transaction Party</label>
        <locationX>2677</locationX>
        <locationY>8924</locationY>
        <connector>
            <targetReference>Get_Case_Documents</targetReference>
        </connector>
        <inputAssignments>
            <field>Company_Name__c</field>
            <value>
                <elementReference>var_BondRecordId.Rental_Provider__r.RTBA_Registered_Name__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Email_Address__c</field>
            <value>
                <elementReference>var_BondRecordId.Rental_Provider__r.Transaction_notices__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Mobile_Number__c</field>
            <value>
                <elementReference>var_BondRecordId.Rental_Provider__r.PersonMobilePhone</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Agreed</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>var_BondRecordId.Rental_Provider__r.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter__c</field>
            <value>
                <elementReference>Get_Contact_ID_for_a_L4_RP_User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Role__c</field>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transaction__c</field>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <description>Creates the Staying and Leaving transaction parties for the transfer transaction.</description>
        <name>Create_Staying_and_Leaving_Transaction_Parties</name>
        <label>Create staying and leaving Transaction Parties</label>
        <locationX>2677</locationX>
        <locationY>8708</locationY>
        <connector>
            <targetReference>Get_Contact_ID_for_a_L4_RP_User</targetReference>
        </connector>
        <inputReference>Filter_Staying_and_Leaving_TP_s</inputReference>
    </recordCreates>
    <recordCreates>
        <description>Create the new Renter Transfer transaction record.</description>
        <name>Create_transaction_record</name>
        <label>Create transaction record</label>
        <locationX>2677</locationX>
        <locationY>7460</locationY>
        <assignRecordIdToReference>var_NewTransactionRecordId</assignRecordIdToReference>
        <connector>
            <targetReference>Assign_All_TP_Collections_to_Main_TP_Collection</targetReference>
        </connector>
        <inputAssignments>
            <field>Additional_Address_Details__c</field>
            <value>
                <elementReference>var_BondRecordId.Additional_Address_Details__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bond_Paid_By__c</field>
            <value>
                <elementReference>var_BondRecordId.Bond_Paid_By__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Bond__c</field>
            <value>
                <elementReference>var_BondRecordId.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Country__c</field>
            <value>
                <elementReference>var_BondRecordId.Country__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Court_Reference_Number__c</field>
            <value>
                <elementReference>Court_reference_number</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Date_Of_Transfer__c</field>
            <value>
                <elementReference>Date_of_transfer</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Funding_Source__c</field>
            <value>
                <stringValue>Private</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Modification_Bond_Component__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Number_Of_Bedrooms__c</field>
            <value>
                <elementReference>var_BondRecordId.Number_of_Bedrooms__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Origin__c</field>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Postcode__c</field>
            <value>
                <elementReference>var_BondRecordId.Postcode__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Premises_Type_Other__c</field>
            <value>
                <elementReference>var_BondRecordId.Premises_Type_Other__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Premises_Type__c</field>
            <value>
                <elementReference>var_BondRecordId.Premises_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>var_TransactionRecordType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Rental_Provider__c</field>
            <value>
                <elementReference>var_BondRecordId.Rental_Provider__r.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Renter_Transfer_Option__c</field>
            <value>
                <elementReference>frm_RenterTransferOption</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Revision_Number__c</field>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>State__c</field>
            <value>
                <elementReference>var_BondRecordId.State__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Pending with RTBA</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Street_Address__c</field>
            <value>
                <elementReference>var_BondRecordId.Street_Address__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Submission_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Suburb__c</field>
            <value>
                <elementReference>var_BondRecordId.Suburb__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Period_months__c</field>
            <value>
                <elementReference>var_BondRecordId.Tenancy_Period_months__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Start_Date__c</field>
            <value>
                <elementReference>var_BondRecordId.Tenancy_Start_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Tenancy_Type__c</field>
            <value>
                <elementReference>var_BondRecordId.Tenancy_Type__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transfer_Option__c</field>
            <value>
                <elementReference>frm_RenterTransferOption</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Type__c</field>
            <value>
                <stringValue>Renter Transfer</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>VCAT_Order_Number__c</field>
            <value>
                <elementReference>VCAT_order_number</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Weekly_Rental_Amount__c</field>
            <value>
                <elementReference>var_BondRecordId.Weekly_Rental_Amount__c</elementReference>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordCreates>
    <recordLookups>
        <description>Obtain the case details from Salesforce for the provided case number.</description>
        <name>Get_Case_Details</name>
        <label>Get case details</label>
        <locationX>2919</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Case_Found</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Rolleback_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseNumber</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Please_provide_your_case_number</elementReference>
            </value>
        </filters>
        <filters>
            <field>Category__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Renter Transfer</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>CaseNumber</queriedFields>
        <queriedFields>Status</queriedFields>
        <queriedFields>Category__c</queriedFields>
        <queriedFields>Subject</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>DML to get the case document links which can then be used to create a new content document record for the transaction and it&apos;s link to the document.</description>
        <name>Get_Case_Documents</name>
        <label>Get Case documents</label>
        <locationX>2677</locationX>
        <locationY>9032</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_case_document_links</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case_Details.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContentDocumentLink</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>ContentDocumentId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Obtain a L4 user contact ID for use with the TP.</description>
        <name>Get_Contact_ID_for_a_L4_RP_User</name>
        <label>Get Contact ID for a L4 RP user</label>
        <locationX>2677</locationX>
        <locationY>8816</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_RP_Transaction_Party</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_BondRecordId.Rental_Provider__r.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Access_Level__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>L4</stringValue>
            </value>
        </filters>
        <filters>
            <field>User_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get all of the non-transfer pending transactions for the bond</description>
        <name>Get_Pending_Transactions_non_transfer</name>
        <label>Get pending transactions (non-transfer)</label>
        <locationX>1957</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Transfer_Transaction_Records</targetReference>
        </connector>
        <filterLogic>1 AND 9 AND (2 OR 3 OR 4 OR 5 OR 6 OR 7 OR 8)</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_BondRecordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending Payment</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending Response</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with Another Rental Provider</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with RP</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with RTBA</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with Transferee</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>To Be Processed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Type__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Rental Provider Transfer</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Transaction__c</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the Person Account recrod type Id.</description>
        <name>Get_Person_Account_Record_Type</name>
        <label>Get Person Account record type</label>
        <locationX>2677</locationX>
        <locationY>7352</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_transaction_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>PersonAccount</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>var_PersonAccountRecordTypeId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Gets the associated contact record from the new Person Account Record so as it can be used in the creation of the new TP.</description>
        <name>Get_Person_ID_Contact_Record</name>
        <label>Get Person ID Contact record</label>
        <locationX>2765</locationX>
        <locationY>8300</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Incoming_Renter_TP</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_NewPersonAccountRecordId</elementReference>
            </value>
        </filters>
        <object>Account</object>
        <outputAssignments>
            <assignToReference>var_newPersonAccountContactIId</assignToReference>
            <field>PersonContactId</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get the bond parties associated with the bond include active renters and the RP.</description>
        <name>Get_RenterBond_Parties</name>
        <label>Get renter bond parties</label>
        <locationX>2501</locationX>
        <locationY>1214</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Count_of_Existing_bond_Parties</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_BondRecordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </filters>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </filters>
        <object>Bond_Party__c</object>
        <outputReference>var_allBondParties</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Email_Address__c</queriedFields>
        <queriedFields>Renter_Name__c</queriedFields>
        <queriedFields>Status__c</queriedFields>
        <queriedFields>Date_of_Birth__c</queriedFields>
        <queriedFields>Mobile_Number__c</queriedFields>
        <queriedFields>Renter__c</queriedFields>
        <queriedFields>Company_Name__c</queriedFields>
        <queriedFields>First_Name__c</queriedFields>
        <queriedFields>Family_Name__c</queriedFields>
        <queriedFields>Rental_Provider__c</queriedFields>
        <queriedFields>Role__c</queriedFields>
        <queriedFields>Renter_Type__c</queriedFields>
    </recordLookups>
    <recordLookups>
        <description>Get a list of RP user emails which can be used to check against the renter emails for a duplicate.</description>
        <name>Get_RP_user_records</name>
        <label>Get RP user records</label>
        <locationX>2501</locationX>
        <locationY>5396</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Are_all_existing_renters_leaving</targetReference>
        </connector>
        <filterLogic>1 AND ( 2 OR 3)</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_BondRecordId.Rental_Provider__r.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>User_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </filters>
        <filters>
            <field>User_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Frozen</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Email</queriedFields>
        <queriedFields>Username</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets the correct record type for the transfer transaction.</description>
        <name>Get_the_record</name>
        <label>Get the record</label>
        <locationX>2677</locationX>
        <locationY>7244</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Person_Account_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Transfer</stringValue>
            </value>
        </filters>
        <object>RecordType</object>
        <outputAssignments>
            <assignToReference>var_TransactionRecordType</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get the new transaction number for display in the</description>
        <name>Get_Transaction_Number</name>
        <label>Get transaction number</label>
        <locationX>2677</locationX>
        <locationY>9548</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </filters>
        <object>Transaction__c</object>
        <outputAssignments>
            <assignToReference>var_newTransactionNumberDisplay</assignToReference>
            <field>Name</field>
        </outputAssignments>
    </recordLookups>
    <recordLookups>
        <description>Get all of the transfer transactions. These are stored differently to other transaction types, hence the reason for the independedn get_records.</description>
        <name>Get_Transfer_Transaction_Records</name>
        <label>Get transfer transaction records</label>
        <locationX>1957</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_counts_of_pending_transactions</targetReference>
        </connector>
        <filterLogic>1 AND ( 2 OR 3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9)</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>var_BondRecordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with RTBA</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending Response</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending Payment</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with Another Rental Provider</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending eith Transferee</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with Renter</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Pending with RP</stringValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>To Be Processed</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Transfer_Transaction_Bonds__c</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordRollbacks>
        <description>Rolls back records</description>
        <name>Rollback_Records</name>
        <label>Rollback Records</label>
        <locationX>2941</locationX>
        <locationY>9548</locationY>
        <connector>
            <targetReference>DML_Error_Screen</targetReference>
        </connector>
    </recordRollbacks>
    <recordRollbacks>
        <description>Rolls back records in the case of a DML error.</description>
        <name>Rolleback_Records</name>
        <label>Rollback records</label>
        <locationX>3601</locationX>
        <locationY>782</locationY>
    </recordRollbacks>
    <recordUpdates>
        <description>Update the case to link the transaction.</description>
        <name>Update_Case</name>
        <label>Update case</label>
        <locationX>2677</locationX>
        <locationY>9440</locationY>
        <connector>
            <targetReference>Get_Transaction_Number</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Rollback_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Case_Details.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Transaction_Number__c</field>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <screens>
        <description>A screen asking the user if they would like to add more renters.</description>
        <name>Add_More_Renters_Screen</name>
        <label>Add more renters screen</label>
        <locationX>2237</locationX>
        <locationY>4778</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Add_Another_Renter</targetReference>
        </connector>
        <fields>
            <name>Do_you_wish_to_add_another_renter</name>
            <choiceReferences>ch_AddRentersYes</choiceReferences>
            <choiceReferences>ch_AddRentersNo</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Do you wish to add another renter?</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Add_new_renters</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>This screen will present the user with the question asking if additional renters will be added to the bond.</description>
        <name>Add_Renters_Initial_Screen</name>
        <label>Add renters initial screen</label>
        <locationX>2600</locationX>
        <locationY>4130</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Adding_new_renters</targetReference>
        </connector>
        <fields>
            <name>Do_you_wish_to_any_new_renters_to_the_bond</name>
            <choiceReferences>ch_AddRentersYes</choiceReferences>
            <choiceReferences>ch_AddRentersNo</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Do you wish to any new renters to the bond?</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Add_new_renters</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A screen allwoign the user to specify the basis for transfer.</description>
        <name>Basis_of_Transfer_Screen</name>
        <label>Basis of transfer screen</label>
        <locationX>2501</locationX>
        <locationY>998</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Supporting_Documents_Screen</targetReference>
        </connector>
        <fields>
            <name>Basis_of_transfer</name>
            <choiceReferences>ch_WithoutConsent</choiceReferences>
            <choiceReferences>ch_WithConsent</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Basis of transfer</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Basis_for_transfer_stage</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>Error screen to be shown when the bond has a status of Closed or Cancelled</description>
        <name>Bond_Closed_Cancelled_Error_Screen</name>
        <label>Bond Closed / Cancelled Error Screen</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Bond_ClosedCancelled_Error_Text</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(192, 12, 12); background-color: rgb(255, 255, 255);&quot;&gt;&lt;span class=&quot;ql-cursor&quot;&gt;﻿&lt;/span&gt;A renter transfer on this bond is not possible because the status is either &apos;Closed&apos; or &apos;Cancelled&apos;.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Unable_to_complete_renter_transfer</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>Error screen to be shown when the bond has a status of Suspended.</description>
        <name>Bond_Suspended_Error_Screen</name>
        <label>Bond Suspended Error Screen</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Bond_Suspended_Error_Text</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(192, 12, 12);&quot;&gt;﻿A renter transfer on this bond is not possible because the bond has been Suspended.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Unable_to_complete_renter_transfer</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A case has been found and this screen will display the details.</description>
        <name>Case_Details_Screen</name>
        <label>Case details screen</label>
        <locationX>2501</locationX>
        <locationY>890</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Basis_of_Transfer_Screen</targetReference>
        </connector>
        <fields>
            <name>CaseDetails_Text</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 18px;&quot;&gt;Case Details&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Subject:&lt;/strong&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt; {!Get_Case_Details.Subject}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Category:&lt;/strong&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt; {!Get_Case_Details.Category__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Click next to proceed or back to re-enter case number&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Case_Details_Stage</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>An error screen to be shown when a case cannot be found based on the number provided by the users.</description>
        <name>Case_Not_Found_Error_Screen</name>
        <label>Case not found error screen</label>
        <locationX>3337</locationX>
        <locationY>890</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <fields>
            <name>CaseNotFound_ErrorText</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(192, 2, 2);&quot;&gt;Case number &quot;{!Please_provide_your_case_number}&quot; could not be found. Please check and re-enter the case number.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Unable_to_complete_renter_transfer</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A screen for accepting the case number.</description>
        <name>Case_Number_Screen</name>
        <label>Case number screen</label>
        <locationX>2919</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Get_Case_Details</targetReference>
        </connector>
        <fields>
            <name>Please_provide_your_case_number</name>
            <dataType>String</dataType>
            <fieldText>Please provide your case number:</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;span style=&quot;color: rgb(192, 12, 12); background-color: rgb(255, 255, 255);&quot;&gt;The case number must contains between 8 and 20 numeric characters only. Please review. &lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!Please_provide_your_case_number}, &quot;^[0-9]{8,20}$&quot;)</formulaExpression>
            </validationRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Case_Details_Stage</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A screen to confirm the renter details.</description>
        <name>Confirm_Details_Screen</name>
        <label>Confirm details screen</label>
        <locationX>2677</locationX>
        <locationY>7136</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Get_the_record</targetReference>
        </connector>
        <fields>
            <name>Confirm_Bond_Details</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 22px;&quot;&gt;Confirm Bond Details&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Bond Details:&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Bond Number:&lt;/strong&gt; {!var_BondRecordId.Name}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Rented Property Address: &lt;/strong&gt;{!var_BondRecordId.Rented_Property_Address__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Current Bond Amount: &lt;/strong&gt;{!var_CurrentBondAmountDisplay}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>DateofTransfer</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Date of Transfer:&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; {!Date_of_transfer}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Staying_and_Incoming_Renters_ConfirmDetails</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Transaction_Party__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Staying and incoming renters</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>var_AllStayingIncomingTP</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Party_Name__c&quot;,&quot;guid&quot;:&quot;column-cdbe&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Renter name&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Party Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Date_of_Birth__c&quot;,&quot;guid&quot;:&quot;column-3a76&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Date of birth&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Date of Birth&quot;,&quot;type&quot;:&quot;date-local&quot;},{&quot;apiName&quot;:&quot;Email_Address__c&quot;,&quot;guid&quot;:&quot;column-fe51&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Email address&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Email Address&quot;,&quot;type&quot;:&quot;email&quot;},{&quot;apiName&quot;:&quot;Mobile_Number__c&quot;,&quot;guid&quot;:&quot;column-abdd&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Mobile number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Mobile Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Renter_Type__c&quot;,&quot;guid&quot;:&quot;column-5483&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Renter Type&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <fields>
            <name>Leaving_Renters_ConfirmDetails</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Transaction_Party__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Leaving renters</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>NO_SELECTION</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>var_LeavingTransactionParties</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Party_Name__c&quot;,&quot;guid&quot;:&quot;column-05be&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Renter name&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Party Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Date_of_Birth__c&quot;,&quot;guid&quot;:&quot;column-7b20&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Date of birth&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Date of Birth&quot;,&quot;type&quot;:&quot;date-local&quot;},{&quot;apiName&quot;:&quot;Email_Address__c&quot;,&quot;guid&quot;:&quot;column-6e1b&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Email address&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Email Address&quot;,&quot;type&quot;:&quot;email&quot;},{&quot;apiName&quot;:&quot;Mobile_Number__c&quot;,&quot;guid&quot;:&quot;column-7c8c&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Mobile number&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Mobile Number&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Submit</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Confirm_details</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A standard error screen which all fault paths lead to.</description>
        <name>DML_Error_Screen</name>
        <label>DML Error Screen</label>
        <locationX>2941</locationX>
        <locationY>9656</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>DMLErrorScreenText</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(234, 0, 30);&quot;&gt;Something went wrong. Please contact your system administrator.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30);&quot;&gt;Please provide you system administrator with the following details:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Error</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>Error screen to be displayed when there is a duplicate email detected.</description>
        <name>Duplicate_Email_Error_Screen</name>
        <label>Duplicate email error screen</label>
        <locationX>2325</locationX>
        <locationY>7136</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <fields>
            <name>DuplicateEmailError_RenterText</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;{!ttemp_Duplicate_Email_Renter}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>var_RenterDuplicateEmailListText</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Dupliocate_Email_Renter_RP</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;{!ttemp_Duplciate_Renter_Email_RP}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>var_RenterDuplicateEmailRPText</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Unable_to_complete_renter_transfer</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>Error screen to be shown when the bond is funded by Homes Victoria.</description>
        <name>Homes_Victoria_Funded_Error_Screen</name>
        <label>Homes Victoria Funded Error Screen</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Bond_HVFunded_Error_Text</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(192, 12, 12);&quot;&gt;A rental transfer for this bond is not available because a loan was provided by Homes Victoria to a current renter.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Unable_to_complete_renter_transfer</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A screen to accept the new renter details</description>
        <name>New_Renter_Details_Screen</name>
        <label>New renter details screen</label>
        <locationX>2391</locationX>
        <locationY>4346</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Assign_New_Renter_Details_to_Collection_Variable</targetReference>
        </connector>
        <fields>
            <name>LastPartyAddText</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(192, 2, 2);&quot;&gt;Please note:&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;You may only add one more renter to this bond.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>var_CountAllTPs</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <numberValue>9.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>OnceActiveRenterText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;There must be a least one active renter on a bond.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>var_CountAllTPs</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <numberValue>0.0</numberValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>New_Renter_Type</name>
            <choiceReferences>ch_NewRenterType</choiceReferences>
            <dataType>String</dataType>
            <fieldText>New Renter Type</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>New_Renter_First_Name</name>
            <dataType>String</dataType>
            <fieldText>First name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(192, 2, 2);&quot;&gt;&lt;span class=&quot;ql-cursor&quot;&gt;﻿&lt;/span&gt;Up to 40 characters are allowed for the First Name containing the following: A-Z, a-z, -, &apos;, () and space.&lt;/strong&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!New_Renter_First_Name}, &quot;^[A-Za-z&apos;()\\-\\s]{1,40}$&quot;)</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>New_Renter_Type</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Individual</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>New_Renter_Family_Name</name>
            <dataType>String</dataType>
            <fieldText>Family name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(192, 2, 2);&quot;&gt;Up to 80 characters are allowed for the First Name containing the following: A-Z, a-z, -, &apos;, () and space.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!New_Renter_Family_Name}, &quot;^[A-Za-z&apos;()\\-\\s]{1,80}$&quot;)</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>New_Renter_Type</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Individual</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>New_Renter_Date_of_birth</name>
            <dataType>Date</dataType>
            <fieldText>Date of birth</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;The renter must be between 16 and 100 years of age.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>IF(AND({!frm_RenterAge}&lt;=100,{!frm_RenterAge}&gt;=16), TRUE, FALSE)</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>New_Renter_Type</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Individual</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>New_Renter_Company_Name</name>
            <dataType>String</dataType>
            <fieldText>Company name</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;Up to 80 characters are allowed for the Company Name containing the following: (A-Z, a-z) ,numbers (0-9), @, hash (#), ampersand (&amp;amp;), apostrophe, brackets, hyphens, commas, full stops and space.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!New_Renter_Company_Name}, &quot;^[A-Za-z0-9@#&amp;&apos;()\\-,\\.\\s]{1,80}$&quot;)</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>New_Renter_Type</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Company</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>New_Renter_Mobile_Number</name>
            <dataType>String</dataType>
            <fieldText>Mobile number</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(192, 2, 2);&quot;&gt;Mobile number must be a 10-digit number starting with 04.&lt;/strong&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!New_Renter_Mobile_Number}, &quot;^04[0-9]{8}$&quot;)</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>New_Renter_Type</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>New_Renter_Email_2</name>
            <dataType>String</dataType>
            <fieldText>Email</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>false</booleanValue>
            </isDisabled>
            <isReadOnly>
                <booleanValue>false</booleanValue>
            </isReadOnly>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(192, 2, 2);&quot;&gt;Please enter a valid email address.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>OR(ISBLANK({!New_Renter_Email_2}), 
REGEX({!New_Renter_Email_2}, &quot;^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$&quot;))</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>New_Renter_Type</leftValueReference>
                    <operator>IsNull</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Add_new_renters</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A screen to display an error when there have been no changes made.</description>
        <name>No_Change_Error_Screen</name>
        <label>No change error screen</label>
        <locationX>2897</locationX>
        <locationY>4454</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <fields>
            <name>NoChangeErrorText</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(192, 2, 2);&quot;&gt;No Incoming or leaving renters have been specified.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;Please click the &quot;Back&quot; button to go back and change your selections, or click &quot;Close&quot; to exit.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Unable_to_complete_renter_transfer</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>Error screen to be shown when the bond has a status of Closed or Cancelled</description>
        <name>Pending_Transactions_Error_Screen</name>
        <label>Pending Transactions Error Screen</label>
        <locationX>842</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>PendingTransactions_ErrorText</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(192, 12, 12);&quot;&gt;A transaction is currently in progress for this bond. Another transaction for the same bond cannot be started until the previous transaction is completed.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Unable_to_complete_renter_transfer</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A screen which allows the user to specify the reason for leaving for each leaving renter.</description>
        <name>Reason_for_Leaving</name>
        <label>Reason for leaving</label>
        <locationX>1898</locationX>
        <locationY>2270</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Loop_Reasons_for_leaving</targetReference>
        </connector>
        <fields>
            <name>Repeater_Leaving_Renters</name>
            <fieldType>Repeater</fieldType>
            <fields>
                <name>LeavingRenterName_Text</name>
                <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Renter Name: &lt;/strong&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;{!Select_Leaving_Renters.selectedRows[$EachItem].Renter_Name__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                <fieldType>DisplayText</fieldType>
            </fields>
            <fields>
                <name>ReasonForLeaving</name>
                <dataType>String</dataType>
                <fieldText>Reason for leaving</fieldText>
                <fieldType>InputField</fieldType>
                <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                <isRequired>true</isRequired>
            </fields>
            <fields>
                <name>SelectedLeavingRenterContactID</name>
                <dataType>String</dataType>
                <defaultValue>
                    <elementReference>Select_Leaving_Renters.selectedRows[$EachItem].Renter__r.Id</elementReference>
                </defaultValue>
                <fieldText>Renter contact ID</fieldText>
                <fieldType>InputField</fieldType>
                <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
                <isDisabled>
                    <booleanValue>true</booleanValue>
                </isDisabled>
                <isReadOnly>
                    <booleanValue>true</booleanValue>
                </isReadOnly>
                <isRequired>false</isRequired>
            </fields>
            <inputParameters>
                <name>collection</name>
                <value>
                    <elementReference>Select_Leaving_Renters.selectedRows</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>canAddItem</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>canRemoveItem</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <isRequired>false</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Leaving_renters</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>The end success screen showing a message to the user</description>
        <name>Success_Screen</name>
        <label>Success screen</label>
        <locationX>2677</locationX>
        <locationY>9656</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMessageText</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 14px; color: rgb(29, 179, 2);&quot;&gt;Successful renter transfer submission&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Your renter transfer has been submitted awaiting internal review.&lt;/p&gt;&lt;p&gt;The transaction number is &lt;a href=&quot;{!frm_NewTransactionLink}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;{!var_newTransactionNumberDisplay}&lt;/a&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Successfulsubmission</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A screen allowing the user to specify the type and details of supporting documents which have been provided. The documents are attached to the case and will be associated with the resulting transaction.</description>
        <name>Supporting_Documents_Screen</name>
        <label>Supporting documents screen</label>
        <locationX>2501</locationX>
        <locationY>1106</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Get_RenterBond_Parties</targetReference>
        </connector>
        <fields>
            <name>Supporting_document_type</name>
            <choiceReferences>ch_VCATOrder</choiceReferences>
            <choiceReferences>ch_CourtReferenceNumber</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Supporting document type</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Basis_of_transfer</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>ch_WithoutConsent</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>VCAT_order_number</name>
            <dataType>String</dataType>
            <fieldText>VCAT order number</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2); background-color: rgb(255, 255, 255);&quot;&gt;Please enter a complete VCAT order number.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>OR(
AND(
   REGEX({!VCAT_order_number}, &quot;^RT[0-9]{2}[0-9]{1,6}$&quot;),
   VALUE(MID({!VCAT_order_number}, 3, 2)) &lt;= MOD(YEAR(TODAY()), 100)
),
AND(
  REGEX({!VCAT_order_number}, &quot;^R[0-9]{4}/[0-9]{5}/[0-9]{2}$&quot;),
    VALUE(MID({!VCAT_order_number}, 2, 4)) &lt;= YEAR(TODAY())
)
 )</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Basis_of_transfer</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>ch_WithoutConsent</elementReference>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Supporting_document_type</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>ch_VCATOrder</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Court_reference_number</name>
            <dataType>String</dataType>
            <fieldText>Court reference number</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2); background-color: rgb(255, 255, 255);&quot;&gt;Court reference number must be alphanumeric and no more than 20 characters long.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!Court_reference_number}, &quot;^[a-zA-Z0-9]{1,20}$&quot;)</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Basis_of_transfer</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>ch_WithoutConsent</elementReference>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>Supporting_document_type</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>ch_CourtReferenceNumber</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Date_of_transfer</name>
            <dataType>Date</dataType>
            <fieldText>Date of transfer</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Basis_of_transfer</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>ch_WithConsent</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Supporting_documents_stage</elementReference>
        </stageReference>
    </screens>
    <screens>
        <description>A displaying the active renters on the bond and allowing the user to select any renters who are leaving. Selection is optional.</description>
        <name>Transfer</name>
        <label>Select leaving renters screen</label>
        <locationX>2272</locationX>
        <locationY>1538</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Assign_count_of_leaving_renters</targetReference>
        </connector>
        <fields>
            <name>Select_Leaving_Renters</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Bond_Party__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Select renters who are leaving the bond</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>var_allBondParties</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Renter_Name__c&quot;,&quot;guid&quot;:&quot;column-b80f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Renter Name&quot;,&quot;type&quot;:&quot;customRichText&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Leaving_renters</elementReference>
        </stageReference>
    </screens>
    <stages>
        <description>Stage for adding new renters to the bond</description>
        <name>Add_new_renters</name>
        <isActive>false</isActive>
        <label>Add new renters</label>
        <stageOrder>20</stageOrder>
    </stages>
    <stages>
        <description>A stage for the basis of transfer</description>
        <name>Basis_for_transfer_stage</name>
        <isActive>false</isActive>
        <label>Basis for transfer</label>
        <stageOrder>5</stageOrder>
    </stages>
    <stages>
        <description>A stage for providing the case details.</description>
        <name>Case_Details_Stage</name>
        <isActive>false</isActive>
        <label>Provide case details</label>
        <stageOrder>1</stageOrder>
    </stages>
    <stages>
        <description>A stage for confirming the details of the transfer.</description>
        <name>Confirm_details</name>
        <isActive>false</isActive>
        <label>Confirm details</label>
        <stageOrder>25</stageOrder>
    </stages>
    <stages>
        <description>Error Stage</description>
        <name>Error</name>
        <isActive>false</isActive>
        <label>Error</label>
        <stageOrder>45</stageOrder>
    </stages>
    <stages>
        <description>A stage for leaving renters selection.</description>
        <name>Leaving_renters</name>
        <isActive>false</isActive>
        <label>Leaving renters</label>
        <stageOrder>15</stageOrder>
    </stages>
    <stages>
        <description>Stage for the success page</description>
        <name>Successfulsubmission</name>
        <isActive>false</isActive>
        <label>Successful submission</label>
        <stageOrder>30</stageOrder>
    </stages>
    <stages>
        <description>A stage for specifying the details of the supporting documents.</description>
        <name>Supporting_documents_stage</name>
        <isActive>false</isActive>
        <label>Supporting documents</label>
        <stageOrder>10</stageOrder>
    </stages>
    <stages>
        <description>This stage is used for screens which display messages informing the user that they are unable to continue with the renter transfer.</description>
        <name>Unable_to_complete_renter_transfer</name>
        <isActive>false</isActive>
        <label>Unable to complete renter transfer</label>
        <stageOrder>40</stageOrder>
    </stages>
    <start>
        <locationX>1831</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Pending_Transactions_non_transfer</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>A line break text template</description>
        <name>LineBreak</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;br&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>ttemp_Duplciate_Renter_Email_RP</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;strong style=&quot;color: rgb(192, 2, 2);&quot;&gt;Each renter email address must not be the same as any email address associated with the rental provider.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;Please provide a unique email address for the listed renters.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;{!frm_RenterDuplicateEmailRPText}&lt;/span&gt;&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <description>Text template for the error message to be shown when there a duplicate renter email is found between all renters.</description>
        <name>ttemp_Duplicate_Email_Renter</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;&lt;strong style=&quot;color: rgb(192, 2, 2);&quot;&gt;Each renter must have unique email address.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;Please provide a unique email address for the listed renters:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(192, 2, 2);&quot;&gt;{!frm_RenterDuplicateEmailREnterText}&lt;/span&gt;&lt;/p&gt;</text>
    </textTemplates>
    <transforms>
        <description>Transform element to set up the leaving renters collection which can then be looped to assign the TP details. This transform element is used when the basis of transfer is &quot;With Consent&quot;</description>
        <name>Selected_Leaving_Renters_With_Consent</name>
        <label>Selected leaving renters - With Consent</label>
        <locationX>2162</locationX>
        <locationY>2270</locationY>
        <connector>
            <targetReference>Assign_leaving_renters_to_new_collection</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>var_LeavingTransactionParties[$EachItem].Renter__c</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <transforms>
        <description>Transform element to set up the leaving renters collection which can then be looped to assign the TP details. This transform element is used when the basis of transfer is &quot;Without Consent&quot;</description>
        <name>Selected_leaving_renters_Without_Consent</name>
        <label>Selected leaving renters - Without Consent</label>
        <locationX>1898</locationX>
        <locationY>3062</locationY>
        <connector>
            <targetReference>Loops_Existing_Bond_Parties</targetReference>
        </connector>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <scale>0</scale>
        <storeOutputAutomatically>true</storeOutputAutomatically>
        <transformValues>
            <transformValueActions>
                <transformType>Map</transformType>
                <value>
                    <elementReference>var_LeavingTransactionPartiesFinal[$EachItem].Renter__c</elementReference>
                </value>
            </transformValueActions>
        </transformValues>
    </transforms>
    <variables>
        <name>currentItem_Filter_Incoming_Renters</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>currentItem_Filter_Staying_and_Leaving_TP_s</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>currentItem_RP_user_emails</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
    <variables>
        <description>All Bond Parties</description>
        <name>var_allBondParties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond_Party__c</objectType>
    </variables>
    <variables>
        <name>var_AllStayingIncomingTP</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>This record collection is used to collect all of the transaction party records to be created in Salesforce.</description>
        <name>var_AllTransactionParties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>The final version of the All Transaction PArties which includes the update for a transaction record.</description>
        <name>var_AllTransactionPartiesFinal</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>The bond record for the bond being transferred,</description>
        <name>var_BondRecordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond__c</objectType>
    </variables>
    <variables>
        <name>var_CountAllTPs</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <description>A count of existing active renter bond parties.</description>
        <name>var_countExistingRenterBondParties</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <description>A count of all pending non-transfer transactions.</description>
        <name>var_countPendingNonTransferTransactions</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <description>A count of pending transfer transactions</description>
        <name>var_countPendingTransferTransactions</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <description>A count of the selected leaving bond parties</description>
        <name>var_countSelectedLaveingBondParties</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <description>Current Bond Amount for display on the confirm details screen.</description>
        <name>var_CurrentBondAmountDisplay</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
        <value>
            <elementReference>var_BondRecordId.Current_Bond_Amount__c</elementReference>
        </value>
    </variables>
    <variables>
        <description>A duplicate email flag to be used to trigger a decision path later in the flow</description>
        <name>var_duplicateEmailFlag</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>var_duplicateRenterTPEmailCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <name>var_duplicateRenterTPEmailWithRP</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>A record collection of leaving bond parties</description>
        <name>var_leavingBondParties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond_Party__c</objectType>
    </variables>
    <variables>
        <description>Record collection for leaving transaction parties.</description>
        <name>var_LeavingTransactionParties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>This record collection is used to finalise the leaving transaction parties. It is  what will be used to create the TP&apos;s later in the flow.</description>
        <name>var_LeavingTransactionPartiesFinal</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>The Record Id for the new Person Account contact record.</description>
        <name>var_newPersonAccountContactIId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>The new record Id from a person account</description>
        <name>var_NewPersonAccountRecordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Collection variable for a single renter</description>
        <name>var_NewRenterSingle</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond_Party__c</objectType>
    </variables>
    <variables>
        <description>The new Transaction Party record (Single). Will be added to a collection for later addition to the database</description>
        <name>var_NewRenterTransactionParty</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>The new transaction number to be displayed</description>
        <name>var_newTransactionNumberDisplay</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Record collection for the new transaction parties.</description>
        <name>var_NewTransactionPArties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>Stores the transaction record ID generated by Salesforce, so as it can be used to create Transaction Parties and link the case.</description>
        <name>var_NewTransactionRecordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>The Record type Id for a Person Account</description>
        <name>var_PersonAccountRecordTypeId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to store the text for an error message for a single renter who has an duplicate email address with another renter.</description>
        <name>var_RenterDuplicateEmailListText</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>A variable to store the error text the for a single renter who has a duplicate email address with the RP (Org or user)</description>
        <name>var_RenterDuplicateEmailRPText</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>var_StayingRenterParties</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Party__c</objectType>
    </variables>
    <variables>
        <description>The Record Type ID for a Transfer.</description>
        <name>var_TransactionRecordType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>A list of the unique emails for each renter. This is used in the email duplication check to store emails which have been checked against other renter emails and RP emails, including user emails for RP users.</description>
        <name>var_UnqiueRenterEmailList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
