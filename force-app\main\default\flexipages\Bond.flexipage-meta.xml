<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Bond__c.Amend_bond</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$User.RTBA_User_Type__c}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>MRT</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Bond__c.Generate_New_Bond_Receipt</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$User.RTBA_User_Type__c}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>MRT</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Bond__c.Cancel_Bond</value>
                            <visibilityRule>
                                <booleanFilter>(1 OR 2) AND 3</booleanFilter>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Cancel_Bond}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Reconciliation}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Active</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Bond__c.Transfer_this_bond</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Transfer_Bond}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Bond__c.Refund_this_bond</value>
                            <visibilityRule>
                                <booleanFilter>(1 AND 2 AND (3 OR 4) OR 5)</booleanFilter>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Refund_Bond}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Current_Bond_Amount__c}</leftValue>
                                    <operator>GT</operator>
                                    <rightValue>0</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Active</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Partially Paid</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!$User.Profile.Name}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>System Administrator</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Bond__c.Correction_Repayment</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Reconciliation}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Bond__c.Adhoc_Bond_Repayment</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Reconciliation}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>7</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Rented_Property_Address__c</fieldItem>
                <identifier>RecordRented_Property_Address_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Source__c</fieldItem>
                <identifier>RecordSource_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Type__c</fieldItem>
                <identifier>RecordType_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Modification_Bond_Description__c</fieldItem>
                <identifier>RecordModification_Bond_Description_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Type__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Modification Bond Lodgement</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Comments__c</fieldItem>
                <identifier>RecordComments_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-7e1862f0-25d1-435d-9b83-c6925beffe6d</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Lodgement_Amount__c</fieldItem>
                <identifier>RecordLodgement_Amount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Current_Bond_Amount__c</fieldItem>
                <identifier>RecordCurrent_Bond_Amount_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status__c</fieldItem>
                <identifier>RecordStatus_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bond_Cancellation_Reason__c</fieldItem>
                <identifier>RecordBond_Cancellation_Reason_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Status__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cancelled</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bond_Cancellation_Reason_Other__c</fieldItem>
                <identifier>RecordBond_Cancellation_Reason_Other__cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Status__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cancelled</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Bond_Cancellation_Reason__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Other</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Closed_date__c</fieldItem>
                <identifier>RecordClosed_date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Funding_Source__c</fieldItem>
                <identifier>RecordFunding_Source_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Homes_Victoria_Loan_Number__c</fieldItem>
                <identifier>RecordHomes_Victoria_Loan_Number_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Funding_Source__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Homes Victoria</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Homes_Victoria_Voucher_Number__c</fieldItem>
                <identifier>RecordHomes_Victoria_Voucher_Number_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Funding_Source__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Homes Victoria</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-6ae1f030-261e-4f0f-a0b9-71c2a2589da9</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7e1862f0-25d1-435d-9b83-c6925beffe6d</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-6ae1f030-261e-4f0f-a0b9-71c2a2589da9</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-7c468cbc-0e12-4c9c-88ac-9bb6645aeadc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Rental_Provider__c</fieldItem>
                <identifier>RecordRental_Provider_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.RTBA_Registration_Number__c</fieldItem>
                <identifier>RecordRTBA_Registration_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Rental_Provider_Address__c</fieldItem>
                <identifier>RecordRental_Provider_Address_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Rental_Provider_Email_address__c</fieldItem>
                <identifier>RecordRental_Provider_Email_address_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Rental_Provider_Phone_Number__c</fieldItem>
                <identifier>RecordRental_Provider_Phone_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-dbea7296-f1ed-45dc-9fe3-ba90b7f13263</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>Facet-377ff26c-2d06-4eb2-8caf-38ad83ae36ee</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dbea7296-f1ed-45dc-9fe3-ba90b7f13263</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-377ff26c-2d06-4eb2-8caf-38ad83ae36ee</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ad35f0a1-d706-4d4b-8239-b406b49f5821</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bond_Paid_By__c</fieldItem>
                <identifier>RecordBond_Paid_By_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Funding_Source__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Private</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Date_Bond_Recieved_By_Rental_Provider__c</fieldItem>
                <identifier>RecordDate_Bond_Recieved_By_Rental_Provider_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Bond_Paid_By__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Rental Provider</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Tenancy_Start_Date__c</fieldItem>
                <identifier>RecordTenancy_Start_Date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Tenancy_Period_months__c</fieldItem>
                <identifier>RecordTenancy_Period_months_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-273ccc8b-acd5-4fb5-96ed-2d5789bebbb4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Tenancy_Type__c</fieldItem>
                <identifier>RecordTenancy_Type_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Weekly_Rental_Amount__c</fieldItem>
                <identifier>RecordWeekly_Rental_Amount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Premises_Type__c</fieldItem>
                <identifier>RecordPremises_Type_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Premises_Type_Other__c</fieldItem>
                <identifier>RecordPremises_Type_Other_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Premises_Type__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Other</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Number_of_Bedrooms__c</fieldItem>
                <identifier>RecordNumber_of_Bedrooms_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-43fd6aae-a17a-4401-bb05-b2c4ee7a2a53</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-273ccc8b-acd5-4fb5-96ed-2d5789bebbb4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-43fd6aae-a17a-4401-bb05-b2c4ee7a2a53</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-f3838de5-575b-43d2-82e4-18fdddb4a21e</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DPID__c</fieldItem>
                <identifier>RecordDPID_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DPID_Date__c</fieldItem>
                <identifier>RecordDPID_Date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DPID_Source__c</fieldItem>
                <identifier>RecordDPID_Source_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-b46bf695-c3f5-41e2-9ad6-58db1c921bca</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.LastModifiedById</fieldItem>
                <identifier>RecordLastModifiedByIdField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DPID_Flag__c</fieldItem>
                <identifier>RecordDPID_Flag_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DPID_Match_Result__c</fieldItem>
                <identifier>RecordDPID_Match_Result_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DPID_Match_Code__c</fieldItem>
                <identifier>RecordDPID_Match_Code_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-xwcq6vkl91</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b46bf695-c3f5-41e2-9ad6-58db1c921bca</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-xwcq6vkl91</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-cb272d86-c90f-4418-a538-e3fe4ae2c763</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-7c468cbc-0e12-4c9c-88ac-9bb6645aeadc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Bond Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-ad35f0a1-d706-4d4b-8239-b406b49f5821</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Rental Provider Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-f3838de5-575b-43d2-82e4-18fdddb4a21e</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Bond and Tenancy Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-cb272d86-c90f-4418-a538-e3fe4ae2c763</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Additional Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>Status__c|EQUALS|[&quot;Active&quot;]</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>11</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Bond_Parties__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Role__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Rental_Provider__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Email_Address__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Mobile_Number__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Bond Parties</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Transactions1__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Type__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Submission_Date__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Transaction_Completion_Date__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Transactions (Bond)</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList6</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Transfer_Transaction_Bonds__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>Transaction__c.NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Transaction__c.Type__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Transaction__c.Submission_Date__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Transaction__c.Status__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Transaction__c.Transaction_Completion_Date__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Transfer Transaction Bonds</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Payments__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payer__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payer_Name__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payment_Method__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payment_Status_Date_Time__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payment_Amount__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Payments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Repayments__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payee_Type__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Payee__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Amount__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Repayment_Method__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Repayment_Status_Date_Time__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Repayments</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList7</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>detailTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>inRecordPage</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inTab</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>listName</name>
                    <value>Documents_Bonds</value>
                </componentInstanceProperties>
                <componentName>smartlists:smartFilesList</componentName>
                <identifier>smartlists_smartFilesList</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>inRecordPage</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inTab</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>listName</name>
                    <value>Notices_Bonds</value>
                </componentInstanceProperties>
                <componentName>smartlists:smartFilesList</componentName>
                <identifier>smartlists_smartFilesList3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>inRecordPage</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inTab</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>listName</name>
                    <value>Receipts_Bonds</value>
                </componentInstanceProperties>
                <componentName>smartlists:smartFilesList</componentName>
                <identifier>smartlists_smartFilesList2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>inRecordPage</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inTab</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>listName</name>
                    <value>Email_Messages</value>
                </componentInstanceProperties>
                <componentName>smartlists:smartList</componentName>
                <identifier>smartlists_smartList</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>inRecordPage</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inTab</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>listName</name>
                    <value>MC_Email_Tracking_Bonds</value>
                </componentInstanceProperties>
                <componentName>smartlists:smartList</componentName>
                <identifier>smartlists_smartList2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>inRecordPage</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inTab</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>listName</name>
                    <value>MC_SMS_Bonds</value>
                </componentInstanceProperties>
                <componentName>smartlists:smartList</componentName>
                <identifier>smartlists_smartList3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-4d78e04c-8294-4bc9-bd48-e023e328be19</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Histories</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b0c58257-998a-4ed8-a6bc-db5f2ecab3b8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                    <valueList>
                        <valueListItems>
                            <value>Status__c|EQUALS|[&quot;Inactive&quot;]</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Role__c|EQUALS|[&quot;Renter&quot;]</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Bond_Parties__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>Renter_Name__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Commencement_Date__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Outgoing_Closed_Date__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Bond Party History</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-cc761704-ac27-49f5-aa72-63f31689e534</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Reconciled_Amount__c</fieldItem>
                <identifier>RecordReconciled_Amount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Reconciliation_Difference__c</fieldItem>
                <identifier>RecordReconciliation_Difference_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-7c496e5f-f58f-4351-a813-8487da840f70</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-7c496e5f-f58f-4351-a813-8487da840f70</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column19</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-4a2e6c91-2e07-4dde-9207-6b1b0a7dbfee</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-4a2e6c91-2e07-4dde-9207-6b1b0a7dbfee</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Reconciliation Details</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9bff5d90-4551-4903-9871-0ba6e183cbae</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Recoupment_Status__c</fieldItem>
                <identifier>RecordRecoupment_Status_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Subsequent_Bond__c</fieldItem>
                <identifier>RecordSubsequent_Bond_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-9a72ae6a-bf4a-4153-bc60-6496d2b21053</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Recoupment_Status_Date__c</fieldItem>
                <identifier>RecordRecoupment_Status_Date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-e27bc07c-23c9-4edd-af52-294827989f68</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9a72ae6a-bf4a-4153-bc60-6496d2b21053</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e27bc07c-23c9-4edd-af52-294827989f68</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-8e115ccd-e799-41d1-a1e2-4b87fac9777b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-8e115ccd-e799-41d1-a1e2-4b87fac9777b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Section</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Bond__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Subsequent_Bonds__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Rented_Property_Address__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CREATED_DATE</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Status__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Current_Bond_Amount__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Bonds (Subsequent Bond)</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ec5cbfef-9037-4340-a0d4-8f6226e161cb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>detailTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>detailTab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-4d78e04c-8294-4bc9-bd48-e023e328be19</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Documents and Files</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b0c58257-998a-4ed8-a6bc-db5f2ecab3b8</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Change History</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$User.RTBA_User_Type__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>MRT</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-cc761704-ac27-49f5-aa72-63f31689e534</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Renter History</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9bff5d90-4551-4903-9871-0ba6e183cbae</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Reconciliation</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab4</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.Reconciliation}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-ec5cbfef-9037-4340-a0d4-8f6226e161cb</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Recoupment</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab5</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.Recoupment}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>maintabs</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>maintabs</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$User.RTBA_User_Type__c}</leftValue>
                        <operator>NE</operator>
                        <rightValue>MRT</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Bond</masterLabel>
    <parentFlexiPage>flexipage__default_rec_L</parentFlexiPage>
    <sobjectType>Bond__c</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
    </template>
    <type>RecordPage</type>
</FlexiPage>
