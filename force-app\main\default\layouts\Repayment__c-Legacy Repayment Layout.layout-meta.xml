<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Bond__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Transaction__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Payee_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Repayment_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Repayment_Method__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BSB__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Account_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Mobile_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email_Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Payee__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Payer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Branch_No__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Cheque_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Cheque_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Cheque_No__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Claim_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Clearance_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Repayment_External_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Cleared__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DC_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DC_File_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DE_Report_Remarks__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DE_Report_Run_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DE_Report_Status__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DE_Return_Ac_No__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DE_Return_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DE_Return_File_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DE_Return_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Debit_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Entry_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Excel_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Parent_Transaction_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Pay_Details__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Pay_Success__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Payee_Country_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Payee_Suburb_City__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Payment_Mode__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PDF_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Remarks__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sequence_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DM_CSV_Part__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DM_Upload_Attempt_number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Parent_Repayment__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Incorrect_Repayment_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Incorrect_Repayment__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Incorrect_Repayment_Fee_Waived__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Partial_Bond_Refund_Reason__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Repayment__c.Parent_Repayment__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hMn0000055Xdu</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
