<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <description>Calls an APEX class &quot;CloneContentVersion&quot; to clone the content version for the specified document and link it to the case. It ensures that the metadata associated with the content version record is set correctly.</description>
        <name>Create_new_document_version</name>
        <label>Create new document version</label>
        <locationX>270</locationX>
        <locationY>1298</locationY>
        <actionName>CloneContentVersion</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Loop_case_document_links</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback_Records</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>caseNumber</name>
            <value>
                <elementReference>Get_case_records.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>category</name>
            <value>
                <stringValue>Transactions - Documents</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>contentDocumentId</name>
            <value>
                <elementReference>Loop_case_document_links.ContentDocumentId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>newRecordId</name>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>status</name>
            <value>
                <stringValue>Finalised</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transactionType</name>
            <value>
                <stringValue>Bond Lodgement</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>uploadDocumentTo</name>
            <value>
                <stringValue>Transactions</stringValue>
            </value>
        </inputParameters>
        <nameSegment>CloneContentVersion</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <choices>
        <name>choiceHomesVictoria</name>
        <choiceText>Homes Victoria</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Homes Victoria</stringValue>
        </value>
    </choices>
    <choices>
        <name>choicePrivate</name>
        <choiceText>Private</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Private</stringValue>
        </value>
    </choices>
    <decisions>
        <name>Case_records_exist</name>
        <label>Case records exist</label>
        <locationX>422</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>Case_does_not_exist_error_message</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Case does not exist , display error message</defaultConnectorLabel>
        <rules>
            <name>Case_record_exist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_case_records</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Successfully_found_case_number</targetReference>
            </connector>
            <label>Case record exist</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_for_account_status</name>
        <label>Check for account status</label>
        <locationX>806</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Display_error_screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Status is suspended or deregistered</defaultConnectorLabel>
        <rules>
            <name>Status_is_active_or_monitor</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RP_Org_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Active</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.RP_Org_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Monitor</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Enter_case_number</targetReference>
            </connector>
            <label>Status is active or monitor</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_for_funding_source</name>
        <label>Check for funding source</label>
        <locationX>182</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Call_homes_victoria_subflow</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Call Homes Victoria sub flow</defaultConnectorLabel>
        <rules>
            <name>Call_private_sub_flow</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_payment_funding</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Private</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Call_privately_funded_subflow</targetReference>
            </connector>
            <label>Call private sub flow</label>
        </rules>
    </decisions>
    <description>Action to launch a screen flow to step an internal user through the process of lodging a new bond.</description>
    <environments>Default</environments>
    <formulas>
        <description>Formula to generate an error message when the case number does not meet validation criteria.</description>
        <name>formulaCaseNumberErrorDisplay</name>
        <dataType>String</dataType>
        <expression>IF(
    LEN({!Please_provide_your_case_number}) &gt; 20,
    &quot;Case Number must be 20 characters.&quot;,
    IF(
        NOT(REGEX({!Please_provide_your_case_number}, &quot;^[0-9A-Za-z]$&quot;)),
        &quot;Characters allowed for Case Number are: 0-9, A-Z, a-z&quot;,
        &quot;&quot;
    )
)</expression>
    </formulas>
    <formulas>
        <description>A hyperlink to the new transaciton record.</description>
        <name>LinkedTransactionRecord</name>
        <dataType>String</dataType>
        <expression>LEFT({!$Api.Partner_Server_URL_260}, FIND( &apos;/services&apos;, {!$Api.Partner_Server_URL_260})) &amp; {!var_NewTransactionRecordId}</expression>
    </formulas>
    <interviewLabel>Lodge New Bond {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Lodge New Bond</label>
    <loops>
        <description>Loops through the content document links from the case records</description>
        <name>Loop_case_document_links</name>
        <label>Loop case document links</label>
        <locationX>182</locationX>
        <locationY>1190</locationY>
        <collectionReference>Get_case_documents</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Create_new_document_version</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_case_with_transaction</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <description>DML to get the case document links which can then be used to create a new content document record for the transaction and it&apos;s link to the document.</description>
        <name>Get_case_documents</name>
        <label>Get case documents</label>
        <locationX>182</locationX>
        <locationY>1082</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_case_document_links</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>LinkedEntityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_case_records.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ContentDocumentLink</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>ContentDocumentId</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_case_records</name>
        <label>Get case records</label>
        <locationX>422</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Case_records_exist</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Rollback_Records</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseNumber</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Please_provide_your_case_number</elementReference>
            </value>
        </filters>
        <filters>
            <field>Category__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Bond Lodgement</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordRollbacks>
        <description>Rolls back the records.</description>
        <name>Rollback_Records</name>
        <label>Rollback Records</label>
        <locationX>926</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>DML_Error_Screen</targetReference>
        </connector>
    </recordRollbacks>
    <recordUpdates>
        <description>Update the case record with the new transaction record Id.</description>
        <name>Update_case_with_transaction</name>
        <label>Update case with transaction</label>
        <locationX>182</locationX>
        <locationY>1538</locationY>
        <connector>
            <targetReference>Success_screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_case_records.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Transaction_Number__c</field>
            <value>
                <elementReference>var_NewTransactionRecordId</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <screens>
        <name>Case_does_not_exist_error_message</name>
        <label>Case does not exist error message</label>
        <locationX>662</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <fields>
            <name>Error_message</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(192, 12, 12); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Case number {!Get_case_records.CaseNumber} could not be found. &lt;/span&gt;&lt;span style=&quot;color: rgb(192, 12, 12); font-size: 14px; background-color: rgb(255, 255, 255); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif;&quot;&gt;Please check and re-enter the case number.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Display_error_screen</name>
        <label>Display error screen</label>
        <locationX>1190</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>displayErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(192, 12, 12);&quot;&gt;{!recordId.Name} is {!recordId.RP_Org_Status__c}. You cannot create a new bond lodgement while the Rental Provider is suspended or deregistered.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>Error screen for displaying any DML Errors.</description>
        <name>DML_Error_Screen</name>
        <label>DML Error Screen</label>
        <locationX>926</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>DMLErrorText</name>
            <fieldText>&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(234, 0, 30);&quot;&gt;Something went wrong. Please contact your system administrator.&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30);&quot;&gt;Please provide you system administrator with the following details:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(234, 0, 30);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Enter_case_number</name>
        <label>Enter case number</label>
        <locationX>422</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Get_case_records</targetReference>
        </connector>
        <fields>
            <name>Please_provide_your_case_number</name>
            <dataType>String</dataType>
            <fieldText>Please provide your case number:</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;Case Number must be limited to 20 characters.&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!Please_provide_your_case_number}, &quot;^[0-9A-Za-z&apos;]{1,20}$&quot;)</formulaExpression>
            </validationRule>
        </fields>
        <nextOrFinishButtonLabel>Next</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Funding Source</description>
        <name>Funding_Source</name>
        <label>Funding Source screen</label>
        <locationX>182</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Check_for_funding_source</targetReference>
        </connector>
        <fields>
            <name>FundingSource</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Funding Source&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Select_payment_funding</name>
            <choiceReferences>choicePrivate</choiceReferences>
            <choiceReferences>choiceHomesVictoria</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Select payment funding</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <description>The scree shown once successful submission is completed</description>
        <name>Success_screen</name>
        <label>Success screen</label>
        <locationX>182</locationX>
        <locationY>1646</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessScreenText_Prviate</name>
            <fieldText>&lt;h3&gt;&lt;strong&gt;Successful lodgement submission&lt;/strong&gt;&lt;/h3&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;The transaction number&amp;nbsp;&lt;a href=&quot;{!LinkedTransactionRecord}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;{!var_newTransactionNumber}&lt;/a&gt; has been successfully initiated.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Select_payment_funding</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>choicePrivate</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>SuccessScreenText_HV</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Successful lodgement submission&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Homes Victoria Voucher Number &lt;strong&gt;{!var_HomeVictoriaVoucherNumber} &lt;/strong&gt;has been successfully submitted.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;The Transaction is &lt;a href=&quot;{!LinkedTransactionRecord}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;{!var_newTransactionNumber}&lt;/a&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Select_payment_funding</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>choiceHomesVictoria</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Successfully_found_case_number</name>
        <label>Successfully found case number</label>
        <locationX>182</locationX>
        <locationY>566</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Funding_Source</targetReference>
        </connector>
        <fields>
            <name>SubjectText</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Subject:&lt;/strong&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt; {!Get_case_records.Subject}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Category:&lt;/strong&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt; {!Get_case_records.Category__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;Click next to proceed or back to re-enter case number&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <stages>
        <description>Stage for entering the case details</description>
        <name>Case_Details</name>
        <isActive>true</isActive>
        <label>Case details</label>
        <stageOrder>1</stageOrder>
    </stages>
    <stages>
        <description>Stage for entering the funding source details</description>
        <name>Funding_Source_Stage</name>
        <isActive>true</isActive>
        <label>Funding source</label>
        <stageOrder>2</stageOrder>
    </stages>
    <stages>
        <description>Successful Submission Stage</description>
        <name>Successfulsubmission</name>
        <isActive>true</isActive>
        <label>Successful submission</label>
        <stageOrder>3</stageOrder>
    </stages>
    <start>
        <locationX>680</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_for_account_status</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Call_homes_victoria_subflow</name>
        <label>Call homes victoria subflow</label>
        <locationX>314</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Get_case_documents</targetReference>
        </connector>
        <flowName>Lodge_New_Homes_Victoria_Funded_Bond</flowName>
        <inputAssignments>
            <name>varCaseID</name>
            <value>
                <elementReference>Get_case_records.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varRentalProvider</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>var_NewTransactionRecordId</assignToReference>
            <name>returnedTransactionId</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>var_newTransactionNumber</assignToReference>
            <name>returnedTransactionNumber</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>var_HomeVictoriaVoucherNumber</assignToReference>
            <name>var_HomesVictoriaVoucherNumber</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <description>Call privately funded subflow</description>
        <name>Call_privately_funded_subflow</name>
        <label>Call privately funded subflow</label>
        <locationX>50</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Get_case_documents</targetReference>
        </connector>
        <flowName>Lodge_New_Privately_Funded_Bond</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>varCaseID</name>
            <value>
                <elementReference>Get_case_records.Id</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>var_NewTransactionRecordId</assignToReference>
            <name>varTransactionId</name>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>var_newTransactionNumber</assignToReference>
            <name>varTransactionNumber</name>
        </outputAssignments>
    </subflows>
    <variables>
        <description>Input record</description>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <description>The Homes Victoria voucher number</description>
        <name>var_HomeVictoriaVoucherNumber</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>The new transaction number</description>
        <name>var_newTransactionNumber</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>var_NewTransactionRecordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
