<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Log_Error_To_Bond</name>
        <label>Log Error To Bond</label>
        <locationX>512</locationX>
        <locationY>2462</locationY>
        <actionName>FlowRecordLogEntry</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Error_Display_Screen</targetReference>
        </connector>
        <dataTypeMappings>
            <typeName>T__record</typeName>
            <typeValue>Bond__c</typeValue>
        </dataTypeMappings>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>flowName</name>
            <value>
                <stringValue>Bond_Correction_Repayment</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>message</name>
            <value>
                <elementReference>$Flow.FaultMessage</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>record</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>saveLog</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>FlowRecordLogEntry</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Corrective_Repayment_To_Collection</name>
        <label>Assign Corrective Repayment To Collection</label>
        <locationX>512</locationX>
        <locationY>2138</locationY>
        <assignmentItems>
            <assignToReference>repaymentsToCreate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>baseCorrectionRepayment</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Combined_Repayments_Amount_Are_Less_Than_Or_Equal_To_Incorrect_Repayment_A</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Fee_Repayment_To_Collection</name>
        <label>Assign Fee Repayment To Collection</label>
        <locationX>974</locationX>
        <locationY>1730</locationY>
        <assignmentItems>
            <assignToReference>repaymentsToCreate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>repaymentFeeRecord</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Confirm_Repayment_Details_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Create_Base_Correction_Repayment</name>
        <label>Create Base Correction Repayment</label>
        <locationX>512</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Bond__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Repayment_Record.Bond__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Parent_Repayment__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Repayment_Record.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Payee__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Repayment_Record.Payee__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Payee_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Repayment_Record.Payee_Type__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Repayment_Method__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Direct Credit</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Repayment_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Correction Repayment</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Repayment_Date__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>todaysDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Payer__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>RTBA</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Fee_Waived</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Create_Repayment_Fee</name>
        <label>Create Repayment Fee</label>
        <locationX>974</locationX>
        <locationY>1106</locationY>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>baseCorrectionRepayment</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Repayment_Fee</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Correction_Repayment</name>
        <label>Update Correction Repayment</label>
        <locationX>50</locationX>
        <locationY>998</locationY>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Incorrect_Repayment_Fee_Waived__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Confirm_Repayment_Details_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Fee_Repayment_With_Domestic_BT</name>
        <label>Update Fee Repayment With Domestic BT</label>
        <locationX>314</locationX>
        <locationY>1430</locationY>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Repayment_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Bank Trace Fee</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>repaymentFeeDataTable.firstSelectedRow.Fee_Amount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Fee_Repayment_To_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Fee_Repayment_With_International</name>
        <label>Update Fee Repayment With International</label>
        <locationX>578</locationX>
        <locationY>1430</locationY>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Repayment_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>International Fee</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>repaymentFeeDataTable.firstSelectedRow.Fee_Amount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Fee_Repayment_To_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Fee_Repayment_With_Other</name>
        <label>Update Fee Repayment With Other</label>
        <locationX>842</locationX>
        <locationY>1430</locationY>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Repayment_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Other Fee</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Other_Fee_Amount</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Fee_Repayment_To_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Fee_Repayment_With_Stop_Cheque</name>
        <label>Update Fee Repayment With Stop Cheque</label>
        <locationX>1106</locationX>
        <locationY>1430</locationY>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Repayment_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Other Fee</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>repaymentFeeDataTable.firstSelectedRow.Fee_Amount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Fee_Repayment_To_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Fee_Repayment_With_Westpac_BT</name>
        <label>Update Fee Repayment With Westpac BT</label>
        <locationX>1370</locationX>
        <locationY>1430</locationY>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Repayment_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Bank Trace Fee</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>repaymentFeeDataTable.firstSelectedRow.Fee_Amount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Fee_Repayment_To_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Repayment_Fee</name>
        <label>Update Repayment Fee</label>
        <locationX>974</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Payee__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_RBIIA_Account_Bank_Details.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Payee_Type__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>RBIIA</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Bank_Account_Name__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_RBIIA_Account_Bank_Details.Direct_Credit_Bank_Account_Name__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.BSB__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_RBIIA_Account_Bank_Details.Direct_Credit_BSB__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>repaymentFeeRecord.Account_Number__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_RBIIA_Account_Bank_Details.Direct_Credit_Bank_Account_Number__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Set_Repayment_Type_and_Repayment_Amount</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Repayments</name>
        <label>Update Repayments</label>
        <locationX>512</locationX>
        <locationY>2030</locationY>
        <assignmentItems>
            <assignToReference>baseCorrectionRepayment.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Correction_Repayment_Amount</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Corrective_Repayment_To_Collection</targetReference>
        </connector>
    </assignments>
    <constants>
        <description>Variable to store marketing cloud journey name.</description>
        <name>MCJourneyName</name>
        <dataType>String</dataType>
        <value>
            <stringValue>InternalUser-CorrectionRepayment-14565</stringValue>
        </value>
    </constants>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Check_Combined_Repayments_Amount_Are_Less_Than_Or_Equal_To_Incorrect_Repayment_A</name>
        <label>Check Combined Repayments Amount Are Less Than Or Equal To Incorrect Repayment Amount</label>
        <locationX>512</locationX>
        <locationY>2246</locationY>
        <defaultConnector>
            <targetReference>Repayments_Amount_Too_High_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Proceed_Less_Than_Or_Equal</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>combinedRepaymentsAmounts</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>Get_Repayment_Record.Amount__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Repayments</targetReference>
            </connector>
            <label>Proceed - Less Than Or Equal</label>
        </rules>
    </decisions>
    <decisions>
        <name>Do_Child_Incorrect_Repayment_Exist</name>
        <label>Do Child Incorrect Repayment Exist?</label>
        <locationX>1205</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>No_Child_Repayments_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Incorrect_Repayments_Exist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Child_Incorrect_Repayments</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Fee_Data</targetReference>
            </connector>
            <label>Yes - Incorrect Repayments Exist</label>
        </rules>
    </decisions>
    <decisions>
        <description>If we don&apos;t need to charge a Fee then we only need to create a single Repayment. If we do charge a Fee then we create two Repayments.</description>
        <name>Fee_Waived</name>
        <label>Fee Waived?</label>
        <locationX>512</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>Get_RBIIA_Account_Bank_Details</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>True_Fee_Waived</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Waive_Fee</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Correction_Repayment</targetReference>
            </connector>
            <label>True - Fee Waived</label>
        </rules>
    </decisions>
    <decisions>
        <name>Set_Repayment_Type_and_Repayment_Amount</name>
        <label>Set Repayment Type and Repayment Amount</label>
        <locationX>974</locationX>
        <locationY>1322</locationY>
        <defaultConnector>
            <targetReference>Unsupported_Fee_Type_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Fee_Type_Domestic_Bank_Trace_Fee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>repaymentFeeDataTable.firstSelectedRow.Fee_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Domestic Bank Trace Fee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Fee_Repayment_With_Domestic_BT</targetReference>
            </connector>
            <label>Fee Type - Domestic Bank Trace Fee</label>
        </rules>
        <rules>
            <name>Fee_Type_International_Transfer_Fee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>repaymentFeeDataTable.firstSelectedRow.Fee_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>International Transfer Fee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Fee_Repayment_With_International</targetReference>
            </connector>
            <label>Fee Type - International Transfer Fee</label>
        </rules>
        <rules>
            <name>Fee_Type_Other_Fee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>repaymentFeeDataTable.firstSelectedRow.Fee_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Other Fee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Fee_Repayment_With_Other</targetReference>
            </connector>
            <label>Fee Type - Other Fee</label>
        </rules>
        <rules>
            <name>Fee_Type_Stop_Cheque_Authority_Fee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>repaymentFeeDataTable.firstSelectedRow.Fee_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Stop Cheque Authority Fee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Fee_Repayment_With_Stop_Cheque</targetReference>
            </connector>
            <label>Fee Type - Stop Cheque Authority Fee</label>
        </rules>
        <rules>
            <name>Fee_Type_Westpac_Bank_Trace_Fee</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>repaymentFeeDataTable.firstSelectedRow.Fee_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Westpac Bank Trace Fee</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Fee_Repayment_With_Westpac_BT</targetReference>
            </connector>
            <label>Fee Type - Westpac Bank Trace Fee</label>
        </rules>
    </decisions>
    <description>Screen Flow that guides a User through making a special Bond Repayment outside of a Claim. Correction Repayments include:
- Underpayments
- Reprocessing after a Bank Trace
- Stop Cheque</description>
    <environments>Default</environments>
    <formulas>
        <name>combinedRepaymentsAmounts</name>
        <dataType>Currency</dataType>
        <expression>IF(ISNULL({!repaymentFeeRecord.Amount__c}),0,{!repaymentFeeRecord.Amount__c}) + {!baseCorrectionRepayment.Amount__c}</expression>
        <scale>2</scale>
    </formulas>
    <formulas>
        <name>todaysDate</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <interviewLabel>Bond Correction Repayment {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Bond Correction Repayment</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Repayments</name>
        <label>Create Repayments</label>
        <locationX>248</locationX>
        <locationY>2354</locationY>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Log_Error_To_Bond</targetReference>
        </faultConnector>
        <inputReference>repaymentsToCreate</inputReference>
    </recordCreates>
    <recordLookups>
        <description>Gets &quot;Incorrect&quot; Repayments that are related to Bond this screen flow is embedded on.</description>
        <name>Get_Child_Incorrect_Repayments</name>
        <label>Get Child Incorrect Repayments</label>
        <locationX>1205</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Do_Child_Incorrect_Repayment_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Bond__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Incorrect_Repayment__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Repayment__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Gets &quot;Repayment Fee&quot; Metadata. Only records with status Active.</description>
        <name>Get_Fee_Data</name>
        <label>Get Fee Data</label>
        <locationX>512</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Correction_Repayment_Start_Screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Active</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Repayment_Fees__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_RBIIA_Account_Bank_Details</name>
        <label>Get RBIIA Account Bank Details</label>
        <locationX>974</locationX>
        <locationY>998</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Repayment_Fee</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>RBIIA</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Direct_Credit_Bank_Account_Name__c</queriedFields>
        <queriedFields>Direct_Credit_BSB__c</queriedFields>
        <queriedFields>Direct_Credit_Bank_Account_Number__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>After a Repayment is chosen in the Screen via a Data Table, we query the Repayment. This is necessary to avoid weird unexplained bugs.</description>
        <name>Get_Repayment_Record</name>
        <label>Get Repayment Record</label>
        <locationX>512</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Confirm_Selection_Of_Repayment_Fees</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>childIncorrectRepayments.firstSelectedRow.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Repayment__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>Confirm_Repayment_Details_Screen</name>
        <label>Confirm Repayment Details Screen</label>
        <locationX>512</locationX>
        <locationY>1922</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Update_Repayments</targetReference>
        </connector>
        <fields>
            <name>IncorrectRepaymentDetails</name>
            <fieldText>&lt;p&gt;You are about to create a Correction Repayment for the following Incorrect Repayment&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Incorrect_Repayment</name>
            <fieldText>Incorrect Repayment</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Incorrect_Repayment_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>IncorrectRepaymentDisplayTextLeft</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Bond: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!Get_Repayment_Record.Bond__r.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Incorrect Repayment Reason: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!Get_Repayment_Record.Incorrect_Repayment_Reason__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Status: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!Get_Repayment_Record.Status__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0);&quot;&gt;Status Date/Time: &lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0);&quot;&gt;{!Get_Repayment_Record.Repayment_Status_Date_Time__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Repayment Type: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!Get_Repayment_Record.Repayment_Type__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Repayment Method: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!Get_Repayment_Record.Repayment_Method__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Incorrect_Repayment_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>IncorrectRepaymentDisplayTextRight</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Payee Type: &lt;/strong&gt;{!Get_Repayment_Record.Payee_Type__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Payee: &lt;/strong&gt;{!Get_Repayment_Record.Payee__r.Account_Name_Formula__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Bank Account Name: &lt;/strong&gt;{!Get_Repayment_Record.Bank_Account_Name__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Account Number: &lt;/strong&gt;{!Get_Repayment_Record.Account_Number__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;BSB: &lt;/strong&gt;{!Get_Repayment_Record.BSB__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Repayment Amount: &lt;/strong&gt;{!Get_Repayment_Record.Amount__c}﻿&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Correction_Repayment</name>
            <fieldText>Correction Repayment</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Correction_Repayment_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>CorrectionRepaymentDisplayText</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Bond: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!baseCorrectionRepayment.Bond__r.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Parent Repayment: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!baseCorrectionRepayment.Parent_Repayment__r.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Status: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!baseCorrectionRepayment.Status__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Repayment Type:&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; {!baseCorrectionRepayment.Repayment_Type__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Repayment Method:&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; {!baseCorrectionRepayment.Repayment_Method__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Payee Type:&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; {!baseCorrectionRepayment.Payee_Type__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Payee:&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!baseCorrectionRepayment.Payee__r.Account_Name_Formula__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;Incorrect Repayment Fee Waived:&lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt; {!baseCorrectionRepayment.Incorrect_Repayment_Fee_Waived__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Correction_Repayment_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>baseCorrectionRepayment.Bank_Account_Name__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>baseCorrectionRepayment.BSB__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>baseCorrectionRepayment.Account_Number__c</objectFieldReference>
                </fields>
                <fields>
                    <name>Correction_Repayment_Amount</name>
                    <dataType>Currency</dataType>
                    <defaultValue>
                        <numberValue>0.0</numberValue>
                    </defaultValue>
                    <fieldText>Correction Repayment Amount</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <scale>2</scale>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <fields>
            <name>Repayment_Fee</name>
            <fieldText>Repayment Fee</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Repayment_Fee_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>RepaymentFeeDetailsText</name>
                    <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Bond: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!repaymentFeeRecord.Bond__r.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Parent Repayment: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!repaymentFeeRecord.Parent_Repayment__r.Name}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Status: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!repaymentFeeRecord.Status__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Repayment Type: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!repaymentFeeRecord.Repayment_Type__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Repayment Method: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!repaymentFeeRecord.Repayment_Method__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Payee Type: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!repaymentFeeRecord.Payee_Type__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 0, 0);&quot;&gt;Payee: &lt;/strong&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!repaymentFeeRecord.Payee__r.Account_Name_Formula__c}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Repayment_Fee_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>FeeRepaymentDisplaySpecificsText</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Bank Name:&lt;/strong&gt; {!repaymentFeeRecord.Bank_Account_Name__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;BSB:&lt;/strong&gt; {!repaymentFeeRecord.BSB__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Account Number:&lt;/strong&gt; {!repaymentFeeRecord.Account_Number__c}&lt;/p&gt;&lt;p&gt;&lt;strong&gt;Repayment Amount: &lt;/strong&gt;{!repaymentFeeRecord.Amount__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Waive_Fee</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Confirm_Selection_Of_Repayment_Fees</name>
        <label>Confirm Selection Of Repayment + Fees</label>
        <locationX>512</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Create_Base_Correction_Repayment</targetReference>
        </connector>
        <fields>
            <name>SelectedRepaymentDisplayText</name>
            <fieldText>&lt;p&gt;You have selected to make a &quot;Correction Repayment&quot; for the following &quot;Incorrect Repayment&quot;.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Would you like to charge a Fee for this Correction Repayment?&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Waive_Fee</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <booleanValue>false</booleanValue>
            </defaultValue>
            <fieldText>Waive Fee?</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>repaymentFeeDataTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Repayment_Fees__mdt</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Repayment Fees</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Fee_Data</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Fee_Name__c&quot;,&quot;guid&quot;:&quot;column-c78f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Fee Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Fee_Amount__c&quot;,&quot;guid&quot;:&quot;column-6194&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Fee Amount&quot;,&quot;type&quot;:&quot;number&quot;},{&quot;apiName&quot;:&quot;Bank_Trace_Fee__c&quot;,&quot;guid&quot;:&quot;column-f6b8&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Bank Trace Fee&quot;,&quot;type&quot;:&quot;boolean&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>Waive_Fee</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Other_Fee_Amount</name>
            <dataType>Currency</dataType>
            <defaultValue>
                <numberValue>1.0</numberValue>
            </defaultValue>
            <fieldText>Other Fee Amount</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
            <scale>2</scale>
            <validationRule>
                <errorMessage>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Fee Amount must be greater than zero.&lt;/span&gt;&lt;/p&gt;</errorMessage>
                <formulaExpression>{!Other_Fee_Amount} &gt; 0</formulaExpression>
            </validationRule>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>repaymentFeeDataTable.firstSelectedRow.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Other_Fee</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Correction_Repayment_Start_Screen</name>
        <label>Correction Repayment - Start Screen</label>
        <locationX>512</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Get_Repayment_Record</targetReference>
        </connector>
        <fields>
            <name>IntroductionScreenDisplayText</name>
            <fieldText>&lt;p&gt;Welcome to the &quot;Correction Repayment&quot; Screen Flow.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;This screen will walk through creating a &quot;special&quot; (outside a claim) repayment of a bond.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;It is essential that a Repayment related to this bond is marked as an &quot;Incorrect Repayment&quot;. Alternatively use the Amend Bond functionality.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please note that Correction Repayments ONLY support domestic bank transfers.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please beware that for certain types of Correction Repayments you need to ensure prior to making a Correction Repayment, that monies have been returned back to the Bond. (eg Bank Trace). This flow will not stop you from overdrawing on the Bond.&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Please choose the Incorrect Repayment from below.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>childIncorrectRepayments</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Repayment__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Incorrect Repayments On This Bond</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Child_Incorrect_Repayments</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-ff05&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Repayment Number&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Status__c&quot;,&quot;guid&quot;:&quot;column-8b32&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Status&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Amount__c&quot;,&quot;guid&quot;:&quot;column-9e19&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Repayment Amount&quot;,&quot;type&quot;:&quot;currency&quot;},{&quot;apiName&quot;:&quot;Repayment_Method__c&quot;,&quot;guid&quot;:&quot;column-32c6&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:3,&quot;label&quot;:&quot;Repayment Method&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Incorrect_Repayment__c&quot;,&quot;guid&quot;:&quot;column-ed26&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:4,&quot;label&quot;:&quot;Incorrect Repayment&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;apiName&quot;:&quot;Incorrect_Repayment_Reason__c&quot;,&quot;guid&quot;:&quot;column-c3b7&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:5,&quot;label&quot;:&quot;Incorrect Repayment Reason&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Comments__c&quot;,&quot;guid&quot;:&quot;column-2490&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:6,&quot;label&quot;:&quot;Comments&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Error_Display_Screen</name>
        <label>Error Display</label>
        <locationX>512</locationX>
        <locationY>2570</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>ErrorDisplayMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 20px; color: rgb(255, 0, 0);&quot;&gt;An error occured. Please contact your Salesforce Administrator.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 20px; color: rgb(255, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>No_Child_Repayments_Screen</name>
        <label>No Child Repayments Screen</label>
        <locationX>1898</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>noChildRepaymentsTextDisplay</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;There are no child Incorrect Repayments for this Bond. Cannot issue a Correction Repayment.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Repayments_Amount_Too_High_Screen</name>
        <label>Repayments Amount Too High Screen</label>
        <locationX>776</locationX>
        <locationY>2354</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Confirm_Repayment_Details_Screen</targetReference>
        </connector>
        <fields>
            <name>RepaymentsTooHighText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 18px; color: rgb(255, 0, 0);&quot;&gt;The combined amount of Repayments for both the Corrective and Fee Repayments are higher than the Incorrect Repayments. Please adjust to proceed.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Success_Screen</name>
        <label>Success Screen</label>
        <locationX>248</locationX>
        <locationY>2462</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>SuccessMessageText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(39, 144, 71); font-size: 18px;&quot;&gt;Congrats! Repayments were created successfully!&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Unsupported_Fee_Type_Screen</name>
        <label>Unsupported Fee Type Screen</label>
        <locationX>1634</locationX>
        <locationY>1430</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>UnsupportFeeTypeErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Oops! The Fee Type you choose is unsupported in this screen flow.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>1079</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Child_Incorrect_Repayments</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>baseCorrectionRepayment</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Repayment__c</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond__c</objectType>
    </variables>
    <variables>
        <name>repaymentFeeRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Repayment__c</objectType>
    </variables>
    <variables>
        <name>repaymentsToCreate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Repayment__c</objectType>
    </variables>
</Flow>
