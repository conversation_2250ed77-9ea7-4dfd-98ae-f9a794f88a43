<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Email</name>
        <label>Send Email</label>
        <locationX>930</locationX>
        <locationY>5210</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>Get_case_Details_related_to_Transaction.Initiator_Email__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>Get_Email_Address.Address</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>Subject_RT</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>Email_Body_RT</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>useLineBreaks</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Action to send the email to the management notices email address of the RP.</description>
        <name>Send_Email_BL</name>
        <label>Send Email</label>
        <locationX>5418</locationX>
        <locationY>5102</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>Get_Account_Details_BL.Management_notices__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>Get_Sender_Email_Address_BL.Address</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>Subject_BL</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>Email_Body_BL</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>useLineBreaks</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Bond Amendment Rejected MC API Journey</description>
        <name>Trigger_BA_Rejected_Notification</name>
        <label>Trigger BA Rejected Notification</label>
        <locationX>4802</locationX>
        <locationY>4478</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_BA_Rejected</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger RP Initial Claim Rejected MC API Journey</description>
        <name>Trigger_BC_Rejected_Notification</name>
        <label>Trigger BC Rejected Notification</label>
        <locationX>6210</locationX>
        <locationY>4478</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_Claim_Rejected</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger BM Rejected MC API Journey</description>
        <name>Trigger_BM_Rejected_Notification</name>
        <label>Trigger BM Rejected Notification</label>
        <locationX>2514</locationX>
        <locationY>4478</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_BM_Rejected</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRPType</name>
            <value>
                <stringValue>Transferee</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger Renter Initial Claim Rejected MC API Journey</description>
        <name>Trigger_RIC_Rejected_Notification</name>
        <label>Trigger RIC Rejected Notification</label>
        <locationX>5682</locationX>
        <locationY>4478</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_Claim_Rejected</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetClaimInitiator</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger RPT Bond In Rejected MC API Journey</description>
        <name>Trigger_RPT_Bond_In_Rejected_Notification</name>
        <label>Trigger RPT Bond In Rejected Notification</label>
        <locationX>3042</locationX>
        <locationY>4694</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Rejected</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRPType</name>
            <value>
                <stringValue>Transferee</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger RPT Bond In Rejected MC API Journey Mail</description>
        <name>Trigger_RPT_Bond_In_Rejected_Notification_Mail</name>
        <label>Trigger RPT Bond In Rejected Notification Mail</label>
        <locationX>4098</locationX>
        <locationY>4586</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Rejected_Mail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>Mail</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRPType</name>
            <value>
                <stringValue>Transferor</stringValue>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger RPT Bond Out Rejected MC API Journey</description>
        <name>Trigger_RPT_Bond_Out_Rejected_Notification</name>
        <label>Trigger RPT Bond Out Rejected Notification</label>
        <locationX>3570</locationX>
        <locationY>4694</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RPT_Rejected</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>originCheck</name>
            <value>
                <stringValue>RTBA Website</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRPType</name>
            <value>
                <stringValue>Transferor</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Trigger RT Rejected MC API Journey</description>
        <name>Trigger_RT_Rejected_Notification</name>
        <label>Trigger RT Rejected Notification</label>
        <locationX>314</locationX>
        <locationY>4478</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Name_RT_Rejected</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>statusCheck</name>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Add the individual Transaction Rejection record to the collection which will be used to create the records at the end of the flow.</description>
        <name>Add_Transaction_Rejection_to_Collection</name>
        <label>Add Transaction Rejection to Collection</label>
        <locationX>1788</locationX>
        <locationY>3074</locationY>
        <assignmentItems>
            <assignToReference>Transaction_Rejection_Record</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Individual_Transaction_Rejection_Record</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Another_Rejection_Code_Choice_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign values to the individual transaction rejection record variable which will be added later to the collection variable.</description>
        <name>Assign_Individual_Transaction_Rejection_Record</name>
        <label>Assign Individual Transaction Rejection Record</label>
        <locationX>1788</locationX>
        <locationY>2966</locationY>
        <assignmentItems>
            <assignToReference>Individual_Transaction_Rejection_Record.Reason_Text__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ReasonTextFinal</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Individual_Transaction_Rejection_Record.Rejection_Code__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Rejection_Code</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Individual_Transaction_Rejection_Record.Remedy_Text__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Remedy</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Individual_Transaction_Rejection_Record.Transaction__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Transaction_Rejection_to_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Reason and Remedy read only flag to &quot;False&quot;. So they are editable.</description>
        <name>Assign_ReasonRemedy_ReadOnly_False</name>
        <label>Assign_ReasonRemedy_ReadOnly_False</label>
        <locationX>1656</locationX>
        <locationY>2666</locationY>
        <assignmentItems>
            <assignToReference>ReasonRemedyReadOnly</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Reason_and_Remedy_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the Reason and Remedy read only flag to &quot;True&quot;. So they are not editable.</description>
        <name>Assign_ReasonRemedy_ReadOnly_True</name>
        <label>Assign_ReasonRemedy_ReadOnly_True</label>
        <locationX>1920</locationX>
        <locationY>2666</locationY>
        <assignmentItems>
            <assignToReference>ReasonRemedyReadOnly</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Reason_and_Remedy_Screen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Rejection_Codes</name>
        <label>Assign Rejection Codes</label>
        <locationX>1018</locationX>
        <locationY>4910</locationY>
        <assignmentItems>
            <assignToReference>var_RejectionCodeText</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>var_RejectionCodes_RT</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_Rejection_Reasons</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assign the rejection code details to the text to be displayted.</description>
        <name>Assign_Rejection_Codes_BL</name>
        <label>Assign Rejection Codes</label>
        <locationX>5506</locationX>
        <locationY>4802</locationY>
        <assignmentItems>
            <assignToReference>var_RejectionCodeText</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>var_RejectionCodes_BL</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_Rejection_Reasons_BL</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Assigns the current selected party to the list of parties</description>
        <name>Assign_the_Current_Party_to_the_list</name>
        <label>Assign the Current Party to the list</label>
        <locationX>1658</locationX>
        <locationY>2198</locationY>
        <assignmentItems>
            <assignToReference>SelectedPartiesList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Selected_Parties.Party_Name__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Selected_Parties</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets the flag &quot;Display TP Selection&quot; to FALSE so as the transaction Party selection can be displayed.</description>
        <name>DisplayTPSelectionAssignment_No</name>
        <label>DisplayTPSelectionAssignment_No</label>
        <locationX>1986</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>DisplayTPSelection</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_Reason_and_Remedy_Editable</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets the flag &quot;Display TP Selection&quot; to yes so as the transaction Party selection can be displayed.</description>
        <name>DisplayTPSelectionAssignment_Yes</name>
        <label>DisplayTPSelectionAssignment_Yes</label>
        <locationX>1590</locationX>
        <locationY>1166</locationY>
        <assignmentItems>
            <assignToReference>DisplayTPSelection</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Are_TP_s_Optional</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Reset the Individual Transaction record variable in preparation for the selection of the next rejection code.
Reset the Selected Transaction Party list to empty.</description>
        <name>Reset_Individual_Transaction_Rejection_Record</name>
        <label>Reset Individual Transaction Rejection Record</label>
        <locationX>50</locationX>
        <locationY>3398</locationY>
        <assignmentItems>
            <assignToReference>Individual_Transaction_Rejection_Record</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SelectedPartiesList</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Choose_Rejection_Code</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>This adds the &quot;, &quot; delimiter between the values in the string.</description>
        <name>Selected_Parties_List_Add_Comma_Delimeter</name>
        <label>Selected Parties List  - Add Comma Delimeter</label>
        <locationX>1746</locationX>
        <locationY>2006</locationY>
        <assignmentItems>
            <assignToReference>SelectedPartiesList</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>, </stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_the_Current_Party_to_the_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set the TPSelectionRequired flag to False</description>
        <name>TPSelectionAssignment_Optional</name>
        <label>TPSelectionAssignment_Optional</label>
        <locationX>1722</locationX>
        <locationY>1382</locationY>
        <assignmentItems>
            <assignToReference>MinTPSelection</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Transaction_Party_Selection_Table</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Set the minimum number of TP&apos;s to be selected to 1</description>
        <name>TPSelectionAssignment_Required</name>
        <label>TPSelectionAssignment_Required</label>
        <locationX>1458</locationX>
        <locationY>1382</locationY>
        <assignmentItems>
            <assignToReference>MinTPSelection</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Transaction_Party_Selection_Table</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>No</name>
        <choiceText>No</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>No</stringValue>
        </value>
    </choices>
    <choices>
        <name>Yes</name>
        <choiceText>Yes</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Yes</stringValue>
        </value>
    </choices>
    <constants>
        <description>Journey Name - Bond-Amendment-Rejected-15635</description>
        <name>MC_Journey_Name_BA_Rejected</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Bond-Amendment-Rejected-15635</stringValue>
        </value>
    </constants>
    <constants>
        <description>Journey Name - Bond-Merge-Transaction-Rejected-12858</description>
        <name>MC_Journey_Name_BM_Rejected</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Bond-Merge-Transaction-Rejected-12858</stringValue>
        </value>
    </constants>
    <constants>
        <description>SF - Reject Claim Transaction Notifications MC Journey Name</description>
        <name>MC_Journey_Name_Claim_Rejected</name>
        <dataType>String</dataType>
        <value>
            <stringValue>Bond-Claim-Transaction-Rejected-12536</stringValue>
        </value>
    </constants>
    <constants>
        <description>Journey Name - RPTransfer-Transaction-Rejected-14788</description>
        <name>MC_Journey_Name_RPT_Rejected</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RPTransfer-Transaction-Rejected-14788</stringValue>
        </value>
    </constants>
    <constants>
        <description>Journey Name - PaperForm-RPTransfer-Reject-Transaction-Notification to Case Initiator-14597</description>
        <name>MC_Journey_Name_RPT_Rejected_Mail</name>
        <dataType>String</dataType>
        <value>
            <stringValue>PaperForm-RPTransfer-Reject-Transaction-Notification to Case Initiator-14597</stringValue>
        </value>
    </constants>
    <constants>
        <description>Journey Name - RTransfer-Transaction-Rejected-12784</description>
        <name>MC_Journey_Name_RT_Rejected</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RTransfer-Transaction-Rejected-12784</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Check what the user indicated, and if they indicated to add another rejection code, then move to reset the individual transaction and the Selected Transactopn parties list.</description>
        <name>Add_Another_Rejection_Code</name>
        <label>Add Another Rejection Code</label>
        <locationX>1788</locationX>
        <locationY>3290</locationY>
        <defaultConnector>
            <targetReference>Comments_and_Final_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>AddRejectionCode_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Do_you_wish_to_add_another_rejection_code_b</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Yes</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Reset_Individual_Transaction_Rejection_Record</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check to see if any parties are selected. If so, loop through them to</description>
        <name>Are_any_parties_selected</name>
        <label>Are any parties selected</label>
        <locationX>1590</locationX>
        <locationY>1682</locationY>
        <defaultConnector>
            <targetReference>Are_Reason_and_Remedy_Editable</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Parties Selected</defaultConnectorLabel>
        <rules>
            <name>Parties_are_selected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Select_Transaction_Party.selectedRows</leftValueReference>
                <operator>NotEqualTo</operator>
            </conditions>
            <connector>
                <targetReference>Loop_Selected_Parties</targetReference>
            </connector>
            <label>Parties are selected</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if comments have been provided. If they are not provided we don&apos;t want to overwrite the existing.</description>
        <name>Are_Comments_Provided</name>
        <label>Are Comments Provided</label>
        <locationX>3526</locationX>
        <locationY>3614</locationY>
        <defaultConnector>
            <targetReference>Transaction_Record_Without_Comments</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Comments Not Provided</defaultConnectorLabel>
        <rules>
            <name>Comments_are_Provided</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Comments</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Transaction_Record_With_Comments</targetReference>
            </connector>
            <label>Comments are Provided</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if the Reason and Remedy are editable on use.</description>
        <name>Are_Reason_and_Remedy_Editable</name>
        <label>Are Reason and Remedy Editable?</label>
        <locationX>1788</locationX>
        <locationY>2558</locationY>
        <defaultConnector>
            <targetReference>Assign_ReasonRemedy_ReadOnly_True</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>Reason_Remedy_Editable_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Chosen_RC_EditableOnUse</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ReasonRemedy_ReadOnly_False</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Determine if the TP Selection is required or optional.  If they are required, then we set the minimal selected rows flag to 1. This will be used later when displaying the data table.</description>
        <name>Are_TP_s_Optional</name>
        <label>Are TP&apos;s Optional</label>
        <locationX>1590</locationX>
        <locationY>1274</locationY>
        <defaultConnector>
            <targetReference>TPSelectionAssignment_Optional</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Optional</defaultConnectorLabel>
        <rules>
            <name>Required</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Chosen_RC_ImpactedPartyRequired</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>TPSelectionAssignment_Required</targetReference>
            </connector>
            <label>Required</label>
        </rules>
    </decisions>
    <decisions>
        <description>Checks if the running user has permissions to run the flow. This is a safety check in the event the flow is run manually and not from the action on the transaction record page.</description>
        <name>Check_Permissions</name>
        <label>Check Permissions</label>
        <locationX>4747</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Permission_Denied</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Permission Denied</defaultConnectorLabel>
        <rules>
            <name>Permission_Granted</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Permission.Reject_Transaction</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Confirmation_Screen</targetReference>
            </connector>
            <label>Permission Granted</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check to see if the selected parties list is empty. If so then it will add the first party. If there is more than one selected, then on further iterations it will not be Null, and a Comma will be added before adding the next value.</description>
        <name>Chekc_if_Party_List_is_NULL</name>
        <label>Chekc if Party List is NULL</label>
        <locationX>1658</locationX>
        <locationY>1898</locationY>
        <defaultConnector>
            <targetReference>Selected_Parties_List_Add_Comma_Delimeter</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Null</defaultConnectorLabel>
        <rules>
            <name>Null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>SelectedPartiesList</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_the_Current_Party_to_the_list</targetReference>
            </connector>
            <label>Null</label>
        </rules>
    </decisions>
    <decisions>
        <description>Trigger RPT Email based on category</description>
        <name>Determine_RPT_Category</name>
        <label>Determine RPT Category</label>
        <locationX>3306</locationX>
        <locationY>4586</locationY>
        <defaultConnector>
            <targetReference>Trigger_RPT_Bond_Out_Rejected_Notification</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>RPT Bond Out</defaultConnectorLabel>
        <rules>
            <name>RPT_Bond_In</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Transfer_Option__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Transfer in - one bond</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_RPT_Bond_In_Rejected_Notification</targetReference>
            </connector>
            <label>RPT Bond In</label>
        </rules>
    </decisions>
    <decisions>
        <description>Decision on whether to display the transaction party selection to the user. This is based on if the Impacted Party is set to &quot;Yes&quot; or &quot;Optional&quot;. If it is set to &quot;No&quot; then don&apos;t display it.</description>
        <name>Display_Transaction_Party_Selection</name>
        <label>Display Transaction Party Selection</label>
        <locationX>1788</locationX>
        <locationY>1058</locationY>
        <defaultConnector>
            <targetReference>DisplayTPSelectionAssignment_No</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Do Not Display TP Selection</defaultConnectorLabel>
        <rules>
            <name>Display_TP_Selection_Option</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Chosen_RC_ImpactedPartyRequired</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Chosen_RC_ImpactedPartyRequired</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Optional</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>DisplayTPSelectionAssignment_Yes</targetReference>
            </connector>
            <label>Display TP Selection</label>
        </rules>
    </decisions>
    <decisions>
        <name>Does_it_exist</name>
        <label>Does it exist</label>
        <locationX>2008</locationX>
        <locationY>566</locationY>
        <defaultConnector>
            <targetReference>Loop_Record_Collection</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Does Not Exist</defaultConnectorLabel>
        <rules>
            <name>Exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Rejection_Code</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Record_Collection.Rejection_Code__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Error_Screen_Rejection_Code_Exists</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <description>Check if origin was RTBA Website or Mail</description>
        <name>Record_Origin</name>
        <label>Record Origin</label>
        <locationX>3966</locationX>
        <locationY>4478</locationY>
        <defaultConnector>
            <targetReference>Success_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RTBA_Website</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTBA Website</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Determine_RPT_Category</targetReference>
            </connector>
            <label>RTBA Website</label>
        </rules>
        <rules>
            <name>Mail</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_RPT_Bond_In_Rejected_Notification_Mail</targetReference>
            </connector>
            <label>Mail</label>
        </rules>
    </decisions>
    <decisions>
        <description>Trigger MC API journey notification based on transaction type</description>
        <name>Trigger_Notification_Based_on_Type</name>
        <label>Trigger Notification Based on Type</label>
        <locationX>3526</locationX>
        <locationY>4370</locationY>
        <defaultConnector>
            <targetReference>Success_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Renter_Transfer_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Transfer</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTBA Website</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_RT_Rejected_Notification</targetReference>
            </connector>
            <label>Renter Transfer Type</label>
        </rules>
        <rules>
            <name>Renter_Transfer_Mail</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Transfer</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Account_Details</targetReference>
            </connector>
            <label>Renter Transfer- Mail</label>
        </rules>
        <rules>
            <name>Bond_Merge_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bond Merge</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTBA Website</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_BM_Rejected_Notification</targetReference>
            </connector>
            <label>Bond Merge Type</label>
        </rules>
        <rules>
            <name>RP_Transfer_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider Transfer</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Record_Origin</targetReference>
            </connector>
            <label>RP Transfer Type</label>
        </rules>
        <rules>
            <name>Bond_Amendment_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bond Amendment</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTBA Website</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_BA_Rejected_Notification</targetReference>
            </connector>
            <label>Bond Amendment Type</label>
        </rules>
        <rules>
            <name>Bond_Lodgement_Paper</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Bond Lodgement</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mail</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Account_Details_BL</targetReference>
            </connector>
            <label>Bond Lodgement - Paper</label>
        </rules>
        <rules>
            <name>Renter_Claim_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTBA Website</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claims</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Claim_Sub_Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Claim with Order</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_RIC_Rejected_Notification</targetReference>
            </connector>
            <label>Renter Claim Type</label>
        </rules>
        <rules>
            <name>RP_Claim_Type</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Origin__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTBA Website</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claims</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Trigger_BC_Rejected_Notification</targetReference>
            </connector>
            <label>RP Claim Type</label>
        </rules>
    </decisions>
    <description>The flow will allow an Internal user to reject a transaction based on specific criteria</description>
    <dynamicChoiceSets>
        <description>The record choice set to be populated from the &quot;Rejection Code&quot; object for use in the flow.</description>
        <name>RejectionCodeChoiceSet</name>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction_Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Type__c</elementReference>
            </value>
        </filters>
        <object>Rejection_Code__c</object>
        <outputAssignments>
            <assignToReference>Chosen_RC_EditableOnUse</assignToReference>
            <field>Editable_On_Use__c</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>Chosen_RC_ImpactedPartyRequired</assignToReference>
            <field>Impacted_Party_Required__c</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>Chosen_Reason_Text</assignToReference>
            <field>Reason_Text__c</field>
        </outputAssignments>
        <outputAssignments>
            <assignToReference>Chosen_Remedy_Text</assignToReference>
            <field>Remedy_Text__c</field>
        </outputAssignments>
        <valueField>Name</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <description>This is the final Reason text which may include the selected names on the end of it (where approriate)</description>
        <name>ReasonTextFinal</name>
        <dataType>String</dataType>
        <expression>IF(OR({!Chosen_RC_ImpactedPartyRequired}=&apos;Yes&apos;, {!Chosen_RC_ImpactedPartyRequired}=&apos;Optional&apos;), {!Chosen_Reason_Text} +&apos;: &apos;+{!SelectedPartiesList}, {!Chosen_Reason_Text})</expression>
    </formulas>
    <formulas>
        <description>Rejections Codes Text for Bond Lodgement</description>
        <name>var_RejectionCodes_BL</name>
        <dataType>String</dataType>
        <expression>{!var_RejectionCodeText} &amp; 
IF(ISBLANK({!var_RejectionCodeText}), &quot;&quot;, BR() &amp; BR()) &amp; {!Loop_through_Rejection_Reasons_BL.Reason_Text__c} &amp; BR() &amp;  {!Loop_through_Rejection_Reasons_BL.Remedy_Text__c}</expression>
    </formulas>
    <formulas>
        <name>var_RejectionCodes_RT</name>
        <dataType>String</dataType>
        <expression>{!var_RejectionCodeText} &amp; 
IF(ISBLANK({!var_RejectionCodeText}), &quot;&quot;, BR() &amp; BR()) &amp;  {!Loop_through_Rejection_Reasons.Reason_Text__c} &amp; BR() &amp;  {!Loop_through_Rejection_Reasons.Remedy_Text__c}</expression>
    </formulas>
    <interviewLabel>Reject Transaction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Reject Transaction</label>
    <loops>
        <description>Checks to see if the chosen rejection code has already been chosen and added to the &quot;Transaction Rejection Record&quot; collection. If if does exist and error message will be shown and the loop will exit.</description>
        <name>Loop_Record_Collection</name>
        <label>Loop Record Collection</label>
        <locationX>1788</locationX>
        <locationY>458</locationY>
        <collectionReference>Transaction_Rejection_Record</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Does_it_exist</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Transaction_Parties</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loop through the selected Parties and build a concatenated, comma delimited string.</description>
        <name>Loop_Selected_Parties</name>
        <label>Loop Selected Parties</label>
        <locationX>1482</locationX>
        <locationY>1790</locationY>
        <collectionReference>Select_Transaction_Party.selectedRows</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Chekc_if_Party_List_is_NULL</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Are_Reason_and_Remedy_Editable</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_through_Rejection_Reasons</name>
        <label>Loop through Rejection Reasons</label>
        <locationX>930</locationX>
        <locationY>4802</locationY>
        <collectionReference>Transaction_Rejection_Record</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Rejection_Codes</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Email_Address</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <description>Loops through the selected rejection codes. Inside the loop the text for the reason and remedy text will be formulated for inclusion in the email.</description>
        <name>Loop_through_Rejection_Reasons_BL</name>
        <label>Loop through Rejection Reasons</label>
        <locationX>5418</locationX>
        <locationY>4694</locationY>
        <collectionReference>Transaction_Rejection_Record</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Rejection_Codes_BL</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Sender_Email_Address_BL</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <description>Create the Transaction Rejection Records from the collection.</description>
        <name>Create_Transaction_Rejection_Records</name>
        <label>Create Transaction Rejection Records</label>
        <locationX>3526</locationX>
        <locationY>3506</locationY>
        <connector>
            <targetReference>Are_Comments_Provided</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Copy_2_of_Rollback</targetReference>
        </faultConnector>
        <inputReference>Transaction_Rejection_Record</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Get_Account_Details</name>
        <label>Get Account Details</label>
        <locationX>930</locationX>
        <locationY>4478</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Contact_Details</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Rental_Provider__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Office_Email__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the account details to obtain the management notices email address</description>
        <name>Get_Account_Details_BL</name>
        <label>Get Account Details</label>
        <locationX>5418</locationX>
        <locationY>4478</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_case_Details_related_to_Transaction_BL</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Rental_Provider__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Management_notices__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_case_Details_related_to_Transaction</name>
        <label>Get case Details related to Transaction</label>
        <locationX>930</locationX>
        <locationY>4694</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_through_Rejection_Reasons</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the details of the case which is asspciated to the BL transaftion</description>
        <name>Get_case_Details_related_to_Transaction_BL</name>
        <label>Get case Details related to Transaction</label>
        <locationX>5418</locationX>
        <locationY>4586</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_through_Rejection_Reasons_BL</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction_Number__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Contact_Details</name>
        <label>Get Contact Details</label>
        <locationX>930</locationX>
        <locationY>4586</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_case_Details_related_to_Transaction</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Account_Details.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Email</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Email_Address</name>
        <label>Get Email Address</label>
        <locationX>930</locationX>
        <locationY>5102</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Email</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Comms-Rentalbonds (DGS)</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the senders email address</description>
        <name>Get_Sender_Email_Address_BL</name>
        <label>Get Sender Email Address</label>
        <locationX>5418</locationX>
        <locationY>4994</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Send_Email_BL</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Comms-Rentalbonds (DGS)</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Get the transaction parties for the transaction.</description>
        <name>Get_Transaction_Parties</name>
        <label>Get Transaction Parties</label>
        <locationX>1788</locationX>
        <locationY>950</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Display_Transaction_Party_Selection</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Unable_to_get_Transaction_Parties</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordRollbacks>
        <name>Copy_1_of_Rollback</name>
        <label>Copy 1 of Rollback</label>
        <locationX>4054</locationX>
        <locationY>3830</locationY>
        <connector>
            <targetReference>Unable_to_update_Transaction_Record_Without_Comments</targetReference>
        </connector>
    </recordRollbacks>
    <recordRollbacks>
        <name>Copy_2_of_Rollback</name>
        <label>Copy 2 of Rollback</label>
        <locationX>7178</locationX>
        <locationY>3614</locationY>
        <connector>
            <targetReference>Unable_to_create_Transaction_Rejection_Records</targetReference>
        </connector>
    </recordRollbacks>
    <recordRollbacks>
        <name>Copy_3_of_Rollback</name>
        <label>Copy 3 of Rollback</label>
        <locationX>6914</locationX>
        <locationY>4370</locationY>
        <connector>
            <targetReference>Unable_to_update_TP_Records</targetReference>
        </connector>
    </recordRollbacks>
    <recordRollbacks>
        <name>Rollback</name>
        <label>Rollback</label>
        <locationX>3526</locationX>
        <locationY>3830</locationY>
        <connector>
            <targetReference>Unable_to_update_Transaction_Record_with_Comments</targetReference>
        </connector>
    </recordRollbacks>
    <recordUpdates>
        <description>Update the transaction record to set the status &quot;RTBA Rejected&quot; but not the comments.</description>
        <name>Transaction_Record_Without_Comments</name>
        <label>Transaction Record (Without Comments)</label>
        <locationX>3790</locationX>
        <locationY>3722</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Records</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Copy_1_of_Rollback</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the transaction party records and set the status of each party to &apos;Cancelled&apos;. Also sets the Party Status Date to the current date and time.</description>
        <name>Update_Transaction_Party_Records</name>
        <label>Update Transaction Party Records</label>
        <locationX>3526</locationX>
        <locationY>4262</locationY>
        <connector>
            <targetReference>Trigger_Notification_Based_on_Type</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Copy_3_of_Rollback</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status_Date__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update the transaction record to set the status &quot;RTBA Rejected&quot; and add the comments.</description>
        <name>Update_Transaction_Record_With_Comments</name>
        <label>Update Transaction Record (With Comments)</label>
        <locationX>3262</locationX>
        <locationY>3722</locationY>
        <connector>
            <targetReference>Update_Transaction_Party_Records</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Rollback</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>Comments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>RTBA Rejected</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <runInMode>SystemModeWithSharing</runInMode>
    <screens>
        <description>Displays the option to add another rejection code.</description>
        <name>Add_Another_Rejection_Code_Choice_Screen</name>
        <label>Add Another Rejection Code Choice Screen</label>
        <locationX>1788</locationX>
        <locationY>3182</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Add_Another_Rejection_Code</targetReference>
        </connector>
        <fields>
            <name>Add_Another_Rejection_Code_Choice_Screen_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Add_Another_Rejection_Code_Choice_Screen_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Do_you_wish_to_add_another_rejection_code_b</name>
                    <choiceReferences>Yes</choiceReferences>
                    <choiceReferences>No</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Do you wish to add another rejection code?</fieldText>
                    <fieldType>RadioButtons</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This page provides the ability for the user to select a rejection code, which will auto populate the rejection reason and remedy.</description>
        <name>Choose_Rejection_Code</name>
        <label>Choose Rejection Code</label>
        <locationX>1788</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Loop_Record_Collection</targetReference>
        </connector>
        <fields>
            <name>Rejection_Code</name>
            <choiceReferences>RejectionCodeChoiceSet</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Rejection Code</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>The final screen of the flow where optional comments can be added.</description>
        <name>Comments_and_Final_Screen</name>
        <label>Comments and Final Screen</label>
        <locationX>3526</locationX>
        <locationY>3398</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Create_Transaction_Rejection_Records</targetReference>
        </connector>
        <fields>
            <name>Selected_Rejection_Code_Text_C</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Selected Rejection Code:&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; {!Rejection_Code}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Comments</name>
            <fieldText>Comments</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Seek confirmation for rejecting the transaction.</description>
        <name>Confirmation_Screen</name>
        <label>Confirmation Screen</label>
        <locationX>1788</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Choose_Rejection_Code</targetReference>
        </connector>
        <fields>
            <name>Confirm_Transaction_Rejection</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; color: rgb(18, 14, 14);&quot;&gt;﻿&lt;/span&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); font-size: 14px; font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; color: rgb(15, 2, 1);&quot;&gt;You are about to reject this transaction. Do you want to proceed?&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>Displays if the Error Code has previously been chosen.</description>
        <name>Error_Screen_Rejection_Code_Exists</name>
        <label>Error Screen - Rejection Code Exists</label>
        <locationX>1876</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>false</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Choose Different Rejection Code</backButtonLabel>
        <connector>
            <targetReference>Loop_Record_Collection</targetReference>
        </connector>
        <fields>
            <name>RejectionCode_AlreadyChosen_ErrorText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;The Rejection Code &lt;/span&gt;&lt;strong style=&quot;color: rgb(255, 0, 0);&quot;&gt;&quot;{!Rejection_Code}&quot;&lt;/strong&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt; has already been added. Please choose a different rejection Code.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen shows and error which indicated that the user is not permitted to perform the rejection of the transaction.</description>
        <name>Permission_Denied</name>
        <label>Permission Denied</label>
        <locationX>7706</locationX>
        <locationY>242</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>PermissionDeniedMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Your permissions will not allow the rejection of this transaction. Please contact your System Administrator&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen shows the reason and remedy for the chosen rejection code. Where the &quot;Editable On Use&quot; flag is set against the rejection code, they will be edirtable, otherwise read only.</description>
        <name>Reason_and_Remedy_Screen</name>
        <label>Reason and Remedy Screen</label>
        <locationX>1788</locationX>
        <locationY>2858</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Assign_Individual_Transaction_Rejection_Record</targetReference>
        </connector>
        <fields>
            <name>Selected_Rejection_Code_Text_Display_B</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt;Selected Rejection Code:&lt;/strong&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(68, 68, 68);&quot;&gt; {!Rejection_Code}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Reason</name>
            <defaultValue>
                <stringValue>{!ReasonTextFinal}</stringValue>
            </defaultValue>
            <fieldText>Reason</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <elementReference>ReasonRemedyReadOnly</elementReference>
            </isDisabled>
            <isReadOnly>
                <elementReference>ReasonRemedyReadOnly</elementReference>
            </isReadOnly>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Remedy</name>
            <defaultValue>
                <stringValue>{!Chosen_Remedy_Text}</stringValue>
            </defaultValue>
            <fieldText>Remedy</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <elementReference>ReasonRemedyReadOnly</elementReference>
            </isDisabled>
            <isReadOnly>
                <elementReference>ReasonRemedyReadOnly</elementReference>
            </isReadOnly>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>The success screen displayed at the end of the process.</description>
        <name>Success_Screen</name>
        <label>Success Screen</label>
        <locationX>3526</locationX>
        <locationY>5450</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMessageText</name>
            <fieldText>&lt;p&gt;The Transaction has been successfully rejected.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This data table displays a list of transaction parties.</description>
        <name>Transaction_Party_Selection_Table</name>
        <label>Transaction Party Selection Table</label>
        <locationX>1590</locationX>
        <locationY>1574</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Are_any_parties_selected</targetReference>
        </connector>
        <fields>
            <name>Selected_Rejection_Code_Text_Display_A</name>
            <fieldText>&lt;p&gt;&lt;strong&gt;Selected Rejection Code:&lt;/strong&gt; {!Rejection_Code}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Select_Transaction_Party</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Transaction_Party__c</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Impacted Parties</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>MULTI_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <elementReference>MinTPSelection</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Transaction_Parties</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Party_Name__c&quot;,&quot;guid&quot;:&quot;column-f768&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Name&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Party Name&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Role__c&quot;,&quot;guid&quot;:&quot;column-d767&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Role&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Role&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>DisplayTPSelection</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen will display when the flow encounters an error with updating the transaction record.</description>
        <name>Unable_to_create_Transaction_Rejection_Records</name>
        <label>Unable to create Transaction Rejection Records</label>
        <locationX>7178</locationX>
        <locationY>3722</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Claim_UnableToReject_Message_AddTxnRejections</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Something went wrong. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen will display when the flow encounters an error with getting the transaction parties.</description>
        <name>Unable_to_get_Transaction_Parties</name>
        <label>Unable to get Transaction Parties</label>
        <locationX>7442</locationX>
        <locationY>1058</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_Copy_2_of_Copy_1_of_Claim_UnableToReject_Message</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Something went wrong. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen will display when the flow encounters an error with updating the transaction party records.</description>
        <name>Unable_to_update_TP_Records</name>
        <label>Unable to update TP Records</label>
        <locationX>6914</locationX>
        <locationY>4478</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Claim_UnableToReject_Message</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Something went wrong. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen will display when the flow encounters an error with updating the transaction record.</description>
        <name>Unable_to_update_Transaction_Record_with_Comments</name>
        <label>Unable to update Transaction Record with Comments</label>
        <locationX>3526</locationX>
        <locationY>3938</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Claim_UnableToReject_Message_TxnUpdComments</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Something went wrong. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <description>This screen will display when the flow encounters an error with updating the transaction record.</description>
        <name>Unable_to_update_Transaction_Record_Without_Comments</name>
        <label>Unable to update Transaction Record Without Comments</label>
        <locationX>4054</locationX>
        <locationY>3938</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Claim_UnableToReject_Message_UpdTxn</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Something went wrong. Please contact your System Administrator.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>4621</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_Permissions</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>The Bond Lodgement Rejection Email Body</description>
        <name>Email_Body_BL</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;This advice is from the Residential Tenancies Bond Authority (RTBA).&lt;/p&gt;&lt;p&gt;The bond lodgement {!Get_case_Details_related_to_Transaction.CaseNumber} cannot be processed by the RTBA for the following reasons:&lt;/p&gt;&lt;p&gt;{!var_RejectionCodeText}&lt;/p&gt;&lt;p&gt;Please note that any payment that was included with the bond lodgement has been banked by the RTBA. Please address the issues raised in this email and submit a new, corrected lodgement form only. Do not include any payment with the new lodgement form.&lt;/p&gt;&lt;p&gt;If you require further information, please email&amp;nbsp;&lt;a href=&quot;mailto:<EMAIL>&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot; style=&quot;color: rgb(11, 92, 171);&quot;&gt;<EMAIL>&lt;/a&gt;&amp;nbsp;or telephone us on 1300 137 164.&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>Email_Body_RT</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;This advice is from the Residential Tenancies Bond Authority (RTBA).&lt;/p&gt;&lt;p&gt;Case Number {!Get_case_Details_related_to_Transaction.CaseNumber}&lt;/p&gt;&lt;p&gt;The renter transfer cannot be processed by the RTBA for the following reasons:&lt;/p&gt;&lt;p&gt;{!var_RejectionCodeText}&lt;/p&gt;&lt;p&gt;Please initiate a new renter transfer, if required.&lt;/p&gt;&lt;p&gt;If you require further information, please email&amp;nbsp;&lt;a href=&quot;mailto:<EMAIL>&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;<EMAIL>&lt;/a&gt;&amp;nbsp;or telephone us on 1300 137 164.&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <description>The subject for the bond lodgement transaction rejections.</description>
        <name>Subject_BL</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Bond Lodgement Rejected by the RTBA ({!Get_case_Details_related_to_Transaction_BL.CaseNumber}) - {!recordId.Rented_Property_Address__c}</text>
    </textTemplates>
    <textTemplates>
        <name>Subject_RT</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Renter Transfer Rejected (Bond Number {!recordId.Bond__r.Name})</text>
    </textTemplates>
    <variables>
        <name>Chosen_RC_EditableOnUse</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Chosen_RC_ImpactedPartyRequired</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Chosen_Reason_Text</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Chosen_Remedy_Text</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Variable to be set which will toggle the TP Selection in the flow.</description>
        <name>DisplayTPSelection</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Individual_Transaction_Rejection_Record</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Rejection__c</objectType>
    </variables>
    <variables>
        <description>Variable to set the minimum TP Selection</description>
        <name>MinTPSelection</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <description>Set a flag preventing the editing of the Reason and Remedy</description>
        <name>ReasonRemedyReadOnly</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>The record Id of the transaction being passed to the flow.</description>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction__c</objectType>
    </variables>
    <variables>
        <description>A comma delimited list of selected parties.</description>
        <name>SelectedPartiesList</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Transaction_Rejection_Record</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Transaction_Rejection__c</objectType>
    </variables>
    <variables>
        <name>var_RejectionCodeText</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
