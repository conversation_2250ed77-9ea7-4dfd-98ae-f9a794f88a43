@IsTest
private class TransfereeReviewControllerTest {
	static final String RP_USERNAME =
		'<EMAIL>' +
		EnvironmentUtility.getUsernameSuffix();
	static final String RP_USERNAME_2 =
		'<EMAIL>' +
		EnvironmentUtility.getUsernameSuffix();

	@TestSetup
	static void setup() {
		App_Settings__c settings = new App_Settings__c(
			Encryption_Key__c = 'vZvcaaLdZMQdqP7Fj3ywSIYYXyJnE5A3O/08YT1/XFU='
		);
		upsert settings;

		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;

		Contact rpc = TestDataFactory.createContact(rentalProvider);
		insert rpc;

		User rpu = TestDataFactory.createRentalProviderUser(
			rpc,
			[
				SELECT Id
				FROM Profile
				WHERE
					Name = :Constants.PROFILE_RTBA_CUSTOMER_COMMUNITY_LOGIN_USER
			]
		);
		rpu.Username = RP_USERNAME;
		insert rpu;

		Account transfereeRentalProvider = TestDataFactory.createRentalProviderAccount();
		transfereeRentalProvider.Name = 'New Agents';
		insert transfereeRentalProvider;

		Contact transfereeContact = TestDataFactory.createContact(
			transfereeRentalProvider
		);
		transfereeContact.LastName = 'Transferee Last Name';
		transfereeContact.Email = '<EMAIL>';
		insert transfereeContact;

		User transfereeUser = TestDataFactory.createRentalProviderUser(
			transfereeContact,
			[
				SELECT Id
				FROM Profile
				WHERE
					Name = :Constants.PROFILE_RTBA_CUSTOMER_COMMUNITY_LOGIN_USER
			]
		);
		transfereeUser.Username = RP_USERNAME_2;
		insert transfereeUser;

		// Assign L4 permission to created user
		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpu.Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c transactionRecord = TestDataFactory.createTransaction(
			rentalProvider
		);
		insert transactionRecord;

		Bond__c bondRecord = TestDataFactory.createBond(transactionRecord);
		insert bondRecord;
		Test.setCreatedDate(bondRecord.Id, Datetime.now().addMonths(-1));

		Account nathanSmithPA = TestDataFactory.createRenterPersonAccount(
			'Nathan',
			'Smith',
			'<EMAIL>'
		);
		insert nathanSmithPA;
		Account nathanSmithWithContact = [
			SELECT Id, PersonContactId
			FROM Account
			WHERE Id = :nathanSmithPA.Id
		];
		Bond_Party__c bp = TestDataFactory.createBondParty(
			bondRecord,
			Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		bp.Role__c = Constants.BOND_PARTY_ROLE_RENTER;
		bp.Status__c = Constants.BOND_PARTY_STATUS_ACTIVE;
		bp.Rental_Provider__c = nathanSmithWithContact.Id;
		bp.Renter__c = nathanSmithWithContact.PersonContactId;

		Bond_Party__c bpRP = TestDataFactory.createBondParty(
			bondRecord,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		bpRP.Email_Address__c = '<EMAIL>';
		bpRP.Rental_Provider__c = rentalProvider.Id;
		bpRP.Role__c = Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER;
		bpRP.Status__c = Constants.BOND_PARTY_STATUS_ACTIVE;

		Bond_Party__c inactiveBp = TestDataFactory.createBondParty(
			bondRecord,
			Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		inactiveBp.First_Name__c = 'Jane';
		inactiveBp.Family_Name__c = 'Smith';
		inactiveBp.Status__c = Constants.BOND_PARTY_STATUS_INACTIVE;
		inactiveBp.Role__c = Constants.BOND_PARTY_ROLE_RENTER;

		Bond_Party__c inactiveBpRP = TestDataFactory.createBondParty(
			bondRecord,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		inactiveBpRP.Company_Name__c = 'TCC Ltd';
		inactiveBpRP.Role__c = Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER;
		inactiveBpRP.Status__c = Constants.BOND_PARTY_STATUS_INACTIVE;

		insert new List<Bond_Party__c>{ bp, bpRP, inactiveBp, inactiveBpRP };

		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE;
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTAL_PROVIDER_TRANSFER;
		transferTx.Transfer_Option__c = Constants.TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_ONE_BOND;
		transferTx.Revision_Number__c = 1;
		insert transferTx;

		List<Transaction_Party__c> tpList = TestDataFactory.createTransactionParties(
			transferTx,
			2
		);

		tpList[0].Role__c = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		tpList[0]
			.Rental_Provider_Type__c = Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE;
		tpList[0]
			.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;
		tpList[0].Renter__c = transfereeContact.Id;

		tpList[1].Role__c = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		tpList[1]
			.Rental_Provider_Type__c = Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR;
		tpList[1].Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		tpList[1].Rental_Provider__c = rentalProvider.Id;
		tpList[1].Renter__c = rpc.Id;
		insert tpList;

		insert new Transfer_Transaction_Bonds__c(
			Transfer_Transaction__c = transferTx.Id,
			Bond__c = bondRecord.Id
		);
	}

	@IsTest
	static void getTransaction_ShouldReturnException_WhenTransfereeRejected() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];
		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		update tx;

		Transaction_Party__c transferee = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		transferee.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RP_REJECTED;
		update transferee;

		String encryptedId = EncryptionUtility.encryptWithKey(transferee.Id);

		// When
		transfereeReviewController.ReviewResponse result = null;

		Boolean exceptionThrown = false;
		String exceptionMessage;

		Test.startTest();

		try {
			System.runAs(rpUser[0]) {
				result = transfereeReviewController.getTransaction(encryptedId);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.isNull(result, 'Should not return a response.');
		Assert.areEqual(
			Label.Transferee_Review_Message_When_RP_Rejected,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	static void getTransaction_ShouldReturnException_WhenTransferorCancelled() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];
		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		update tx;

		Transaction_Party__c transferor = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR
			LIMIT 1
		];

		transferor.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RP_CANCELLED;
		update transferor;

		Transaction_Party__c transferee = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		String encryptedId = EncryptionUtility.encryptWithKey(transferee.Id);

		// When
		transfereeReviewController.ReviewResponse result = null;

		Boolean exceptionThrown = false;
		String exceptionMessage;

		Test.startTest();

		try {
			System.runAs(rpUser[0]) {
				result = transfereeReviewController.getTransaction(encryptedId);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.isNull(result, 'Should not return a response.');
		Assert.areEqual(
			Label.Transferee_Review_Message_When_RP_Cancelled,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	static void getTransaction_ShouldReturnException_WhenTransactionFinilised() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];
		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		update tx;

		Transaction_Party__c transferee = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		String encryptedId = EncryptionUtility.encryptWithKey(transferee.Id);

		// When
		transfereeReviewController.ReviewResponse result = null;

		Boolean exceptionThrown = false;
		String exceptionMessage;

		Test.startTest();

		try {
			System.runAs(rpUser[0]) {
				result = transfereeReviewController.getTransaction(encryptedId);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.isNull(result, 'Should not return a response.');
		Assert.areEqual(
			Label.Transferee_Review_Message_When_Finalised,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	static void getTransaction_ShouldReturnException_WhenTransactionTimedOut() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];
		tx.Status__c = Constants.TRANSACTION_STATUS_TIMEDOUT;
		update tx;

		Transaction_Party__c transferee = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		String encryptedId = EncryptionUtility.encryptWithKey(transferee.Id);

		// When
		transfereeReviewController.ReviewResponse result = null;

		Boolean exceptionThrown = false;
		String exceptionMessage;

		Test.startTest();

		try {
			System.runAs(rpUser[0]) {
				result = transfereeReviewController.getTransaction(encryptedId);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.isNull(result, 'Should not return a response.');
		Assert.areEqual(
			Label.Transferee_Review_Message_When_Timed_Out,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	static void isNewOwner_ShouldReturnTrue_WhenTransfereeHasNoAccount() {
		// Given
		List<User> transfereeUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME_2
			LIMIT 1
		];

		Transaction_Party__c transferee = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, OwnerId
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];

		// When
		Test.startTest();

		Boolean result = false;

		System.runAs(transfereeUser[0]) {
			result = transfereeReviewController.isNewOwner(
				transfereeUser[0].Id,
				transferee.Id,
				tx
			);
		}

		Test.stopTest();

		// Then
		Assert.isTrue(result, 'Should be a new owner.');
	}

	@IsTest
	static void isNewOwner_ShouldReturnFalse_WhenTransfereeHasAccount() {
		// Given
		List<User> transfereeUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME_2
			LIMIT 1
		];

		Account newRP = [
			SELECT Id, Name
			FROM Account
			WHERE Name = 'New Agents'
			LIMIT 1
		];

		Transaction_Party__c transferee = [
			SELECT Id, Rental_Provider__c
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		transferee.Rental_Provider__c = newRP.Id;
		update transferee;

		Transaction__c tx = [
			SELECT Id, OwnerId
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];

		// When
		Test.startTest();

		Boolean result = false;

		System.runAs(transfereeUser[0]) {
			result = transfereeReviewController.isNewOwner(
				transfereeUser[0].Id,
				transferee.Id,
				tx
			);
		}

		Test.stopTest();

		// Then
		Assert.isFalse(result, 'Should not be a new owner.');
	}

	@IsTest
	static void getNewOwnerAccount_ShouldReturnUserAccount() {
		// Given
		List<User> transfereeUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME_2
			LIMIT 1
		];

		// When
		Test.startTest();

		Account result = null;

		System.runAs(transfereeUser[0]) {
			result = transfereeReviewController.WITHOUT_SHARING
				.getNewOwnerAccount(transfereeUser[0].Id);
		}

		Test.stopTest();

		// Then
		Assert.isNotNull(result, 'The user account should be returned.');
	}

	@IsTest
	static void changeTransactionOwner_ShouldUpdateTheTransactionOwner() {
		// Given
		List<User> transfereeUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME_2
			LIMIT 1
		];

		Transaction_Party__c transferee = [
			SELECT Id, Rental_Provider__c
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		Account newRP = [
			SELECT Id, Name
			FROM Account
			WHERE Name = 'New Agents'
			LIMIT 1
		];

		// When
		Test.startTest();

		Transaction__c result = null;

		System.runAs(transfereeUser[0]) {
			result = transfereeReviewController.WITHOUT_SHARING
				.changeTransactionOwner(
					transferee.Id,
					transfereeUser[0].Id,
					newRP.Id
				);
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			transfereeUser[0].Id,
			result.OwnerId,
			'The Transaction Owner should have changed to the new owner.'
		);
		Assert.areEqual(
			newRP.Id,
			result.Rental_Provider__c,
			'The Transaction Account should have changed to the new owner account.'
		);
	}

	@IsTest
	static void setTransfereeAccount_ShouldUpdateTransfereeAccount() {
		// Given
		List<User> transfereeUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME_2
			LIMIT 1
		];

		Transaction_Party__c transferee = [
			SELECT Id, Rental_Provider__c
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		Account newRP = [
			SELECT Id, Name
			FROM Account
			WHERE Name = 'New Agents'
			LIMIT 1
		];

		// When
		Test.startTest();

		Transaction_Party__c result = null;

		System.runAs(transfereeUser[0]) {
			result = transfereeReviewController.WITHOUT_SHARING
				.setTransfereeAccount(transferee.Id, newRP);
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			newRP.Id,
			result.Rental_Provider__c,
			'The Transaction Party Account should have changed to the new owner account.'
		);
	}

	@IsTest
	static void getRelatedBonds_ShouldReturnResult() {
		// Given
		Transaction__c tx = [
			SELECT Id
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];

		// When
		Test.startTest();

		List<Bond__c> result = transfereeReviewController.WITHOUT_SHARING
			.getRelatedBonds(tx.Id);

		Test.stopTest();

		// Then
		Assert.isNotNull(result, 'Related Bonds should have returned.');
		Assert.areEqual(1, result.size(), 'There should be one bond returned.');

		Bond__c resultBond = result[0];
		Assert.areEqual(
			2,
			resultBond.Bond_Parties__r.size(),
			'Only active bond parties should be returned for the bond.'
		);
	}

	@IsTest
	static void getTransaction_ShouldReturnResult_WhenTransactionValid() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];

		Transaction_Party__c transferee = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		String encryptedId = EncryptionUtility.encryptWithKey(transferee.Id);

		// When
		transfereeReviewController.ReviewResponse result = null;

		Test.startTest();

		System.runAs(rpUser[0]) {
			result = transfereeReviewController.getTransaction(encryptedId);
		}

		Test.stopTest();

		// Then
		Assert.isNotNull(result, 'Should return a response.');
		Assert.areEqual(
			tx.Id,
			result.transactionRecord.Id,
			'Transfer transaction should be returned.'
		);
		Assert.areEqual(1, result.bonds.size(), 'One bond should be returned.');
	}

	@IsTest
	static void rejectTransfer_ShouldReturnMessage_WhenTransactionValid() {
		// Given
		List<User> transfereeUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME_2
			LIMIT 1
		];

		Transaction_Party__c transferee = [
			SELECT Id, Rental_Provider__c
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];

		// When
		Test.startTest();

		TransfereeReviewController.ActionResponse result = null;

		System.runAs(transfereeUser[0]) {
			TransfereeReviewController.WITHOUT_SHARING
				.transferTransactionToNewRP(
					transferee.Id,
					transfereeUser[0].Id
				);
			result = TransfereeReviewController.rejectTransfer(tx.Id);
		}

		Test.stopTest();

		Transaction__c returnedTx = [
			SELECT
				Id,
				Name,
				Status__c,
				(
					SELECT Id, Party_Status__c, Rental_Provider_Type__c
					FROM Transaction_Parties__r
				)
			FROM Transaction__c
			WHERE Id = :tx.Id
			LIMIT 1
		];

		// Then
		Assert.isNotNull(result, 'Should return a response.');

		Assert.areEqual(
			Constants.TRANSACTION_STATUS_CANCELLED,
			returnedTx.Status__c,
			'Transaction status should be updated to Cancelled.'
		);

		for (Transaction_Party__c party : returnedTx.Transaction_Parties__r) {
			if (
				party.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_RP_REJECTED,
					party.Party_Status__c,
					'Transferee transaction party status should be updated to RP Rejected.'
				);
			}

			if (
				party.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR
			) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_CANCELLED,
					party.Party_Status__c,
					'Transferor transaction party status should be updated to Cancelled.'
				);
			}
		}
		String expectedMessage = StringUtility.replacePlaceholders(
			transfereeReviewController.SUCCESS_REJECT_TRANSFER,
			new Map<String, String>{ 'transactionNumber' => tx.Name }
		);
		Assert.areEqual(
			expectedMessage,
			result.message,
			'Should have a correct success reject transfer message'
		);
	}

	@IsTest
	static void acceptTransfer_ShouldReturnMessage_WhenTransactionValid() {
		MCJourneyControllerTest.ApiMockSuccessV2 mock = MCJourneyControllerTest.setApiMockSuccessV2();
		// Given
		User transfereeUser = [
			SELECT Id, ContactId, Contact.Email, Account.Transaction_notices__c
			FROM User
			WHERE Username = :RP_USERNAME_2
			LIMIT 1
		];
		Assert.isNotNull(
			transfereeUser.Account.Transaction_notices__c,
			'Expected tx notices'
		);
		Assert.areNotEqual(
			transfereeUser.Contact.Email,
			transfereeUser.Account.Transaction_notices__c,
			'Expected tx notices email to be different'
		);

		Transaction__c tx = [
			SELECT Id, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];

		Transaction_Party__c transferee = [
			SELECT Id
			FROM Transaction_Party__c
			WHERE
				Transaction__c = :tx.Id
				AND Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		Transaction_Party__c transferor = [
			SELECT
				Id,
				Renter__c,
				Email_Address__c,
				Rental_Provider__c,
				Rental_Provider__r.Transaction_notices__c
			FROM Transaction_Party__c
			WHERE
				Transaction__c = :tx.Id
				AND Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR
			LIMIT 1
		];

		Assert.isNotNull(
			transferor.Rental_Provider__r.Transaction_notices__c,
			'Expected tx notices email'
		);
		Assert.areNotEqual(
			transferor.Email_Address__c,
			transferor.Rental_Provider__r.Transaction_notices__c,
			'Expected different tx notices email'
		);

		Assert.isNotNull(
			transferor.Renter__c,
			'Expected contact linked to transferor'
		);

		Account newRP = [
			SELECT Id, Name
			FROM Account
			WHERE Name = 'New Agents'
			LIMIT 1
		];

		// When
		Test.startTest();

		transfereeReviewController.ActionResponse result = null;

		System.runAs(transfereeUser) {
			transfereeReviewController.WITHOUT_SHARING
				.transferTransactionToNewRP(transferee.Id, transfereeUser.Id);
			result = transfereeReviewController.acceptTransfer(tx.Id);
		}

		Test.stopTest();

		Transaction__c returnedTx = [
			SELECT
				Id,
				Name,
				Status__c,
				Rental_Provider__c,
				(
					SELECT
						Id,
						Party_Status__c,
						Rental_Provider_Type__c,
						Latest_Journey_MC__c,
						Renter__c,
						Rental_Provider__r.Transaction_notices__c
					FROM Transaction_Parties__r
				)
			FROM Transaction__c
			WHERE Id = :tx.Id
			LIMIT 1
		];

		// Then
		Assert.isNotNull(result, 'Should return a response.');

		Assert.areEqual(
			Constants.TRANSACTION_STATUS_FINALISED,
			returnedTx.Status__c,
			'Transaction status should be updated to Finalised.'
		);

		for (Transaction_Party__c party : returnedTx.Transaction_Parties__r) {
			if (
				party.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_AGREED,
					party.Party_Status__c,
					'Transferee transaction party status should be updated to Agreed.'
				);
				Assert.isNotNull(
					party.Latest_Journey_MC__c,
					'Expected journey set'
				);
				Assert.isTrue(
					party.Latest_Journey_MC__c.contains(
						Constants.MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEREE
					),
					'Expected finalised journey'
				);
				MCModel.JourneyEntryEvent event = mock.findEntry(
					Constants.MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEREE,
					party.Renter__c
				);
				Assert.isNotNull(
					event,
					'Expected to find finalisation email for transferee'
				);
				Assert.areEqual(
					party.Rental_Provider__r.Transaction_notices__c,
					event.Data.get('Txp_EmailAddress'),
					'expected tx notices as dest address'
				);
			} else if (
				party.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR
			) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_AGREED,
					party.Party_Status__c,
					'Transferor transaction party status should remains as Agreed.'
				);
				Assert.isNotNull(
					party.Latest_Journey_MC__c,
					'Expected journey set'
				);
				Assert.isTrue(
					party.Latest_Journey_MC__c.contains(
						Constants.MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEROR
					),
					'Expected finalised journey'
				);
				MCModel.JourneyEntryEvent event = mock.findEntry(
					Constants.MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEROR,
					party.Renter__c
				);
				Assert.isNotNull(
					event,
					'Expected to find finalisation email for transferor'
				);
				Assert.areEqual(
					party.Rental_Provider__r.Transaction_notices__c,
					event.Data.get('Txp_EmailAddress'),
					'expected tx notices as dest address'
				);
			}
		}

		List<Bond__c> returnedBonds = transfereeReviewController.WITHOUT_SHARING
			.getRelatedBonds(tx.Id);

		for (Bond__c bond : returnedBonds) {
			Assert.areEqual(
				bond.Rental_Provider__c,
				newRP.Id,
				'The Bond Rental Provider should have changed to the new owner account.'
			);
			for (Bond_Party__c bp : bond.Bond_Parties__r) {
				if (bp.Role__c != Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER) {
					continue;
				}

				if (bp.Email_Address__c == '<EMAIL>') {
					Assert.areEqual(
						bp.Status__c,
						Constants.BOND_PARTY_STATUS_INACTIVE,
						'Expeceted previous bond party for RP with inavtive status'
					);
				}

				if (bp.Email_Address__c == '<EMAIL>') {
					Assert.areEqual(
						bp.Status__c,
						Constants.BOND_PARTY_STATUS_ACTIVE,
						'Expeceted new bond party for RP with avtive status'
					);
				}
			}
		}

		List<Bond_Party__c> renters = [
			SELECT Id, Latest_Journey_MC__c
			FROM Bond_Party__c
			WHERE
				Role__c = :Constants.BOND_PARTY_ROLE_RENTER
				AND Email_Address__c != ''
				AND Status__c = :Constants.BOND_PARTY_STATUS_ACTIVE
				AND Bond__c IN :returnedBonds
		];
		Assert.areEqual(1, renters.size(), 'expected the 1 renter');
		for (Bond_Party__c renter : renters) {
			Assert.isNotNull(
				renter.Latest_Journey_MC__c,
				'expected journey triggered'
			);
			Assert.isTrue(
				renter.Latest_Journey_MC__c.contains(
					Constants.MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_RENTERS
				),
				'expected journey triggered'
			);
		}

		// Then
		String expectedMessage = StringUtility.replacePlaceholders(
			TransfereeReviewController.SUCCESS_APPROVAL_TRANSFER,
			new Map<String, String>{ 'transactionNumber' => tx.Name }
		);
		Assert.areEqual(
			expectedMessage,
			result.message,
			'Should have a correct success approval transfer message'
		);
	}

	@IsTest
	static void transferBondsToNewRP_ShouldWork_WhenTransactionValid() {
		// Given
		Transaction_Party__c transferee = [
			SELECT Id, Rental_Provider__c
			FROM Transaction_Party__c
			WHERE
				Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			LIMIT 1
		];

		Account newRP = [
			SELECT Id, Name
			FROM Account
			WHERE Name = 'New Agents'
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_TRANSFEREE
			LIMIT 1
		];

		// When
		Test.startTest();

		TransfereeReviewController.WITHOUT_SHARING
			.changeTransactionOwner(transferee.Id, null, newRP.Id);

		TransfereeReviewController.TransferBondsToNewRpInput input = new TransfereeReviewController.TransferBondsToNewRpInput();
		input.transactionId = tx.Id;
		input.newRpId = newRP.Id;
		List<TransfereeReviewController.TransferBondsToNewRpInput> inputs = new List<TransfereeReviewController.TransferBondsToNewRpInput>{
			input
		};
		TransfereeReviewController.transferBondsToNewRP(inputs);
		Test.stopTest();

		// Then

		List<Bond__c> returnedBonds = TransfereeReviewController.WITHOUT_SHARING
			.getRelatedBonds(tx.Id);

		for (Bond__c bond : returnedBonds) {
			Assert.areEqual(
				bond.Rental_Provider__c,
				newRP.Id,
				'The Bond Rental Provider should have changed to the new owner account.'
			);
			for (Bond_Party__c bp : bond.Bond_Parties__r) {
				if (bp.Role__c != Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER) {
					continue;
				}

				if (bp.Email_Address__c == '<EMAIL>') {
					Assert.areEqual(
						bp.Status__c,
						Constants.BOND_PARTY_STATUS_INACTIVE,
						'Expeceted previous bond party for RP with inactive status'
					);
				}

				if (bp.Email_Address__c == '<EMAIL>') {
					Assert.areEqual(
						bp.Status__c,
						Constants.BOND_PARTY_STATUS_ACTIVE,
						'Expeceted new bond party for RP with active status'
					);
				}
			}
		}
	}

	@IsTest
	static void transferInvocableValidations() {
		Test.startTest();
		{
			try {
				TransfereeReviewController.transferBondsToNewRP(
					new List<TransfereeReviewController.TransferBondsToNewRpInput>()
				);
				Assert.fail('Should have thrown');
			} catch (Exception e) {
				Assert.isInstanceOfType(
					e,
					IllegalArgumentException.class,
					'Expected illegal argument'
				);
			}
			try {
				TransfereeReviewController.transferBondsToNewRP(
					new List<TransfereeReviewController.TransferBondsToNewRpInput>{
						new TransfereeReviewController.TransferBondsToNewRpInput(),
						new TransfereeReviewController.TransferBondsToNewRpInput()
					}
				);
				Assert.fail('Should have thrown');
			} catch (Exception e) {
				Assert.isInstanceOfType(
					e,
					IllegalArgumentException.class,
					'Expected illegal argument'
				);
			}
		}
		Test.stopTest();
	}
}