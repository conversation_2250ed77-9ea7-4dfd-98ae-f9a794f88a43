<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <description>Sets the claim transaction name prefix to &quot;RIC&quot; for any claim (Paper Web) which is initiated by an Renter.</description>
        <name>Claim_TX_Name_2</name>
        <label>Claim_TX_Name_RIC</label>
        <locationX>314</locationX>
        <locationY>431</locationY>
        <assignmentItems>
            <assignToReference>TX_Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>RIC{!$Record.Transaction_Auto_Number__c}</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Transaction_Name</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets the claim transaction name prefix to &quot;BC&quot; for any claim (Paper or Web) which is initiated by an RP.</description>
        <name>Claim_TX_Name_RP</name>
        <label>Claim_TX_Name_RP</label>
        <locationX>50</locationX>
        <locationY>431</locationY>
        <assignmentItems>
            <assignToReference>TX_Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>{!Prefix_For_RP_Initiated_Claims}{!$Record.Transaction_Auto_Number__c}</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Transaction_Name</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Sets the transaction name prefix to to the appropriate prefix for a non-claim type transaction.</description>
        <name>Non_Claim_TX_Name</name>
        <label>Non Claim TX Name Prefic</label>
        <locationX>578</locationX>
        <locationY>431</locationY>
        <assignmentItems>
            <assignToReference>TX_Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NonClaimTransactionName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Transaction_Name</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>Prefix_For_Bond_Amendment</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BA</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction Prefix for Bond Lodgement</description>
        <name>Prefix_For_Bond_Lodgement</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BL</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction prefix for bond merge</description>
        <name>Prefix_For_Bond_Merge</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BM</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction Prefix for Modification Bond Lodgement</description>
        <name>Prefix_For_Modification_Bond_Lodgement</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BL</stringValue>
        </value>
    </constants>
    <constants>
        <description>Prefix for renter transfer</description>
        <name>Prefix_For_Renter_Transfer</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RT</stringValue>
        </value>
    </constants>
    <constants>
        <description>Prefix for retained repayment request</description>
        <name>Prefix_For_Retained_Repayment_Request</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RRR</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction Prefix for RP Initiated Claims</description>
        <name>Prefix_For_RP_Initiated_Claims</name>
        <dataType>String</dataType>
        <value>
            <stringValue>BC</stringValue>
        </value>
    </constants>
    <constants>
        <description>Transaction prefix for RP Transfer</description>
        <name>Prefix_For_RP_Transfer</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RPT</stringValue>
        </value>
    </constants>
    <decisions>
        <description>Determine the type of transaction.</description>
        <name>Based_on_type</name>
        <label>Based on type</label>
        <locationX>314</locationX>
        <locationY>323</locationY>
        <defaultConnector>
            <targetReference>Non_Claim_TX_Name</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Non Claim Type</defaultConnectorLabel>
        <rules>
            <name>Claim_with_Rental_Provider_Initiated_Claim</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>CLAIMS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Claim_TX_Name_RP</targetReference>
            </connector>
            <label>Claim with Rental Provider Initiated Claim</label>
        </rules>
        <rules>
            <name>Claim_with_Renter_Initiated_Claim</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>CLAIMS</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Claim_TX_Name_2</targetReference>
            </connector>
            <label>Claim with Renter Initiated Claim</label>
        </rules>
    </decisions>
    <description>This auto launched flow updates the transaction name with the correct prefix and auto number for the transaction based on the type of transaction.</description>
    <environments>Default</environments>
    <formulas>
        <name>NonClaimTransactionName</name>
        <dataType>String</dataType>
        <expression>CASE(
            {!$Record.Type__c}, &apos;Bond Lodgement&apos;, {!Prefix_For_Bond_Lodgement},
            &apos;Modification Bond Lodgement&apos;, {!Prefix_For_Modification_Bond_Lodgement},
            &apos;Claims&apos;, &apos;CL&apos;,
            &apos;Rental Provider Transfer&apos;, {!Prefix_For_RP_Transfer},
           &apos;Renter Transfer&apos;, {!Prefix_For_Renter_Transfer},
           &apos;Retained Repayment Request&apos;, {!Prefix_For_Retained_Repayment_Request},
           &apos;Bond Merge&apos;, {!Prefix_For_Bond_Merge},
           &apos;Bond Amendment&apos;, {!Prefix_For_Bond_Amendment},
            &apos;&apos;
            )+{!$Record.Transaction_Auto_Number__c}</expression>
    </formulas>
    <interviewLabel>Update Transaction Name {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Update Transaction Name</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Update the transaction name with the correct number and prefix.</description>
        <name>Update_Transaction_Name</name>
        <label>Update Transaction Name</label>
        <locationX>314</locationX>
        <locationY>623</locationY>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>TX_Name</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Based_on_type</targetReference>
        </connector>
        <object>Transaction__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>TX_Name</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
