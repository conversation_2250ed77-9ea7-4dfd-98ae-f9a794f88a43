@isTest
private class RepaymentServiceTest {
	@testSetup
	static void setup() {
		Account rpAccount = TestDataFactory.createRentalProviderAccount();
		insert rpAccount;

		Contact rpContact = TestDataFactory.createContact(rpAccount);
		insert rpContact;

		Profile userProfile = TestDataFactory.getCommunityUserProfile();

		User rpUser = TestDataFactory.createRentalProviderUser(
			rpContact,
			userProfile
		);
		insert rpUser;

		Transaction__c trans = TestDataFactory.createTransaction(rpAccount);
		insert trans;

		Bond__c bond = TestDataFactory.createBond(trans);
		insert bond;

		Bank_Account__c testBankAccount = TestDataFactory.createBankAccount(
			'Test Bank',
			0
		);
		insert testBankAccount;
	}

	@isTest
	static void runDirectDebitReturnReconciliation_Success() {
		// Given
		Bond__c testBond = [SELECT Id FROM Bond__c LIMIT 1];
		Transaction__c testTransaction = [
			SELECT Id
			FROM Transaction__c
			LIMIT 1
		];
		Contact testContact = [SELECT Id FROM Contact LIMIT 1];
		Bank_Account__c testBankAccount = [
			SELECT Id
			FROM Bank_Account__c
			LIMIT 1
		];

		Bank_Account_Statement__c testPaymentStatement = TestDataFactory.createBankAccountStatement(
			testBankAccount.Id,
			'123',
			0
		);
		insert testPaymentStatement;

		Bank_Account_Statement_Transaction__c testTrans = TestDataFactory.createBankAccountStatementTransaction(
			testPaymentStatement.Id,
			100.00,
			0.00,
			'Test 123'
		);
		testTrans.Transaction_Header_Credit_Debit_Indic__c = 'DBIT';
		testTrans.Additional_Transaction_Information__c = 'DIRECT DEBIT RETURNED 260325 BULK ENTRY SEE DERPS DATA';
		testTrans.Bank_Statement_File_Name__c = 'RTBA_CAMT053_135741_20250516_000000.xml';
		insert testTrans;

		Payment__c originalPayment = TestDataFactory.createDirectDebitPayment(
			testBond.Id,
			testTransaction.Id,
			testContact.Id,
			100.00
		);
		originalPayment.Westpac_Disbursement_File_Name__c = 'TestFile.csv';
		originalPayment.Westpac_DERPs_File_Name__c = 'RTBA_DD_DEReturns_05162025_153901_2976096805.txt';
		originalPayment.status__c = Constants.PAYMENT_STATUS_RETURNED;
		insert originalPayment;

		Repayment__c testRepayment = TestDataFactory.createRepayment(
			testBond.Id,
			Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			Constants.REPAYMENT_STATUS_SUCCESSFUL,
			100.00
		);
		testRepayment.Original_Payment__c = originalPayment.Id;
		insert testRepayment;

		Bank_Account_Statement_Transaction__c bankTrans = [
			SELECT
				Id,
				Bank_Statement_Date__c,
				Reconciliation_Difference__c,
				Transaction_Amount_Balance_Impact__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testTrans.Id
			LIMIT 1
		];

		// When
		RepaymentService.runDirectDebitReturnReconciliation(bankTrans);

		// Then
		Repayment__c updatedRepayment = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Reconciliation_Difference__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE Original_Payment__c = :originalPayment.Id
			LIMIT 1
		];

		Bank_Account_Statement_Transaction__c updatedRepaymentBAST = [
			SELECT ID, Reconciled_Amount__c, Reconciliation_Difference__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :bankTrans.Id
			LIMIT 1
		];

		Assert.areEqual(
			-100,
			updatedRepayment.Reconciled_Amount__c,
			'Repayment Reconciled Amount Should Be -100'
		);

		Assert.areEqual(
			0,
			updatedRepayment.Reconciliation_Difference__c,
			'Repayment Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			bankTrans.Id,
			updatedRepayment.Bank_Account_Statement_Transaction__c,
			'Repayment should be linked to the transaction'
		);

		Assert.areEqual(
			-100,
			updatedRepaymentBAST.Reconciled_Amount__c,
			'BAST Reconciled Amount Should Be -100'
		);

		Assert.areEqual(
			0,
			updatedRepaymentBAST.Reconciliation_Difference__c,
			'BAST Reconciliation Difference Should Be Zero'
		);
	}

	@isTest
	static void runIntDCRecon_Success() {
		Bond__c testBond = [SELECT Id FROM Bond__c LIMIT 1];

		Bank_Account_Statement__c testStatement = TestDataFactory.createBankAccountStatement(
			[SELECT Id FROM Bank_Account__c LIMIT 1].Id,
			'123',
			0
		);
		insert testStatement;

		Bank_Account_Statement_Transaction__c testBankTrans = TestDataFactory.createBankAccountStatementTransaction(
			testStatement.Id,
			100.00,
			0.00,
			'Test 123'
		);
		testBankTrans.Transaction_Header_Credit_Debit_Indic__c = 'DBIT';
		testBankTrans.Additional_Transaction_Information__c = 'OVERSEAS TELEGRAPHIC TRANSFER REFERENCE 139236 WBC PAYPLUS-**********';
		testBankTrans.End_To_End_Identification__c = 'TestReference123';
		insert testBankTrans;

		Repayment__c testRepayment = TestDataFactory.createRepayment(
			testBond.Id,
			Constants.REPAYMENT_METHOD_INTERNATIONAL_DIRECT_CREDIT,
			100.00
		);
		testRepayment.International_Bank_Repayment_Reference__c = 'TestReference123';
		testRepayment.Status__c = Constants.REPAYMENT_STATUS_SUCCESSFUL;
		testRepayment.Swift_Code__c = 'ABCDEF8R7G3';
		insert testRepayment;

		Test.startTest();
		Set<Id> bankTransIds = new Set<Id>();
		bankTransIds.add(testBankTrans.Id);
		Bank_Account_Statement_Transaction__c testBankTransEnrich = InterpretationEngineService.getTransactions(
			bankTransIds
		)[0];

		RepaymentService.runIntDCRecon(testBankTransEnrich);
		Test.stopTest();

		Bank_Account_Statement_Transaction__c updatedBAST = [
			SELECT ID, Reconciled_Amount__c, Reconciliation_Difference__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testBankTrans.Id
			LIMIT 1
		];

		Repayment__c updatedRepayment = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Reconciliation_Difference__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE Id = :testRepayment.Id
			LIMIT 1
		];

		Assert.areEqual(
			-100,
			updatedBAST.Reconciled_Amount__c,
			'BAST Reconciled Amount Should Be -100'
		);

		Assert.areEqual(
			0,
			updatedBAST.Reconciliation_Difference__c,
			'BAST Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			updatedBAST.Id,
			updatedRepayment.Bank_Account_Statement_Transaction__c,
			'Repayment should be linked to the transaction'
		);

		Assert.areEqual(
			0,
			updatedRepayment.Reconciliation_Difference__c,
			'Repayment Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			-100,
			updatedRepayment.Reconciled_Amount__c,
			'Repayment Reconciled Amount Should Be -100'
		);
	}

	@isTest
	static void runAusDCRecon_Single_Success() {
		Bond__c testBond = [SELECT Id FROM Bond__c LIMIT 1];

		Bank_Account_Statement__c testStatement = TestDataFactory.createBankAccountStatement(
			[SELECT Id FROM Bank_Account__c LIMIT 1].Id,
			'123',
			0
		);
		insert testStatement;

		Bank_Account_Statement_Transaction__c testBankTrans = TestDataFactory.createBankAccountStatementTransaction(
			testStatement.Id,
			100.00,
			0.00,
			'Test 123'
		);
		testBankTrans.Transaction_Header_Credit_Debit_Indic__c = 'DBIT';
		testBankTrans.Additional_Transaction_Information__c = 'DIRECT ENTRY DRAWING DEDRBAL DE DRAW ID371700 RTBA PAYPLUS DIRECT CREDI';
		testBankTrans.End_To_End_Identification__c = 'TestReference123';
		insert testBankTrans;

		Repayment__c testRepayment = TestDataFactory.createRepayment(
			testBond.Id,
			Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			100.00
		);
		testRepayment.Repayment_Bank_Reference__c = 'TestReference123';
		testRepayment.Status__c = Constants.REPAYMENT_STATUS_SUCCESSFUL;
		insert testRepayment;

		Test.startTest();
		Set<Id> bankTransIds = new Set<Id>();
		bankTransIds.add(testBankTrans.Id);
		Bank_Account_Statement_Transaction__c testBankTransEnrich = InterpretationEngineService.getTransactions(
			bankTransIds
		)[0];

		RepaymentService.runAusDCRecon(testBankTransEnrich);
		Test.stopTest();

		Bank_Account_Statement_Transaction__c updatedBAST = [
			SELECT ID, Reconciled_Amount__c, Reconciliation_Difference__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testBankTrans.Id
			LIMIT 1
		];

		Repayment__c updatedRepayment = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Reconciliation_Difference__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE Id = :testRepayment.Id
			LIMIT 1
		];

		Assert.areEqual(
			-100,
			updatedBAST.Reconciled_Amount__c,
			'BAST Reconciled Amount Should Be -100'
		);

		Assert.areEqual(
			0,
			updatedBAST.Reconciliation_Difference__c,
			'BAST Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			updatedBAST.Id,
			updatedRepayment.Bank_Account_Statement_Transaction__c,
			'Repayment should be linked to the transaction'
		);

		Assert.areEqual(
			0,
			updatedRepayment.Reconciliation_Difference__c,
			'Repayment Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			-100,
			updatedRepayment.Reconciled_Amount__c,
			'Repayment Reconciled Amount Should Be -100'
		);
	}

	@isTest
	static void runAusDCRecon_Bulk_Success() {
		Bond__c testBond = [SELECT Id FROM Bond__c LIMIT 1];

		Bank_Account_Statement__c testStatement = TestDataFactory.createBankAccountStatement(
			[SELECT Id FROM Bank_Account__c LIMIT 1].Id,
			'123',
			0
		);
		insert testStatement;

		Bank_Account_Statement_Transaction__c testBankTrans = TestDataFactory.createBankAccountStatementTransaction(
			testStatement.Id,
			200.00,
			0.00,
			'Test 123'
		);
		testBankTrans.Transaction_Header_Credit_Debit_Indic__c = 'DBIT';
		testBankTrans.Additional_Transaction_Information__c = 'DIRECT ENTRY DRAWING DEDRBAL DE DRAW ID371700 RTBA PAYPLUS DIRECT CREDI';
		testBankTrans.End_To_End_Identification__c = 'TestReference123';
		insert testBankTrans;

		Repayment__c testBulkRepayment = new Repayment__c();
		testBulkRepayment.Bond__c = testBond.Id;
		testBulkRepayment.Repayment_Date__c = System.Today();
		testBulkRepayment.Payer__c = Constants.PAYMENT_PAYER_RENTAL_PROVIDER;
		testBulkRepayment.Payee_Type__c = 'RBIIA';
		testBulkRepayment.Repayment_Type__c = 'Bulk Repayment';
		testBulkRepayment.Repayment_Method__c = Constants.REPAYMENT_METHOD_DIRECT_CREDIT;
		testBulkRepayment.Status__c = Constants.REPAYMENT_STATUS_SUCCESSFUL;
		testBulkRepayment.Amount__c = 200;
		testBulkRepayment.BSB__c = '033547';
		testBulkRepayment.Account_Number__c = '********';
		testBulkRepayment.Repayment_Bank_Reference__c = 'TestReference123';
		insert testBulkRepayment;

		List<Repayment__c> childRepayments = new List<Repayment__c>();
		for (Integer i = 0; i < 20; i++) {
			// Create enough records for a batch
			Repayment__c rep = TestDataFactory.createRepayment(
				testBond.Id,
				Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
				10.00
			);
			rep.Parent_Bulk_Repayment__c = testBulkRepayment.Id;
			rep.Status__c = 'Unclaimed Money - RTBA';
			childRepayments.add(rep);
		}
		insert childRepayments;

		Test.startTest();
		Set<Id> bankTransIds = new Set<Id>();
		bankTransIds.add(testBankTrans.Id);
		Bank_Account_Statement_Transaction__c testBankTransEnrich = InterpretationEngineService.getTransactions(
			bankTransIds
		)[0];

		RepaymentService.runAusDCRecon(testBankTransEnrich);
		Test.stopTest();

		Bank_Account_Statement_Transaction__c updatedBAST = [
			SELECT ID, Reconciled_Amount__c, Reconciliation_Difference__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testBankTrans.Id
			LIMIT 1
		];

		Repayment__c updatedBulkRepayment = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE Id = :testBulkRepayment.Id
			LIMIT 1
		];

		List<Repayment__c> updatedChildRepayments = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE Parent_Bulk_Repayment__c = :testBulkRepayment.Id
		];

		Assert.areEqual(
			-200,
			updatedBAST.Reconciled_Amount__c,
			'BAST Reconciled Amount Should Be -200'
		);

		Assert.areEqual(
			0,
			updatedBAST.Reconciliation_Difference__c,
			'BAST Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			0,
			updatedBulkRepayment.Reconciled_Amount__c,
			'Bulk Repayment Reconciled Amount Should Be 0'
		);
		Assert.areEqual(
			updatedBAST.Id,
			updatedBulkRepayment.Bank_Account_Statement_Transaction__c,
			'Bulk Repayment should be linked to the transaction'
		);

		for (Repayment__c updatedChildRepayment : updatedChildRepayments) {
			Assert.areEqual(
				-10,
				updatedChildRepayment.Reconciled_Amount__c,
				'Child Repayment Reconciled Amount Should Be -10'
			);
			Assert.areEqual(
				updatedBAST.Id,
				updatedChildRepayment.Bank_Account_Statement_Transaction__c,
				'Child Repayment should be linked to the transaction'
			);
		}
	}

	@isTest
	static void repaymentsReconciliation_AmountDifferenceNegative_CreatesCase() {
		Bond__c testBond = [SELECT Id FROM Bond__c LIMIT 1];

		Bank_Account_Statement__c testStatement = TestDataFactory.createBankAccountStatement(
			[SELECT Id FROM Bank_Account__c LIMIT 1].Id,
			'123',
			0
		);
		insert testStatement;

		Bank_Account_Statement_Transaction__c testBankTrans = TestDataFactory.createBankAccountStatementTransaction(
			testStatement.Id,
			100.00,
			0.00,
			'Test 123'
		);
		testBankTrans.Transaction_Header_Credit_Debit_Indic__c = 'DBIT';
		testBankTrans.Additional_Transaction_Information__c = 'DIRECT ENTRY DRAWING DEDRBAL DE DRAW ID371700 RTBA PAYPLUS DIRECT CREDI';
		insert testBankTrans;

		Repayment__c testRepayment = TestDataFactory.createRepayment(
			testBond.Id,
			Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			150.00
		);
		testRepayment.Status__c = Constants.REPAYMENT_STATUS_SUCCESSFUL;
		testRepayment.Repayment_Bank_Reference__c = 'TestReference123';
		insert testRepayment;

		Test.startTest();
		Set<Id> bankTransIds = new Set<Id>();
		bankTransIds.add(testBankTrans.Id);
		Bank_Account_Statement_Transaction__c testBankTransEnrich = InterpretationEngineService.getTransactions(
			bankTransIds
		)[0];

		RepaymentService.repaymentsRecon(
			testBankTransEnrich,
			new List<Repayment__c>{ testRepayment }
		);
		Test.stopTest();

		Bank_Account_Statement_Transaction__c updatedBAST = [
			SELECT ID, Reconciled_Amount__c, Reconciliation_Difference__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testBankTrans.Id
			LIMIT 1
		];

		Repayment__c updatedRepayment = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Reconciliation_Difference__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE ID = :testRepayment.Id
			LIMIT 1
		];

		Case createdCase = [
			SELECT ID, Error_Amount__c, Bank_Account_Statement_Transaction__c
			FROM Case
			LIMIT 1
		];

		Assert.areEqual(
			-100,
			updatedBAST.Reconciled_Amount__c,
			'BAST Reconciled Amount Should Be -100'
		);

		Assert.areEqual(
			0,
			updatedBAST.Reconciliation_Difference__c,
			'BAST Reconciliation Difference Should Be Zero'
		);

		Assert.areNotEqual(
			updatedBAST.Id,
			updatedRepayment.Bank_Account_Statement_Transaction__c,
			'Payment should NOT be linked to the transaction'
		);

		Assert.areEqual(
			150,
			updatedRepayment.Reconciliation_Difference__c,
			'Repayment Reconciliation Difference Should Be 150'
		);

		Assert.areEqual(
			0,
			updatedRepayment.Reconciled_Amount__c,
			'Repayment Reconciled Amount Should Be 0'
		);

		Assert.areEqual(
			updatedBAST.Id,
			createdCase.Bank_Account_Statement_Transaction__c,
			'Case should be linked to the transaction'
		);

		Assert.areEqual(
			-100,
			createdCase.Error_Amount__c,
			'Case Error Amount Should Be -100'
		);
	}

	@isTest
	static void repaymentsReconciliation_AmountDifferencePositive_ZeroReconciledAmountRepayments() {
		Bond__c testBond = [SELECT Id FROM Bond__c LIMIT 1];

		Bank_Account_Statement__c testStatement = TestDataFactory.createBankAccountStatement(
			[SELECT Id FROM Bank_Account__c LIMIT 1].Id,
			'123',
			0
		);
		insert testStatement;

		Bank_Account_Statement_Transaction__c testBankTrans = TestDataFactory.createBankAccountStatementTransaction(
			testStatement.Id,
			100.00,
			0.00,
			'Test 123'
		);
		testBankTrans.Transaction_Header_Credit_Debit_Indic__c = 'DBIT';
		testBankTrans.Additional_Transaction_Information__c = 'DIRECT ENTRY DRAWING DEDRBAL DE DRAW ID371700 RTBA PAYPLUS DIRECT CREDI';
		insert testBankTrans;

		Repayment__c testRepayment = TestDataFactory.createRepayment(
			testBond.Id,
			Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			50.00
		);
		testRepayment.Status__c = Constants.REPAYMENT_STATUS_SUCCESSFUL;
		testRepayment.Repayment_Bank_Reference__c = 'TestReference123';
		insert testRepayment;

		Test.startTest();
		Set<Id> bankTransIds = new Set<Id>();
		bankTransIds.add(testBankTrans.Id);
		Bank_Account_Statement_Transaction__c testBankTransEnrich = InterpretationEngineService.getTransactions(
			bankTransIds
		)[0];

		RepaymentService.repaymentsRecon(
			testBankTransEnrich,
			new List<Repayment__c>{ testRepayment }
		);
		Test.stopTest();

		Bank_Account_Statement_Transaction__c updatedBAST = [
			SELECT ID, Reconciled_Amount__c, Reconciliation_Difference__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testBankTrans.Id
			LIMIT 1
		];

		Repayment__c updatedRepayment = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Reconciliation_Difference__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE ID = :testRepayment.Id
			LIMIT 1
		];

		Case createdCase = [
			SELECT ID, Error_Amount__c, Bank_Account_Statement_Transaction__c
			FROM Case
			LIMIT 1
		];

		Assert.areEqual(
			-100,
			updatedBAST.Reconciled_Amount__c,
			'BAST Reconciled Amount Should Be -100'
		);

		Assert.areEqual(
			0,
			updatedBAST.Reconciliation_Difference__c,
			'BAST Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			updatedBAST.Id,
			updatedRepayment.Bank_Account_Statement_Transaction__c,
			'Reayment should be linked to the transaction'
		);

		Assert.areEqual(
			0,
			updatedRepayment.Reconciliation_Difference__c,
			'Repayment Reconciliation Difference Should Be 0'
		);

		Assert.areEqual(
			-50,
			updatedRepayment.Reconciled_Amount__c,
			'Repayment Reconciled Amount Should Be -50'
		);

		Assert.areEqual(
			updatedBAST.Id,
			createdCase.Bank_Account_Statement_Transaction__c,
			'Case should be linked to the transaction'
		);

		Assert.areEqual(
			-50,
			createdCase.Error_Amount__c,
			'Case Error Amount Should Be -50'
		);
	}

	@isTest
	static void runAusDCReturnRecon_ShouldMatchRepaymentToBankTransaction() {
		// Given
		Bond__c testBond = [SELECT Id FROM Bond__c LIMIT 1];
		Bank_Account__c testBankAccount = [
			SELECT Id
			FROM Bank_Account__c
			LIMIT 1
		];

		Bank_Account_Statement__c testStatement = TestDataFactory.createBankAccountStatement(
			testBankAccount.Id,
			'123',
			0
		);
		insert testStatement;

		Bank_Account_Statement_Transaction__c testBankTrans = TestDataFactory.createBankAccountStatementTransaction(
			testStatement.Id,
			100.00,
			0.00,
			'Test 123'
		);
		testBankTrans.Transaction_Header_Credit_Debit_Indic__c = 'DBIT';
		testBankTrans.Additional_Transaction_Information__c = 'DIRECT CREDIT RETURNED 230425 LODGEMENT REF ********** ANSWER 9';
		testBankTrans.End_To_End_Identification__c = '**********';
		insert testBankTrans;

		Repayment__c testRepayment = TestDataFactory.createRepayment(
			testBond.Id,
			Constants.REPAYMENT_METHOD_DIRECT_CREDIT,
			Constants.REPAYMENT_STATUS_SUCCESSFUL,
			100.00
		);
		testRepayment.Repayment_Bank_Reference__c = '**********';
		insert testRepayment;

		Bank_Account_Statement_Transaction__c bankTrans = [
			SELECT
				ID,
				Reconciled_Amount__c,
				Reconciliation_Difference__c,
				End_To_End_Identification__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testBankTrans.Id
			LIMIT 1
		];

		// When

		Test.startTest();

		RepaymentService.runAusDCReturnRecon(bankTrans);

		Test.stopTest();

		// Then

		Bank_Account_Statement_Transaction__c updatedBAST = [
			SELECT ID, Reconciled_Amount__c, Reconciliation_Difference__c
			FROM Bank_Account_Statement_Transaction__c
			WHERE Id = :testBankTrans.Id
			LIMIT 1
		];

		Repayment__c updatedRepayment = [
			SELECT
				Id,
				Reconciled_Amount__c,
				Reconciliation_Difference__c,
				Bank_Account_Statement_Transaction__c
			FROM Repayment__c
			WHERE ID = :testRepayment.Id
			LIMIT 1
		];

		Assert.areEqual(
			-100,
			updatedBAST.Reconciled_Amount__c,
			'BAST Reconciled Amount Should Be -100'
		);

		Assert.areEqual(
			0,
			updatedBAST.Reconciliation_Difference__c,
			'BAST Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			updatedBAST.Id,
			updatedRepayment.Bank_Account_Statement_Transaction__c,
			'Repayment should be linked to the transaction'
		);

		Assert.areEqual(
			0,
			updatedRepayment.Reconciliation_Difference__c,
			'Repayment Reconciliation Difference Should Be Zero'
		);

		Assert.areEqual(
			-100,
			updatedRepayment.Reconciled_Amount__c,
			'Repayment Reconciled Amount Should Be -100'
		);

		Payment__c newPayment = [
			SELECT Id
			FROM Payment__c
			WHERE Original_Repayment__c = :updatedRepayment.Id
			LIMIT 1
		];

		Assert.isNotNull(newPayment, 'Payment should be created');
	}
}