public without sharing class TransferRenterReviewController {
	public static final String ERROR_DEFAULT = Label.Common_Error_Message_For_System_Failures;
	public static final String ERROR_TC_REJECTED = Label.Renter_Review_Message_On_Renter_Review_If_TC_Rejected;
	public static final String ERROR_EMAIL_BOUNCED = Label.Renter_Review_Claim_Message_On_Submission_If_Email_Bounced;
	public static final String ERROR_RP_CANCELLED = Label.Renter_Review_Message_On_Renter_Review_If_RP_Cancelled;
	public static final String ERROR_PENDING_WITH_RP = Label.Renter_Review_Message_On_Bond_Details_If_Transaction_Status_Is_Pending_With_RP;
	public static final String ERROR_FINALISED = Label.Renter_Review_Transfer_Message_When_Transaction_Finalised_By_RP;
	public static final String ERROR_TIMED_OUT = Label.Renter_Review_Transfer_Message_When_Transaction_Is_Timed_Out;
	public static final String ERROR_INCOMING_RENTER_REMOVED = Label.Renter_Review_Transfer_Message_When_Incoming_Renter_Removed;
	public static final String ERROR_RENTER_AGREED = Label.Renter_Review_Transfer_Message_When_Renter_Already_Agreed;
	public static final String ERROR_RP_RESUBMITTED = Label.Renter_Review_Transfer_Message_When_Rental_Provider_Resubmitted;
	private static final Datetime NOW = Datetime.now();
	private static final String CALLBACK_PAGE_TRANSFER_RENTER_REVIEW =
		RTBAOnlineHelper.siteBaseSecureUrl + '/transfer-renter-review';

	@AuraEnabled(cacheable=false)
	public static TransferWrapper getValidatedTransferDetails(
		String encryptedData
	) {
		if (String.isBlank(encryptedData)) {
			throw AuraHelper.throwToAura(ERROR_DEFAULT);
		}

		Auth.JWT decodedToken = TokenUtility.decodeNewOrLegacy(encryptedData);

		TransferWrapper transfer = getValidatedTransferDetails(
			Id.valueOf(
				(String) decodedToken.getAdditionalClaims()
					.get(TokenUtility.GENERIC_ID_KEY)
			)
		);
		transfer.idamInfoVerified = validateUserCanAccess(
			transfer.reviewer,
			decodedToken
		);
		return transfer;
	}

	@TestVisible
	private static TransferWrapper getValidatedTransferDetails(
		Id transactionPartyId
	) {
		Transaction_Party__c transferTransactionParty = getTransactionPartyDetails(
			transactionPartyId
		);
		validateIncomingPartyRemoved(transferTransactionParty);

		String transactionId = transferTransactionParty?.Transaction__c;

		Transaction__c transferTransaction = getTransactionDetails(
			transactionId
		);

		if (transferTransactionParty == null || transferTransaction == null) {
			return null;
		}

		validateTransferStatuses(transferTransaction);

		if (!pendingWithRenters(transferTransaction)) {
			return null;
		}

		return new TransferWrapper(
			transferTransaction,
			transferTransactionParty
		);
	}

	private static Boolean validateUserCanAccess(
		Transaction_Party__c tp,
		Auth.JWT decodedToken
	) {
		if (ServiceVictoriaService.isIdamDisabled()) {
			return true;
		}

		ServiceVictoriaService.UserInfoResponse serviceVicUser;
		try {
			serviceVicUser = ServiceVictoriaService.getUserFromRtbaToken(
				decodedToken
			);
		} catch (Exception e) {
			throw AuraHelper.throwToAura(
				'Failed to get user info from ServiceVic',
				e
			);
		}

		if (serviceVicUser == null) {
			return false;
		}

		if (tp?.Email_Address__c != serviceVicUser.email) {
			throw AuraHelper.throwToAura(
				Label.Common_Message_When_Service_Vic_Email_Does_Not_Match
			);
		}

		return true;
	}

	private static Transaction__c getTransactionDetails(Id transactionId) {
		Transaction__c transferTransaction = [
			SELECT
				Id,
				Name,
				Rented_Property_Address__c,
				Bond_Amount__c,
				Status__c,
				Sub_Status__c,
				Transaction_Expiry_Date_Time__c,
				Type__c,
				Revision_Number__c,
				RTBA_Registration_ID__c,
				RTBA_Registered_Name__c,
				Bond__c,
				Bond__r.Name,
				Bond__r.Current_Bond_Amount__c,
				Submission_Date__c,
				Date_Of_Transfer__c,
				Review_In_Progress__c,
				Additional_Address_Details__c,
				Street_Address__c,
				Suburb__c,
				Postcode__c,
				Bond_Paid_By__c,
				Number_Of_Bedrooms__c,
				Premises_Type__c,
				Premises_Type_Other__c,
				Tenancy_Period_months__c,
				Tenancy_Start_Date__c,
				Tenancy_Type__c,
				Weekly_Rental_Amount__c,
				Party_Name_Requesting_Change__c,
				Rental_Provider__c,
				Rental_Provider__r.Name,
				Rental_Provider__r.RTBA_Registered_Name__c,
				(
					SELECT
						Id,
						Name,
						Party_Status__c,
						Party_Status_Date__c,
						First_Name__c,
						Last_Name__c,
						Company_Name__c,
						Date_of_Birth__c,
						Mobile_Number__c,
						Email_Address__c,
						Renter_position__c,
						Renter_Type__c,
						Role__c,
						Party_Name__c,
						Rental_Provider__c,
						Renter__c,
						Renter__r.AccountId,
						Rental_Provider_Type__c,
						Rental_Provider__r.Name,
						Rental_Provider__r.RTBA_Registered_Name__c,
						Rental_Provider__r.Transaction_notices__c
					FROM Transaction_Parties__r
					WHERE
						Party_Status__c != :Constants.TRANSACTION_PARTY_STATUS_REMOVED
					ORDER BY
						Role__c DESC,
						Last_Name__c NULLS FIRST,
						First_Name__c
				),
				(
					SELECT
						Id,
						Comments__c,
						Transaction_Party__c,
						Submission_Date__c,
						Active_Review__c
					FROM Transaction_Reviews__r
					WHERE Active_Review__c = TRUE
				)
			FROM Transaction__c
			WHERE
				RecordTypeId = :Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER
				AND Id = :transactionId
		];

		return transferTransaction;
	}

	private static Transaction_Party__c getTransactionPartyDetails(
		Id transactionPartyId
	) {
		return [
			SELECT
				Id,
				Name,
				Transaction__c,
				Party_Status__c,
				First_Name__c,
				Last_Name__c,
				Email_Address__c,
				Company_Name__c,
				Party_Name__c,
				Renter_position__c,
				Transaction_Status__c
			FROM Transaction_Party__c
			WHERE Id = :transactionPartyId
		];
	}

	@TestVisible
	private static void validateTransferStatuses(
		Transaction__c transactionRecord
	) {
		validateFinalised(transactionRecord, ERROR_FINALISED);
		validateTimedOut(transactionRecord, ERROR_TIMED_OUT);
		validateRenterRequestedChange(transactionRecord);
		validateRPCancelled(transactionRecord, ERROR_RP_CANCELLED);
		validateTCRejected(transactionRecord, ERROR_TC_REJECTED);
		validateEmailBounced(transactionRecord, ERROR_EMAIL_BOUNCED);
		validatePendingWithRP(transactionRecord);
	}

	public static void validateTCRejected(
		Transaction__c transactionRecord,
		String errorMessage
	) {
		Boolean hasTxStatus = hasMatchingTxStatus(
			transactionRecord,
			Constants.TRANSACTION_STATUS_CANCELLED,
			Constants.TRANSACTION_SUB_STATUS_TC_REJECTED
		);
		Boolean hasTpStatus = hasMatchingTpStatus(
			transactionRecord,
			'',
			Constants.TRANSACTION_PARTY_STATUS_TC_REJECTED
		);

		if (hasTxStatus || hasTpStatus) {
			throw AuraHelper.throwToAura(errorMessage);
		}
	}

	public static void validateEmailBounced(
		Transaction__c transactionRecord,
		String errorMessage
	) {
		Boolean hasTxStatus = hasMatchingTxStatus(
			transactionRecord,
			Constants.TRANSACTION_STATUS_CANCELLED,
			Constants.TRANSACTION_SUB_STATUS_EMAIL_BOUNCED
		);
		Boolean hasTpStatus = hasMatchingTpStatus(
			transactionRecord,
			'',
			Constants.TRANSACTION_PARTY_STATUS_EMAIL_BOUNCED
		);

		if (hasTxStatus || hasTpStatus) {
			throw AuraHelper.throwToAura(errorMessage);
		}
	}

	public static void validateRPCancelled(
		Transaction__c transactionRecord,
		String errorMessage
	) {
		Boolean hasTxStatus = hasMatchingTxStatus(
			transactionRecord,
			Constants.TRANSACTION_STATUS_CANCELLED,
			Constants.TRANSACTION_SUB_STATUS_RENTAL_PROVIDER_CANCELLED
		);
		Boolean hasTpStatus = hasMatchingTpStatus(
			transactionRecord,
			Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER,
			Constants.TRANSACTION_PARTY_STATUS_RP_CANCELLED
		);

		if (hasTxStatus || hasTpStatus) {
			throw AuraHelper.throwToAura(errorMessage);
		}
	}

	public static void validateFinalised(
		Transaction__c transactionRecord,
		String errorMessage
	) {
		if (
			hasMatchingTxStatus(
				transactionRecord,
				Constants.TRANSACTION_STATUS_FINALISED,
				''
			)
		) {
			throw AuraHelper.throwToAura(errorMessage);
		}
	}

	public static void validateTimedOut(
		Transaction__c transactionRecord,
		String errorMessage
	) {
		if (
			hasMatchingTxStatus(
				transactionRecord,
				Constants.TRANSACTION_STATUS_TIMEDOUT,
				''
			)
		) {
			throw AuraHelper.throwToAura(errorMessage);
		}
	}

	private static void validatePendingWithRP(
		Transaction__c transactionRecord
	) {
		if (
			hasMatchingTxStatus(
				transactionRecord,
				Constants.TRANSACTION_STATUS_PENDING_WITH_RP,
				''
			)
		) {
			throw AuraHelper.throwToAura(ERROR_PENDING_WITH_RP);
		}
	}

	private static void validateRenterRequestedChange(
		Transaction__c transactionRecord
	) {
		Boolean hasTxStatus = hasMatchingTxStatus(
			transactionRecord,
			Constants.TRANSACTION_STATUS_PENDING_WITH_RP,
			Constants.TRANSACTION_SUB_STATUS_RENTER_CHANGE
		);
		Boolean hasTpStatus = hasMatchingTpStatus(
			transactionRecord,
			Constants.TRANSACTION_PARTY_ROLE_RENTER,
			Constants.TRANSACTION_PARTY_STATUS_CHANGE_REQUESTED
		);

		if (
			hasTxStatus ||
			hasTpStatus ||
			transactionRecord.Review_In_Progress__c
		) {
			throw AuraHelper.throwToAura(ERROR_PENDING_WITH_RP);
		}
	}

	private static void validateIncomingPartyRemoved(
		Transaction_Party__c transactionParty
	) {
		if (
			transactionParty.Transaction_Status__c ==
			Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS &&
			transactionParty.Renter_position__c ==
			Constants.TRANSACTION_PARTY_RENTER_POSITION_INCOMING &&
			transactionParty.Party_Status__c ==
			Constants.TRANSACTION_PARTY_STATUS_REMOVED
		) {
			throw AuraHelper.throwToAura(ERROR_INCOMING_RENTER_REMOVED);
		}
	}

	private static Boolean pendingWithRenters(
		Transaction__c transactionRecord
	) {
		return hasMatchingTxStatus(
			transactionRecord,
			Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS,
			''
		);
	}

	private static Boolean hasMatchingTxStatus(
		Transaction__c transactionRecord,
		String txStatus,
		String txSubStatus
	) {
		return transactionRecord?.Status__c == txStatus &&
			(String.isBlank(txSubStatus) ||
			transactionRecord?.Sub_Status__c == txSubStatus);
	}

	private static Boolean hasMatchingTpStatus(
		Transaction__c transactionRecord,
		String partyRole,
		String partyStatus
	) {
		for (
			Transaction_Party__c party : transactionRecord.Transaction_Parties__r
		) {
			if (
				(String.isBlank(partyRole) || party.Role__c == partyRole) &&
				party.Party_Status__c == partyStatus
			) {
				return true;
			}
		}
		return false;
	}

	public static void validateRevisionNumber(
		Transaction__c transactionRecord,
		Integer revisonNumber,
		String errorMessage
	) {
		if (transactionRecord?.Revision_Number__c != revisonNumber) {
			throw AuraHelper.throwToAura(errorMessage);
		}
	}

	private static Transaction_Party__c getRelatedRentalProviderParty(
		Transaction__c transactionRecord
	) {
		for (
			Transaction_Party__c party : transactionRecord.Transaction_Parties__r
		) {
			if (
				party.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				return party;
			}
		}
		return null;
	}

	@AuraEnabled(cacheable=false)
	public static void rejectTermsOfUse(
		Id rejectedById,
		Integer revisionNumber,
		String accessToken
	) {
		TransferWrapper transfer = getValidatedTransferDetails(rejectedById);

		if (
			!validateUserCanAccess(
				transfer.reviewer,
				TokenUtility.decodeTokenAura(accessToken)
			)
		) {
			throw AuraHelper.throwToAura(
				'You are not authorized to perform this action'
			);
		}
		validateRevisionNumber(
			transfer.details,
			revisionNumber,
			ERROR_RP_RESUBMITTED
		);

		List<Transaction_Party__c> transferParties = new List<Transaction_Party__c>();
		for (
			Transaction_Party__c tp : transfer.details.Transaction_Parties__r
		) {
			String tpStatus = (tp.Id == transfer.reviewer.Id)
				? Constants.TRANSACTION_PARTY_STATUS_TC_REJECTED
				: Constants.TRANSACTION_PARTY_STATUS_CANCELLED;
			transferParties.add(
				new Transaction_Party__c(
					Id = tp.Id,
					Party_Status__c = tpStatus,
					Party_Status_Date__c = NOW,
					Initiate_marketing_comms__c = true
				)
			);
		}

		Savepoint sp = Database.setSavepoint();
		try {
			update new Transaction__c(
				Id = transfer.details.Id,
				Status__c = Constants.TRANSACTION_STATUS_CANCELLED,
				Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_TC_REJECTED,
				Declined_Renter_Name__c = transfer.reviewer.Party_Name__c
			);
			update transferParties;
			handleRejectTermsOfUseJourneys(transfer.details);
		} catch (Exception e) {
			Database.rollback(sp);
			throw AuraHelper.throwToAura(ERROR_DEFAULT, e);
		}
	}

	private static void handleRejectTermsOfUseJourneys(Transaction__c rtTx) {
		//RTransfer - By Agree- RNotif-Tx Can Email after declining Terms of Use-3699
		MCJourneyInvocable.MCRequest requestForRenters = new MCJourneyInvocable.MCRequest();
		requestForRenters.recordId = rtTx.Id;
		requestForRenters.journeyName = Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_EMAIL_NOTIFICATIONS_TO_RENTERS;
		requestForRenters.targetRole = Constants.TRANSACTION_PARTY_ROLE_RENTER;
		requestForRenters.statusCheck = Constants.TRANSACTION_STATUS_CANCELLED;
		requestForRenters.subStatusCheck = Constants.TRANSACTION_SUB_STATUS_TC_REJECTED;
		requestForRenters.forceRequery = true;
		MCJourneyInvocable.fireJourneyEntry(requestForRenters);

		//RTransfer-Transaction-cancelled-after-declining-Terms-of-Use-Email-to-RP-4843
		MCJourneyInvocable.MCRequest requestForRP = new MCJourneyInvocable.MCRequest();
		requestForRP.recordId = rtTx.Id;
		requestForRP.journeyName = Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_EMAIL_NOTIFICATIONS_TO_RP;
		requestForRP.targetRole = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		requestForRP.statusCheck = Constants.TRANSACTION_STATUS_CANCELLED;
		requestForRP.subStatusCheck = Constants.TRANSACTION_SUB_STATUS_TC_REJECTED;
		requestForRP.forceRequery = true;
		MCJourneyInvocable.fireJourneyEntry(requestForRP);

		for (Transaction_Party__c tp : rtTx.Transaction_Parties__r) {
			if (
				tp.Role__c == Constants.TRANSACTION_PARTY_ROLE_RENTER &&
				String.isNotBlank(tp.Mobile_Number__c)
			) {
				MCJourneyController.fireJourneyEntry(
					Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_SMS_NOTIFICATIONS_TO_RENTERS,
					tp.Renter__c,
					new List<Id>{ rtTx.Id, tp.Id, rtTx.Bond__c },
					tp.Id
				);
			}
		}
	}

	@AuraEnabled(cacheable=false)
	public static void requestChanges(RequestChangeWrapper changes) {
		if (changes == null) {
			throw AuraHelper.throwToAura('No details provided');
		}

		TransferWrapper transfer = getValidatedTransferDetails(
			changes.requestedById
		);

		if (
			!validateUserCanAccess(
				transfer.reviewer,
				TokenUtility.decodeTokenAura(changes.accessToken)
			)
		) {
			throw AuraHelper.throwToAura(
				'You are not authorized to perform this action'
			);
		}

		validateRevisionNumber(
			transfer.details,
			changes.revisionNumber,
			ERROR_RP_RESUBMITTED
		);

		for (
			Transaction_Party__c tp : transfer.details.Transaction_Parties__r
		) {
			String tpStatus = (tp.Id == transfer.reviewer.Id)
				? Constants.TRANSACTION_PARTY_STATUS_CHANGE_REQUESTED
				: Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED;

			tp.Party_Status__c = tpStatus;
			tp.Party_Status_Date__c = NOW;
			tp.Initiate_marketing_comms__c = true;
		}

		//Generate transaction review upserts list, previous transaction review should mark as false
		List<Transaction_Review__c> trUpsertList = new List<Transaction_Review__c>();
		for (
			Transaction_Review__c tr : transfer.details.Transaction_Reviews__r
		) {
			trUpsertList.add(
				new Transaction_Review__c(Id = tr.Id, Active_Review__c = false)
			);
		}

		Savepoint sp = Database.setSavepoint();
		try {
			//update transaction records
			update new Transaction__c(
				Id = transfer.details.Id,
				Comments__c = changes.comments,
				Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RP,
				Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RENTER_CHANGE,
				Review_In_Progress__c = true,
				Party_Name_Requesting_Change__c = transfer.reviewer.Party_Name__c,
				Submission_Date__c = NOW
			);

			//update transaction parties records
			update transfer.details.Transaction_Parties__r;

			//Update and insert transaction review records
			trUpsertList.add(
				createTransactionReview(
					transfer.details,
					transfer.reviewer,
					changes.comments
				)
			);
			upsert trUpsertList;

			handleRequestChangesJourneys(transfer.details);
		} catch (Exception e) {
			Database.rollback(sp);
			throw AuraHelper.throwToAura(ERROR_DEFAULT, e);
		}
	}

	private static void handleRequestChangesJourneys(Transaction__c rtTx) {
		//RTransfer-Change-Request-Renter-5072
		MCJourneyInvocable.MCRequest requestForRenters = new MCJourneyInvocable.MCRequest();
		requestForRenters.recordId = rtTx.Id;
		requestForRenters.journeyName = Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_EMAIL_NOTIFICATIONS_TO_RENTERS;
		requestForRenters.targetRole = Constants.TRANSACTION_PARTY_ROLE_RENTER;
		requestForRenters.statusCheck = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		requestForRenters.subStatusCheck = Constants.TRANSACTION_SUB_STATUS_RENTER_CHANGE;
		requestForRenters.forceRequery = true;
		MCJourneyInvocable.fireJourneyEntry(requestForRenters);

		//RenterTransfer-RenterReview-ChangeRequestSubmitted-EmailtoRP-3511
		MCJourneyInvocable.MCRequest requestForRP = new MCJourneyInvocable.MCRequest();
		requestForRP.recordId = rtTx.Id;
		requestForRP.journeyName = Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_EMAIL_NOTIFICATIONS_TO_RP;
		requestForRP.targetRole = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		requestForRP.statusCheck = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		requestForRP.subStatusCheck = Constants.TRANSACTION_SUB_STATUS_RENTER_CHANGE;
		requestForRP.forceRequery = true;
		MCJourneyInvocable.fireJourneyEntry(requestForRP);

		for (
			Transaction_Party__c transactionParty : rtTx.Transaction_Parties__r
		) {
			if (
				transactionParty.Role__c !=
				Constants.TRANSACTION_PARTY_ROLE_RENTER
			) {
				continue;
			}
			MCJourneyController.fireJourneyEntry(
				Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_SMS_NOTIFICATIONS_TO_RENTERS,
				transactionParty.Renter__c,
				new List<Id>{ rtTx.Id, transactionParty.Id },
				transactionParty.Id
			);
		}
	}

	public static Transaction_Review__c createTransactionReview(
		Transaction__c tx,
		Transaction_Party__c tp,
		String comments
	) {
		Transaction_Review__c tr = new Transaction_Review__c();
		tr.Transaction__c = tx.Id;
		tr.Transaction_Party__c = tp.Id;
		tr.Active_Review__c = true;
		tr.Other_Active_Review__c = false;
		tr.Revision__c = Constants.TRANSACTION_REVIEW_REVISION_INITIAL_SUBMISSION;
		tr.Submission_Date__c = NOW;
		tr.Comments__c = comments;
		tr.Reviewed_By_Transaction_Party__c = getRelatedRentalProviderParty(tx)
			.Id;

		tr.Additional_Address_Details__c = tx.Additional_Address_Details__c;
		tr.Street_Address__c = tx.Street_Address__c;
		tr.Suburb__c = tx.Suburb__c;
		tr.Postcode__c = tx.Postcode__c;

		tr.Bond_Paid_By__c = tx.Bond_Paid_By__c;
		tr.Bond_Amount__c = tx.Bond_Amount__c;
		tr.Number_Of_Bedrooms__c = tx.Number_Of_Bedrooms__c;
		tr.Premises_Type__c = tx.Premises_Type__c;
		tr.Premises_Type_Other__c = tx.Premises_Type_Other__c;
		tr.Tenancy_Period_months__c = tx.Tenancy_Period_months__c;
		tr.Tenancy_Start_Date__c = tx.Tenancy_Start_Date__c;
		tr.Tenancy_Type__c = tx.Tenancy_Type__c;
		tr.Weekly_Rental_Amount__c = tx.Weekly_Rental_Amount__c;

		Integer index = 1;
		for (Transaction_Party__c party : tx.Transaction_Parties__r) {
			if (
				party.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				continue;
			}

			String suffix = '__c';
			if (index > 1) {
				suffix = '_' + index + '__c';
			}
			String firstNameField = 'First_Name' + suffix;
			String lastNameField = 'Last_Name' + suffix;
			String companyNameField = 'Company_Name' + suffix;
			String renterTypeField = 'Renter_Type' + suffix;
			String dateOfBirthField = 'Date_of_Birth' + suffix;
			String mobileNumberField = 'Mobile_Number' + suffix;
			String emailAddressField = 'Email_Address' + suffix;

			if (
				party.Renter_Type__c ==
				Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
			) {
				tr.put(firstNameField, party.First_Name__c);
				tr.put(lastNameField, party.Last_Name__c);
				tr.put(dateOfBirthField, party.Date_of_Birth__c);
			} else {
				tr.put(companyNameField, party.Company_Name__c);
			}

			tr.put(renterTypeField, party.Renter_Type__c);
			tr.put(
				mobileNumberField,
				party.Mobile_Number__c != null ? party.Mobile_Number__c : ''
			);
			tr.put(
				emailAddressField,
				party.Email_Address__c != null ? party.Email_Address__c : ''
			);
			index++;
		}

		return tr;
	}

	@AuraEnabled(cacheable=false)
	public static void acceptTransfer(
		Id transactionPartyId,
		Integer revisionNumber,
		String accessToken
	) {
		TransferWrapper transfer = getValidatedTransferDetails(
			transactionPartyId
		);

		if (
			!validateUserCanAccess(
				transfer.reviewer,
				TokenUtility.decodeTokenAura(accessToken)
			)
		) {
			throw AuraHelper.throwToAura(
				'You are not authorized to perform this action'
			);
		}

		validateRevisionNumber(
			transfer.details,
			revisionNumber,
			ERROR_RP_RESUBMITTED
		);

		Savepoint sp = Database.setSavepoint();
		try {
			Transaction_Party__c agreedTransactionParty = new Transaction_Party__c(
				Id = transfer.reviewer.Id,
				Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED,
				Party_Status_Date__c = NOW
			);
			update agreedTransactionParty;

			if (hasAllPartiesAccepted(transfer.details.Id)) {
				finaliseTransfer(transfer.details.Id);
			}
		} catch (Exception e) {
			Database.rollback(sp);
			throw AuraHelper.throwToAura(ERROR_DEFAULT, e);
		}
	}

	@TestVisible
	private static Boolean hasAllPartiesAccepted(Id transactionId) {
		Integer countPartiesNotAccepted = [
			SELECT COUNT()
			FROM Transaction_Party__c
			WHERE
				Transaction__c = :transactionId
				AND Party_Status__c != :Constants.TRANSACTION_PARTY_STATUS_REMOVED
				AND Party_Status__c != :Constants.TRANSACTION_PARTY_STATUS_AGREED
		];
		return countPartiesNotAccepted <= 0;
	}

	public static void finaliseTransfer(Id transactionId) {
		//Update Transaction
		Transaction__c revisedTransaction = new Transaction__c(
			Id = transactionId,
			Status__c = Constants.TRANSACTION_STATUS_FINALISED,
			Transaction_Completion_Date__c = NOW
		);

		update revisedTransaction;

		//Upsert Bond Parties
		Transaction__c transactionRecord = getTransactionDetails(transactionId);
		List<Bond__c> bondList = new BondsSelector()
			.selectByIdWithTransactionsWithoutSharing(
				new Set<Id>{ transactionRecord.Bond__c }
			);

		Bond__c bondRecord = bondList[0];

		List<Bond_Party__c> revisedBondParties = createBondParties(
			transactionRecord,
			bondRecord
		);

		upsert revisedBondParties;
		UpdateRenterNameInvocable.updateBondRenterNames(
			new Set<Id>{ bondRecord.Id }
		);
		handleFinalisedTransferJourneys(transactionRecord);
	}

	private static void handleFinalisedTransferJourneys(
		Transaction__c transactionRecord
	) {
		if (
			transactionRecord.Status__c !=
			Constants.TRANSACTION_STATUS_FINALISED
		) {
			return;
		}

		//RTransfer-By-Agreement-Transfer-Receipt-to-Renter-5042
		MCJourneyInvocable.MCRequest requestForRenters = new MCJourneyInvocable.MCRequest();
		requestForRenters.transRecord = transactionRecord;
		requestForRenters.parties = transactionRecord.Transaction_Parties__r;
		requestForRenters.journeyName = Constants.MC_JOURNEY_NAME_RTRANSFER_BY_AGREEMENT_TRANSFER_RECEIPT_TO_RENTERS;
		requestForRenters.targetRole = Constants.TRANSACTION_PARTY_ROLE_RENTER;
		requestForRenters.targetRenterPositions = new List<String>{
			Constants.TRANSACTION_PARTY_RENTER_POSITION_STAYING,
			Constants.TRANSACTION_PARTY_RENTER_POSITION_INCOMING
		};
		MCJourneyInvocable.fireJourneyEntry(requestForRenters);

		//RTransfer-By-Agreement-Transfer-Receipt-to-RP-5044
		MCJourneyInvocable.MCRequest requestForRP = new MCJourneyInvocable.MCRequest();
		requestForRP.transRecord = transactionRecord;
		requestForRP.parties = transactionRecord.Transaction_Parties__r;
		requestForRP.journeyName = Constants.MC_JOURNEY_NAME_RTRANSFER_BY_AGREEMENT_TRANSFER_RECEIPT_TO_RP;
		requestForRP.targetRole = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		MCJourneyInvocable.fireJourneyEntry(requestForRP);

		for (
			Transaction_Party__c tp : transactionRecord.Transaction_Parties__r
		) {
			if (
				tp.Role__c == Constants.TRANSACTION_PARTY_ROLE_RENTER &&
				String.isNotBlank(tp.Mobile_Number__c)
			) {
				MCJourneyController.fireJourneyEntry(
					Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_ACCEPTED_SMS_TO_RENTERS,
					tp.Renter__c,
					new List<Id>{
						transactionRecord.Id,
						tp.Id,
						transactionRecord.Bond__c
					},
					tp.Id
				);
			}
		}
	}

	private static List<Bond_Party__c> createBondParties(
		Transaction__c transactionRecord,
		Bond__c bondRecord
	) {
		List<Transaction_Party__c> transactionParties = transactionRecord.Transaction_Parties__r;
		List<Bond_Party__c> bondParties = bondRecord.Bond_Parties__r;
		List<Bond_Party__c> revisedBondParties = new List<Bond_Party__c>();

		for (Transaction_Party__c tp : transactionParties) {
			if (
				tp.Role__c == Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				continue;
			}

			if (
				tp.Renter_position__c ==
				Constants.TRANSACTION_PARTY_RENTER_POSITION_INCOMING
			) {
				Bond_Party__c incomingParty = BondsUtility.createBondParty(
					bondRecord,
					tp
				);
				incomingParty.Status__c = Constants.BOND_PARTY_STATUS_ACTIVE;
				revisedBondParties.add(incomingParty);
				continue;
			}

			Bond_Party__c existingParty = createBondParty(bondParties, tp);
			revisedBondParties.add(existingParty);
		}

		return revisedBondParties;
	}

	private static Bond_Party__c createBondParty(
		List<Bond_Party__c> bondParties,
		Transaction_Party__c tp
	) {
		Bond_Party__c existingParty;
		for (Bond_Party__c bp : bondParties) {
			if (bp.Role__c == Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER) {
				continue;
			}

			if (!isPartyMatched(bp, tp)) {
				continue;
			}

			if (
				tp.Renter_position__c ==
				Constants.TRANSACTION_PARTY_RENTER_POSITION_LEAVING
			) {
				bp.Status__c = Constants.BOND_PARTY_STATUS_INACTIVE;
				bp.Outgoing_Closed_Date__c = System.today();
				existingParty = bp;
				break;
			}

			if (
				tp.Renter_position__c ==
				Constants.TRANSACTION_PARTY_RENTER_POSITION_STAYING
			) {
				bp.Status__c = Constants.BOND_PARTY_STATUS_ACTIVE;
				existingParty = bp;
				break;
			}
		}
		if (existingParty == null) {
			throw new NoSuchElementException(
				'Existing bond party not found for TP ' + tp.Id
			);
		}
		return existingParty;
	}

	private static Boolean isPartyMatched(
		Bond_Party__c bp,
		Transaction_Party__c tp
	) {
		if (
			bp.Role__c == Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER ||
			tp.Role__c == Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
		) {
			return false;
		}

		if (bp.Renter_Type__c == Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL) {
			return bp.Renter_Type__c == tp.Renter_Type__c &&
				bp.First_Name__c == tp.First_Name__c &&
				bp.Family_Name__c == tp.Last_Name__c;
		}

		if (bp.Renter_Type__c == Constants.BOND_PARTY_RENTER_TYPE_COMPANY) {
			return bp.Renter_Type__c == tp.Renter_Type__c &&
				bp.Company_Name__c == tp.Company_Name__c;
		}

		return false;
	}

	public class TransferWrapper {
		@AuraEnabled
		public Transaction__c details { get; private set; }
		@AuraEnabled
		public Transaction_Party__c reviewer { get; private set; }
		@AuraEnabled
		public Boolean idamInfoVerified { get; set; }

		public TransferWrapper(
			Transaction__c transactionRecord,
			Transaction_Party__c transactionPartyRecord
		) {
			this.details = transactionRecord;
			this.reviewer = transactionPartyRecord;
		}
	}

	public class RequestChangeWrapper {
		@AuraEnabled
		public Id requestedById { get; set; }
		@AuraEnabled
		public String comments { get; set; }
		@AuraEnabled
		public Integer revisionNumber { get; set; }
		@AuraEnabled
		public String accessToken { get; set; }
	}
}