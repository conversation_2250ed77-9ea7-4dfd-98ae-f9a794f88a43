<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Claim_Renter_Initiated_Homes_Victoria_UnRegistered_RP_Trigger</name>
        <label>Claim - Renter Initiated - Homes Victoria - UnRegistered RP Trigger</label>
        <locationX>3042</locationX>
        <locationY>755</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Also_Generate_Postal_Notification</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <stringValue>RIC-HVBond-EmailBouncedUnregisteredRP-SMS-7728</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetPartyIds</name>
            <value>
                <elementReference>Triggering_TP_Id_as_List</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Claim_Renter_Initiated_WOConsent_Private_Unreg_RP_Email_Noti_6939_Trigger</name>
        <label>Claim - Renter Initiated - WOConsent Private-Unreg RP Email Noti-6939 Trigger</label>
        <locationX>3306</locationX>
        <locationY>755</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Also_Generate_Postal_Notification</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <stringValue>RIC-PrivateBond-WithoutConsent-EmalilBouncedUnregisteredRP-SMS-7679</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetPartyIds</name>
            <value>
                <elementReference>Triggering_TP_Id_as_List</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Fire_SMS_Journey_Claim_Renter_Initiated_HV_Bond_Initiator_Renter</name>
        <label>Fire SMS Journey Claim - Renter Initiated - HV- Bond Initiator - (Renter)</label>
        <locationX>4538</locationX>
        <locationY>647</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Generate_postal_notification_PDF</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <stringValue>RIC-HVBond-WithoutConsent-EmailBouncedRenterSMS-9390</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recordId</name>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetPartyIds</name>
            <value>
                <elementReference>Triggering_TP_Id_as_List</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Generate_postal_notification_PDF</name>
        <label>Generate postal notification PDF</label>
        <locationX>2492</locationX>
        <locationY>1331</locationY>
        <actionName>PdfButlerController</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailTitle</name>
            <value>
                <elementReference>$Record.Name</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transactionId</name>
            <value>
                <elementReference>$Record.Transaction__r.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transactionPartyId</name>
            <value>
                <elementReference>$Record.Transaction_Party__r.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>PdfButlerController</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Triggering_TP_Id_as_List</name>
        <label>Assign Triggering TP Id as List</label>
        <locationX>2492</locationX>
        <locationY>323</locationY>
        <assignmentItems>
            <assignToReference>Triggering_TP_Id_as_List</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Which_Email</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Also_Generate_Postal_Notification</name>
        <label>Also generate postal notification?</label>
        <locationX>3306</locationX>
        <locationY>947</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_postal_notification</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Initiated - Without Consent Private Bond -Renter Email Noti-4666</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-HVBond-Unregistered-RPEmail-16515AC2</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated - Homes Victoria - UnRegistered RP Email Notification</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated - Homes Victoria - Registered RP Email Notification</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC- Without Consent-Private-UnregRP-EmailNotification-6939</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated -Without Consent Private Bond-Regi RPEmail Noti-4665</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Generate_postal_notification_PDF</targetReference>
            </connector>
            <label>Yes - postal notification</label>
        </rules>
    </decisions>
    <decisions>
        <name>BC_Renter_review_Transaction_status</name>
        <label>Transaction status</label>
        <locationX>1942</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Generate_postal_notification_PDF</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>BC_Renter_review_Pending_with_renters</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending with Renters</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Update_bounced_TP</targetReference>
            </connector>
            <label>Pending with renters</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_cancelled_transaction_comms</name>
        <label>Check cancelled transaction comms</label>
        <locationX>2382</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>Email_Bounce_Subflow_Trigger_Journey_by_IER_Record</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Needs_Initiate_Comms</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BL - Initiated - Renter Acceptance Email-416</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BL - Renter review - Renter reminder-221 AC1</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Update_non_bounced_parties</targetReference>
            </connector>
            <label>Needs Initiate Comms</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_comms_after_extend_expiry</name>
        <label>Check comms after extend expiry</label>
        <locationX>3306</locationX>
        <locationY>647</locationY>
        <defaultConnector>
            <targetReference>Also_Generate_Postal_Notification</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Claim_Renter_Initiated_Homes_Victoria_UnRegistered_RP</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated - Homes Victoria - UnRegistered RP Email Notification</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-HVBond-Unregistered-RPEmail-16515AC2</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Claim_Renter_Initiated_Homes_Victoria_UnRegistered_RP_Trigger</targetReference>
            </connector>
            <label>Claim - Renter Initiated - Homes Victoria - UnRegistered RP</label>
        </rules>
        <rules>
            <name>Claim_Renter_Initiated_Without_Consent_PrivateBond_Unreg_RP_Email_Noti_6939</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC- Without Consent-Private-UnregRP-EmailNotification-6939</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Claim_Renter_Initiated_WOConsent_Private_Unreg_RP_Email_Noti_6939_Trigger</targetReference>
            </connector>
            <label>Claim - Renter Initiated - Without Consent PrivateBond-Unreg RP Email Noti-6939</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Transaction_Status_is_Finalised</name>
        <label>Check if Transaction Status is Finalised</label>
        <locationX>1238</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Transaction_Finalised</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Finalised</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Valid_VCAT_Order_Number__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claims</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Funding_Source__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Private</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Repayment_Basis__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VCAT Order</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Bond__r.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closed</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_TP_status_to_Email_Bounced</targetReference>
            </connector>
            <label>Transaction Finalised</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_Transaction_status</name>
        <label>Transaction status</label>
        <locationX>710</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Pending_with_renters_private_renter_agreement</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending with Renters</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Funding_Source__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Private</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Repayment_Basis__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Agreement</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claims</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Update_bounced_TP</targetReference>
            </connector>
            <label>Pending with renters private renter agreement</label>
        </rules>
    </decisions>
    <decisions>
        <name>Transaction_status_bc_resubmit</name>
        <label>Check update criteria</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Resubmission_Pending_with_renters_private_rp_initiated</name>
            <conditionLogic>1 AND 2 AND 3 AND 4 AND 5 AND (6 OR 7)</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Pending with Renters</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Funding_Source__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Private</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Repayment_Basis__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Agreement</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Role_Initiating_Claim__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rental Provider</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Type__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claims</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Sub_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Resubmission</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Transaction__r.Sub_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Resubmission - No Change</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_bounced_TP_bc_resubmission</targetReference>
            </connector>
            <label>Resubmission Pending with renters (private, rp initiated, renter agreement)</label>
        </rules>
    </decisions>
    <decisions>
        <name>Which_Email</name>
        <label>Which Email?</label>
        <locationX>2492</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>BC_Resubmission_of_edited_claim_renter_email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BC - Resubmission of edited claim renter email</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transaction_status_bc_resubmit</targetReference>
            </connector>
            <label>BC - Resubmission of edited claim renter email</label>
        </rules>
        <rules>
            <name>BC_First_renter_email_of_claim_with_agreement</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BC - First renter email of claim with agreement</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Transaction_status</targetReference>
            </connector>
            <label>BC - First renter email of claim with agreement</label>
        </rules>
        <rules>
            <name>BC_VCAT_Retained_Bond_Payment_Renter_Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BC - VCAT Retained Bond Payment - Renter Email</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_if_Transaction_Status_is_Finalised</targetReference>
            </connector>
            <label>BC - VCAT Retained Bond Payment - Renter Email</label>
        </rules>
        <rules>
            <name>Postal_notification</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-Private-Validated VCAT Order-Accepted Other Renter Email-8180-AC1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC - Finalised - Unreg. RP</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC - Retained repayment - Unreg. RP</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Generate_postal_notification_PDF</targetReference>
            </connector>
            <label>Postal notification</label>
        </rules>
        <rules>
            <name>BC_Renter_review_Renter_reminder</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BC - Renter review - Renter reminder</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>BC_Renter_review_Transaction_status</targetReference>
            </connector>
            <label>BC - Renter review - Renter reminder</label>
        </rules>
        <rules>
            <name>Cancel_trans_if_not_agreed</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claims - Resubmission unedited claim tx - Review 1st Email Notification(Renter)</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BL - Renter review - Renter reminder-221 AC1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BL - Initiated - Renter Acceptance Email-416</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RP Transfer-With Consent-First Email Notification to RP Transferee-3901</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTransfer-RP-Submitted-by-Renter-Agreement-Initial-EmailToRenters-3351</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTransfer-Review-Renter-Reminder_Timeout-3359</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTransfer-ResubCancelledTimedout RT byAgreement -RenterFirstEmailNoti -9321</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Cancel_trans_if_not_agreed_rp_resubmit</targetReference>
            </connector>
            <label>Cancel trans if not agreed</label>
        </rules>
        <rules>
            <name>Email_bounced_and_init_comms</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-Without Consent-All parties status Agreed-Renter Notification- 8436-AC2</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BC - Finalised Claim Renters not contactable - Renter Email</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-Without Consent-All parties Agreed-UnReg RP Notification-8424-AC1</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-Without Consent-All parties Agreed-UnReg RP Notification-8424-AC2</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_RIC_Retained_repayment_TP_status</targetReference>
            </connector>
            <label>Email bounced and init comms</label>
        </rules>
        <rules>
            <name>Extend_Expiry_Date_for_postal_notification</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated -Without Consent Private Bond-Regi RPEmail Noti-4665</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated - Without Consent PrivateBond-Unreg RP Email Noti-6939</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated - Homes Victoria - Registered RP Email Notification</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-HVBond-Unregistered-RPEmail-16515AC2</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated - Homes Victoria - UnRegistered RP Email Notification</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Renter Initiated - Without Consent Private Bond -Renter Email Noti-4666</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Claim - Renter Initiated -Without Consent Private Bond-Regi RPEmail Noti-4665</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC- Without Consent-Private-UnregRP-EmailNotification-6939</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Transaction_and_Reset_Expiry_Date</targetReference>
            </connector>
            <label>Extend Expiry Date for postal notification</label>
        </rules>
        <rules>
            <name>Set_email_bounced_status</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-Without Consent-Response Period Expired-Txn Cancelled-UnRegRP Email-9409</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTransfer-Edited-RenterAcceptance-5421</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTransfer-Change request rejected-RP Review Process-EmailToRenter-9314</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTransfer-Review-Renter-Reminder_Timeout-3359</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Transaction_and_TP_Updates_Email_Bounced_Status</targetReference>
            </connector>
            <label>Set email bounced status</label>
        </rules>
        <rules>
            <name>Set_email_bounced_status_Renter_Transfer_Cancel_Timeout</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RT - First Email to Renter Bounced - Txn Cancellation-Email to RP-9323</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RTransfer-RP-Submitted-Initial-EmailBounced-CancellationEmail-to-RP-9277</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Tx_and_TP_Updates_Email_Bounced_Status_For_Renter_Transfer_Cancel_Timeout</targetReference>
            </connector>
            <label>Set email bounced status Renter Transfer Cancel Timeout</label>
        </rules>
        <rules>
            <name>Cancel_trans_and_set_initiate_comms_to_true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BL edited - renter acceptance - Renter Further Review Email</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Reset_Initiate_comms_on_Rental_provider_transaction_party</targetReference>
            </connector>
            <label>Cancel trans and set initiate comms to true</label>
        </rules>
        <rules>
            <name>Claim_Renter_Initiated_HV_Bond_Initiator_Renter_Email_Noti_4669</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Name</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RIC-HVBond-RenterEmail-16513</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Mark_TP_bounced_substatus</targetReference>
            </connector>
            <label>Claim - Renter Initiated - HV- Bond Initiator - (Renter) Email Noti-4669</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <description>New Expiry Date (+20 days) for RIC when Email Bounce with Registered email address</description>
        <name>RIC_New_Expiry_Date</name>
        <dataType>DateTime</dataType>
        <expression>NOW() + 20</expression>
    </formulas>
    <interviewLabel>Hard Bounce - Individual Email Result {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Hard Bounce - Individual Email Result</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Copy_1_of_Update_bounced_TP</name>
        <label>Copy 1 of Update bounced TP</label>
        <locationX>578</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Copy_1_of_Update_transaction_to_cancelled</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_Update_non_bounced_parties</name>
        <label>Mark initiate comms for non bounce</label>
        <locationX>2250</locationX>
        <locationY>755</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Party_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Removed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Transaction__r.Transaction_Parties__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_Update_RP_and_Input_awaited_parties</name>
        <label>Copy 1 of Update RP and Input awaited parties</label>
        <locationX>578</locationX>
        <locationY>863</locationY>
        <filterLogic>1 OR (2 AND 3)</filterLogic>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </filters>
        <filters>
            <field>Party_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Input Awaited</stringValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Transaction__r.Transaction_Parties__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_Update_transaction_to_cancelled</name>
        <label>Copy 1 of Update transaction to cancelled</label>
        <locationX>578</locationX>
        <locationY>755</locationY>
        <connector>
            <targetReference>Copy_1_of_Update_RP_and_Input_awaited_parties</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Sub_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_2_of_Update_bounced_TP</name>
        <label>Update bounced TP</label>
        <locationX>1810</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Copy_2_of_Update_transaction_to_cancelled</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_2_of_Update_RP_and_Input_awaited_parties</name>
        <label>Update non bounced parties</label>
        <locationX>1810</locationX>
        <locationY>863</locationY>
        <connector>
            <targetReference>Generate_postal_notification_PDF</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Party_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Removed</stringValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Transaction__r.Transaction_Parties__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_2_of_Update_transaction_to_cancelled</name>
        <label>Update transaction to cancelled</label>
        <locationX>1810</locationX>
        <locationY>755</locationY>
        <connector>
            <targetReference>Copy_2_of_Update_RP_and_Input_awaited_parties</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Sub_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Mark_TP_bounced_substatus</name>
        <label>Mark TP bounced substatus</label>
        <locationX>4538</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Fire_SMS_Journey_Claim_Renter_Initiated_HV_Bond_Initiator_Renter</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__r.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Reset_Initiate_comms_on_Rental_provider_transaction_party</name>
        <label>Reset Initiate comms on Rental provider transaction party</label>
        <locationX>4274</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Cancel_Transaction_Subflow</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_bounced_TP_bc_resubmission</name>
        <label>Update bounced TP</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Update_transaction_to_cancelled_bc_resubmit</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Initiate_Comms</name>
        <label>Update Initiate Comms</label>
        <locationX>4274</locationX>
        <locationY>755</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RIC_Retained_repayment_TP_status</name>
        <label>Update RIC Retained repayment TP status</label>
        <locationX>2778</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Generate_postal_notification_PDF</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__r.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_RP_and_Input_awaited_parties_bc_resubmit</name>
        <label>Update RP and Input awaited parties</label>
        <locationX>50</locationX>
        <locationY>863</locationY>
        <filterLogic>1 OR (2 AND 3)</filterLogic>
        <filters>
            <field>Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </filters>
        <filters>
            <field>Party_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Input Awaited</stringValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.Transaction__r.Transaction_Parties__r</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_TP_status_to_Email_Bounced</name>
        <label>Update TP status to Email Bounced</label>
        <locationX>1106</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Generate_postal_notification_PDF</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Initiate_marketing_comms__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Party_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction_Party__c</object>
    </recordUpdates>
    <recordUpdates>
        <description>Update Transaction &gt; &quot;Party_was_missing_email_at_initiation__c&quot; to TRUE
And Reset Expiry Date to Current Date/Time + 20 days</description>
        <name>Update_Transaction_and_Reset_Expiry_Date</name>
        <label>Update Transaction and Reset Expiry Date</label>
        <locationX>3306</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Check_comms_after_extend_expiry</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Party_was_missing_email_at_initiation__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Transaction_Expiry_Date_Time__c</field>
            <value>
                <elementReference>RIC_New_Expiry_Date</elementReference>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_transaction_to_cancelled_bc_resubmit</name>
        <label>Update transaction to cancelled</label>
        <locationX>50</locationX>
        <locationY>755</locationY>
        <connector>
            <targetReference>Update_RP_and_Input_awaited_parties_bc_resubmit</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Transaction__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Sub_Status__c</field>
            <value>
                <stringValue>Email Bounced</stringValue>
            </value>
        </inputAssignments>
        <object>Transaction__c</object>
    </recordUpdates>
    <start>
        <locationX>2366</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Assign_Triggering_TP_Id_as_List</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>1 AND 2 AND 3</filterLogic>
        <filters>
            <field>et4ae5__HardBounce__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Transaction_Party__c</field>
            <operator>NotEqualTo</operator>
        </filters>
        <filters>
            <field>Transaction__c</field>
            <operator>NotEqualTo</operator>
        </filters>
        <object>et4ae5__IndividualEmailResult__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>Cancel_trans_if_not_agreed_rp_resubmit</name>
        <label>Cancel trans if not agreed</label>
        <locationX>2382</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Check_cancelled_transaction_comms</targetReference>
        </connector>
        <flowName>Cancel_Transaction_from_IER_if_not_all_Agreed</flowName>
        <inputAssignments>
            <name>Individual_Email_Result</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Cancel_Transaction_Subflow</name>
        <label>Cancel Transaction Subflow</label>
        <locationX>4274</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Update_Initiate_Comms</targetReference>
        </connector>
        <flowName>Cancel_Transaction_from_IER_if_not_all_Agreed</flowName>
        <inputAssignments>
            <name>Individual_Email_Result</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <description>Email Bounce Subflow - Trigger Journey by IER Record</description>
        <name>Email_Bounce_Subflow_Trigger_Journey_by_IER_Record</name>
        <label>Email Bounce Subflow - Trigger Journey by IER Record</label>
        <locationX>2514</locationX>
        <locationY>755</locationY>
        <flowName>Email_Bounce_Subflow_Trigger_Journey_by_IER_Record</flowName>
        <inputAssignments>
            <name>Individual_Email_Result</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Transaction_and_TP_Updates_Email_Bounced_Status</name>
        <label>Transaction and TP Updates - Email Bounced Status</label>
        <locationX>3746</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Generate_postal_notification_PDF</targetReference>
        </connector>
        <flowName>Email_Bounce_Subflow_Transaction_and_TP_Updates_Email_Bounced_Status</flowName>
        <inputAssignments>
            <name>transactionPartyId</name>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <description>Trigger MC Notification for Hard bounce</description>
        <name>Trigger_MC_Notification_for_Hard_bounce</name>
        <label>Trigger MC Notification for Hard bounce</label>
        <locationX>4010</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Generate_postal_notification_PDF</targetReference>
        </connector>
        <flowName>First_Email_to_Renter_Bounced_Txn_Cancellation_Email_to_RP</flowName>
        <inputAssignments>
            <name>transactionPartyId</name>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Tx_and_TP_Updates_Email_Bounced_Status_For_Renter_Transfer_Cancel_Timeout</name>
        <label>Tx and TP Updates - Email Bounced Status For Renter Transfer Cancel Timeout</label>
        <locationX>4010</locationX>
        <locationY>539</locationY>
        <connector>
            <targetReference>Trigger_MC_Notification_for_Hard_bounce</targetReference>
        </connector>
        <flowName>Email_Bounce_Subflow_Transaction_and_TP_Updates_Email_Bounced_Status</flowName>
        <inputAssignments>
            <name>transactionPartyId</name>
            <value>
                <elementReference>$Record.Transaction_Party__c</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>Triggering_TP_Id_as_List</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
