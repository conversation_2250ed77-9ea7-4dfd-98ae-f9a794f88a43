<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Trigger_Renter_Journey_for_Cancel_Notification</name>
        <label>Trigger Renter Journey for Cancel Notification</label>
        <locationX>308</locationX>
        <locationY>492</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Trigger_RP_Journey_for_Cancel_Notification</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_Renter_Cancel_Notification</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>parties</name>
            <value>
                <elementReference>Get_All_Transaction_Parties</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetPartyStatus</name>
            <value>
                <elementReference>PartyStatusCancelled</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transRecord</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Trigger_RP_Journey_for_Cancel_Notification</name>
        <label>Trigger RP Journey for Cancel Notifications</label>
        <locationX>308</locationX>
        <locationY>600</locationY>
        <actionName>MCJourneyInvocable</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>forceRequery</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>journeyName</name>
            <value>
                <elementReference>MC_Journey_RP_Cancel_Notification</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>parties</name>
            <value>
                <elementReference>Get_All_Transaction_Parties</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetPartyStatus</name>
            <value>
                <elementReference>PartyStatusRPCancelled</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetRole</name>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>transRecord</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputParameters>
        <nameSegment>MCJourneyInvocable</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Party_Status_and_Roles</name>
        <label>Assign Party Status and Roles</label>
        <locationX>308</locationX>
        <locationY>276</locationY>
        <assignmentItems>
            <assignToReference>PartyStatusCancelled</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>PartyStatusRPCancelled</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>RP Cancelled</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>RoleRentalProvider</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Rental Provider</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>RoleRenter</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>Renter</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_All_Transaction_Parties</targetReference>
        </connector>
    </assignments>
    <constants>
        <name>MC_Journey_Renter_Cancel_Notification</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RTransfer-RPCancelsTransaction-EmailNotifcations-to-Renters-6451</stringValue>
        </value>
    </constants>
    <constants>
        <name>MC_Journey_RP_Cancel_Notification</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RTransfer-RP-Cancels-Transaction-Email-to-RP-6457</stringValue>
        </value>
    </constants>
    <environments>Default</environments>
    <interviewLabel>Rental Provider Cancels Transaction {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Rental Provider Cancels Transaction</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_All_Transaction_Parties</name>
        <label>Get All Transaction Parties</label>
        <locationX>308</locationX>
        <locationY>384</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Trigger_Renter_Journey_for_Cancel_Notification</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Transaction__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Transaction_Party__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Renter Transfer</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Cancelled</stringValue>
            </value>
        </filters>
        <object>Transaction__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Assign_Party_Status_and_Roles</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>PartyStatusCancelled</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>PartyStatusRPCancelled</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>RoleRentalProvider</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>RoleRenter</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
