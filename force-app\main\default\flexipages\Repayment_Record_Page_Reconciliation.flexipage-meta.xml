<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Repayment__c.Incorrect_Repayment</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Incorrect_Repayment}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Repayment__c.Amend_repayment_amount</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.Reconciliation}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Reference_Number__c</fieldItem>
                <identifier>RecordRepayment_Reference_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payee_Type__c</fieldItem>
                <identifier>RecordPayee_Type_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payee__c</fieldItem>
                <identifier>RecordPayee_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bond__c</fieldItem>
                <identifier>RecordBond_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Transaction__c</fieldItem>
                <identifier>RecordTransaction_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Transaction_Party__c</fieldItem>
                <identifier>RecordTransaction_Party_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Amount__c</fieldItem>
                <identifier>RecordAmount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d72313a9-1dbd-4761-a4cd-2983913165b5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status__c</fieldItem>
                <identifier>RecordStatus_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.Edit_Repayment_Status}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Status__c</fieldItem>
                <identifier>RecordStatus_cField2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.Edit_Repayment_Status}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Status_Date_Time__c</fieldItem>
                <identifier>RecordRepayment_Status_Date_Time_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2 AND 3</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Charge Back</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Forwarding_Address__c</fieldItem>
                <identifier>RecordForwarding_Address__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Comments__c</fieldItem>
                <identifier>RecordComments_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Attempt_Number__c</fieldItem>
                <identifier>RecordRepayment_Attempt_Number_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Parent_Repayment__c</fieldItem>
                <identifier>RecordParent_Repayment__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Type__c</fieldItem>
                <identifier>RecordRepayment_Type__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Repayment_Method__c</fieldItem>
                <identifier>RecordRepayment_Method_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-137f2f3a-01ea-45b3-a209-7afb105f71f4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d72313a9-1dbd-4761-a4cd-2983913165b5</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-137f2f3a-01ea-45b3-a209-7afb105f71f4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-ae529d50-ffd1-4f60-892c-27a604c70364</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Reconciled_Amount__c</fieldItem>
                <identifier>RecordReconciled_Amount_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Reconciliation_Difference__c</fieldItem>
                <identifier>RecordReconciliation_Difference_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Partial_Bond_Refund_Reason__c</fieldItem>
                <identifier>RecordPartial_Bond_Refund_Reason_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-6ab9719c-1314-4cfc-8557-64bd5abbf915</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bank_Account_Statement_Transaction__c</fieldItem>
                <identifier>RecordBank_Account_Statement_Transaction_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment_Fee_Waived__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_Fee_Waived_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Incorrect_Repayment_Reason__c</fieldItem>
                <identifier>RecordIncorrect_Repayment_Reason_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Incorrect_Repayment__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-ab457512-8c83-4662-8070-771593836095</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-6ab9719c-1314-4cfc-8557-64bd5abbf915</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column15</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-ab457512-8c83-4662-8070-771593836095</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column16</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-6476265f-33eb-4c62-9a7a-26a9078e6413</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Bank_Account_Name__c</fieldItem>
                <identifier>RecordBank_Account_Name_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Account_Number__c</fieldItem>
                <identifier>RecordAccount_Number_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-9df1faa2-f33b-4af6-84e4-4700d0a73c7a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BSB__c</fieldItem>
                <identifier>RecordBSB_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-cdb466ce-2b5d-444a-b9ca-5abe86e20ff7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9df1faa2-f33b-4af6-84e4-4700d0a73c7a</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column13</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-cdb466ce-2b5d-444a-b9ca-5abe86e20ff7</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column14</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-aa9cb751-a60b-4036-9565-f50448182a09</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Name__c</fieldItem>
                <identifier>RecordInternational_Bank_Name__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Country__c</fieldItem>
                <identifier>RecordInternational_Bank_Country__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Building_Number__c</fieldItem>
                <identifier>RecordInternational_Address_Building_Number__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Street__c</fieldItem>
                <identifier>RecordInternational_Address_Street__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_City__c</fieldItem>
                <identifier>RecordInternational_Address_City__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Post_Code__c</fieldItem>
                <identifier>RecordInternational_Address_Post_Code__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Address_Country__c</fieldItem>
                <identifier>RecordInternational_Address_Country__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-f66a5a7d-aedd-4988-8e99-0d31041a30c7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.IBAN__c</fieldItem>
                <identifier>RecordIBAN_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.SWIFT_Code__c</fieldItem>
                <identifier>RecordSWIFT_Code_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.International_Bank_Repayment_Reference__c</fieldItem>
                <identifier>RecordInternational_Bank_Repayment_Reference__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Unstructured_Remittance_Info__c</fieldItem>
                <identifier>RecordUnstructured_Remittance_Info__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-fe5b37f8-4829-402a-9f06-488ec19e2d00</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f66a5a7d-aedd-4988-8e99-0d31041a30c7</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fe5b37f8-4829-402a-9f06-488ec19e2d00</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-aa5d2f27-13b6-45bd-ab86-f4691665c230</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Status__c</fieldItem>
                <identifier>RecordCheque_Status_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Number__c</fieldItem>
                <identifier>RecordCheque_Number_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Issue_Date__c</fieldItem>
                <identifier>RecordCheque_Issue_Date_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-cd03a32e-18d3-4b8a-b36c-2e1247527fd4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Status_Date_Time__c</fieldItem>
                <identifier>RecordCheque_Status_Date_Time_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Cheque_Present_Date__c</fieldItem>
                <identifier>RecordCheque_Present_Date_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Settlement_Date__c</fieldItem>
                <identifier>RecordSettlement_Date__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-f040197e-4334-488a-98a0-9a71fc0f3e08</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-cd03a32e-18d3-4b8a-b36c-2e1247527fd4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column9</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-f040197e-4334-488a-98a0-9a71fc0f3e08</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column10</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-fd32ea65-70a0-4cab-b01e-d5a71d3f6a64</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Original_Payment__c</fieldItem>
                <identifier>RecordOriginal_Payment__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Payment_Amount__c</fieldItem>
                <identifier>RecordPayment_Amount__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Card_Scheme__c</fieldItem>
                <identifier>RecordCard_Scheme__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-a88c46db-7bbe-4766-b5ae-b16b272dbeb6</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Receipt_Number__c</fieldItem>
                <identifier>RecordReceipt_Number__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Refund_Surcharge_Amount__c</fieldItem>
                <identifier>RecordRefund_Surcharge_Amount__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-2564fdd9-49ef-491a-a310-835034a489b4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a88c46db-7bbe-4766-b5ae-b16b272dbeb6</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column11</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-2564fdd9-49ef-491a-a310-835034a489b4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column12</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b62d0502-9df1-45f9-a00f-cf46f77e4837</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Submission_File_Name__c</fieldItem>
                <identifier>RecordWestpac_Submission_File_Name_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_1_Ack_Status_Desc__c</fieldItem>
                <identifier>RecordWestpac_Level_1_Ack_Status_Desc_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_1_Acknowledge_File_Name__c</fieldItem>
                <identifier>RecordWestpac_Level_1_Acknowledge_File_Name_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_2_Ack_File_Status_Desc__c</fieldItem>
                <identifier>RecordWestpac_Level_2_Ack_File_Status_Desc_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_2_Acknowledge_File_Name__c</fieldItem>
                <identifier>RecordWestpac_Level_2_Acknowledge_File_Name_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_3_Ack_File_Status_Desc__c</fieldItem>
                <identifier>RecordWestpac_Level_3_Ack_File_Status_Desc_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_3_Acknowledge_File_Name__c</fieldItem>
                <identifier>RecordWestpac_Level_3_Acknowledge_File_Name_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CAF_File_Name__c</fieldItem>
                <identifier>RecordCAF_File_Name__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Response_Code__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Response_Code__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Response_Description__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Response_Description__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-1b56f697-dc1d-4fcd-abf4-b0fe0d197a60</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Submission_Date__c</fieldItem>
                <identifier>RecordWestpac_Submission_Date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_1_Acknowledge_Date__c</fieldItem>
                <identifier>RecordWestpac_Level_1_Acknowledge_Date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_1_Acknowledge_File_Status__c</fieldItem>
                <identifier>RecordWestpac_Level_1_Acknowledge_File_Status_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_2_Acknowledge_Date__c</fieldItem>
                <identifier>RecordWestpac_Level_2_Acknowledge_Date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_2_Acknowledge_File_Status__c</fieldItem>
                <identifier>RecordWestpac_Level_2_Acknowledge_File_Status_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_3_Acknowledge_Date__c</fieldItem>
                <identifier>RecordWestpac_Level_3_Acknowledge_Date_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_Level_3_Acknowledge_File_Status__c</fieldItem>
                <identifier>RecordWestpac_Level_3_Acknowledge_File_Status_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CAF_File_Date__c</fieldItem>
                <identifier>RecordCAF_File_Date__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.QuickStream_Created_Date_Time__c</fieldItem>
                <identifier>RecordQuickStream_Created_Date_Time__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Westpac_QuickStream_Summary_Code__c</fieldItem>
                <identifier>RecordWestpac_QuickStream_Summary_Code__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-8e201b57-500c-42a5-9ad3-a34d3596e8bf</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1b56f697-dc1d-4fcd-abf4-b0fe0d197a60</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-8e201b57-500c-42a5-9ad3-a34d3596e8bf</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-217450ac-5216-4dac-b01a-9e6ebfa56693</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.CreatedById</fieldItem>
                <identifier>RecordCreatedByIdField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-e900a4b9-6f8a-4ee3-86d4-3cc9d9c1b864</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>Facet-cd4bd1aa-1645-43a6-96fa-7c8fcc10aeb4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e900a4b9-6f8a-4ee3-86d4-3cc9d9c1b864</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column7</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-cd4bd1aa-1645-43a6-96fa-7c8fcc10aeb4</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column8</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-842ed06f-30eb-49ce-9105-a34023b945ee</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-ae529d50-ffd1-4f60-892c-27a604c70364</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Repayment Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-6476265f-33eb-4c62-9a7a-26a9078e6413</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Reconciliation</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection8</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-aa9cb751-a60b-4036-9565-f50448182a09</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Domestic Banking Transfer Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection7</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-aa5d2f27-13b6-45bd-ab86-f4691665c230</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>International Banking Transfer Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>International Direct Credit</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-fd32ea65-70a0-4cab-b01e-d5a71d3f6a64</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Cheque and Money Order Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection5</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Cheque</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-b62d0502-9df1-45f9-a00f-cf46f77e4837</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Credit Card Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection6</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.Repayment_Method__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Charge Back</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-217450ac-5216-4dac-b01a-9e6ebfa56693</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Integration Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-842ed06f-30eb-49ce-9105-a34023b945ee</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>System Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-a0691c31-8ef1-441c-8ed6-2064efca5416</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Repayment__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Histories</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListComponentOverride</name>
                    <value>NONE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>rowsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>force:relatedListSingleContainer</componentName>
                <identifier>force_relatedListSingleContainer</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-e317deda-bcc0-4baf-acd1-596d75ad0fe7</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>NewCase</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Repayment__c.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Cases__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>CASES.CASE_NUMBER</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.SUBJECT</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.PRIORITY</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.STATUS</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CASES.CREATED_DATE</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Cases</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>CASES.CREATED_DATE</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Descending</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-b3dd6688-f847-4fed-b0c0-c0162db8e6bc</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-a0691c31-8ef1-441c-8ed6-2064efca5416</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-e317deda-bcc0-4baf-acd1-596d75ad0fe7</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Change History</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-b3dd6688-f847-4fed-b0c0-c0162db8e6bc</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.cases</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9d3fe2fb-b92b-465f-990a-19cbd8ea17bb</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-9d3fe2fb-b92b-465f-990a-19cbd8ea17bb</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Repayment Record Page Reconciliation</masterLabel>
    <sobjectType>Repayment__c</sobjectType>
    <template>
        <name>flexipage:recordHomeSimpleViewTemplate</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
