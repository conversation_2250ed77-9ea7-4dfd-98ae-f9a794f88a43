@IsTest
public with sharing class TransferRenterReviewControllerTest {
	@TestSetup
	static void setup() {
		TestDataFactory.assureUserTimezones();
	}

	@IsTest
	public static void getValidatedTransferDetails() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;
		Transaction_Party__c tp = TestDataFactory.createTransactionParty(
			transferTx
		);
		insert tp;
		TransferRenterReviewController.TransferWrapper transfer;

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			transfer = TransferRenterReviewController.getValidatedTransferDetails(
				makeToken(tp.Id)
			);
		}

		Test.stopTest();

		Assert.isTrue(
			transfer.idamInfoVerified,
			'Idam info verified should be true.'
		);
		Assert.isNotNull(transfer, 'Should have returned transfer data');
		Assert.areEqual(
			tp.Id,
			transfer.reviewer.Id,
			'Returned transfer data reviewer should match the transaction party used'
		);
		Assert.areEqual(
			transferTx.Id,
			transfer.details.Id,
			'Returned transfer data details should match the transaction from the transaction party used'
		);
	}

	@IsTest
	public static void validateRevisionNumber() {
		Transaction__c tx = new Transaction__c(Revision_Number__c = 1);
		String transferException;

		Test.startTest();

		TransferRenterReviewController.validateRevisionNumber(
			tx,
			1,
			TransferRenterReviewController.ERROR_RP_RESUBMITTED
		);
		try {
			TransferRenterReviewController.validateRevisionNumber(
				tx,
				2,
				TransferRenterReviewController.ERROR_RP_RESUBMITTED
			);
		} catch (AuraHandledException e) {
			transferException = e.getMessage();
		}

		Test.stopTest();

		Assert.isNotNull(transferException, 'Should have returned exception');
		Assert.areEqual(
			TransferRenterReviewController.ERROR_RP_RESUBMITTED,
			transferException,
			'Should have returned the rp resubmitted error message'
		);
	}

	@IsTest
	public static void rejectTermsOfUse() {
		insert new Integration_Settings__c(MarketingCloud__c = 'UAT');
		MCJourneyControllerTest.ApiMockSuccessV2 mcMock = MCJourneyControllerTest.setApiMockSuccessV2();
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		rentalProvider.Transaction_notices__c = '<EMAIL>';
		insert rentalProvider;

		Contact rpc = TestDataFactory.createContact(rentalProvider);
		insert rpc;

		Account incomingInc = TestDataFactory.createRenterPersonAccount(
			'Nathan',
			'Incoming Inc',
			'<EMAIL>'
		);
		Account testIncPA = TestDataFactory.createRenterPersonAccount(
			null,
			'Test Inc',
			'<EMAIL>'
		);
		List<Account> personAccounts = new List<Account>{
			incomingInc,
			testIncPA
		};
		insert personAccounts;
		Map<Id, Account> personAccountsWithContactId = new Map<Id, Account>(
			[SELECT Id, PersonContactId FROM Account WHERE id = :personAccounts]
		);

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;
		Transaction_Party__c tp = TestDataFactory.createTransactionParty(
			transferTx
		);
		tp.Company_Name__c = 'Test Inc';
		tp.Renter_position__c = Constants.TRANSACTION_PARTY_RENTER_POSITION_STAYING;
		tp.Rental_Provider__c = testIncPA.Id;
		tp.Renter__c = personAccountsWithContactId.get(testIncPA.Id)
			.PersonContactId;

		Transaction_Party__c tp2 = TestDataFactory.createTransactionParty(
			transferTx
		);
		tp2.Company_Name__c = 'Incoming Inc';
		tp2.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		tp2.Renter_position__c = Constants.TRANSACTION_PARTY_RENTER_POSITION_INCOMING;
		tp2.Email_Address__c = '<EMAIL>';
		tp2.Rental_Provider__c = incomingInc.Id;
		tp2.Renter__c = personAccountsWithContactId.get(incomingInc.Id)
			.PersonContactId;

		Transaction_Party__c rpTp = TestDataFactory.createTransactionParty(
			transferTx
		);
		rpTp.Renter_Type__c = null;
		rpTp.Role__c = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		rpTp.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		rpTp.Renter__c = rpc.Id;
		rpTp.Rental_Provider__c = rentalProvider.Id;
		insert new List<Transaction_Party__c>{ tp, tp2, rpTp };

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			TransferRenterReviewController.rejectTermsOfUse(
				tp.Id,
				1,
				makeToken(tp.Id)
			);
		}

		Test.stopTest();

		transferTx = [
			SELECT
				Id,
				Status__c,
				Sub_Status__c,
				(
					SELECT
						Id,
						Party_Status__c,
						Initiate_marketing_comms__c,
						Renter__c,
						Role__c
					FROM Transaction_Parties__r
				)
			FROM Transaction__c
			WHERE Id = :transferTx.Id
			LIMIT 1
		];
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_CANCELLED,
			transferTx.Status__c,
			'Transfer should have status set as Cancelled'
		);
		Assert.areEqual(
			Constants.TRANSACTION_SUB_STATUS_TC_REJECTED,
			transferTx.Sub_Status__c,
			'Transfer should have sub-status set as TC Rejected'
		);
		Assert.isTrue(
			!transferTx.Transaction_Parties__r.isEmpty(),
			'Transfer should have returned its parties'
		);
		for (
			Transaction_Party__c transferParty : transferTx.Transaction_Parties__r
		) {
			if (transferParty.Id == tp.Id) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_TC_REJECTED,
					transferParty.Party_Status__c,
					'Party should have sub-status set as TC Rejected'
				);
			}
			if (transferParty.Id == tp2.Id) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_CANCELLED,
					transferParty.Party_Status__c,
					'Party should have sub-status set as Cancelled'
				);
			}
			if (
				transferParty.Role__c == Constants.TRANSACTION_PARTY_ROLE_RENTER
			) {
				MCModel.JourneyEntryEvent smsEntry = mcMock.findEntry(
					Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_SMS_NOTIFICATIONS_TO_RENTERS,
					transferParty.Renter__c
				);
				Assert.isNotNull(smsEntry, 'Expected sms journey');
			}
		}
		MCModel.JourneyEntryEvent rpEmailJourney = mcMock.findEntry(
			Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_TCREJECTED_EMAIL_NOTIFICATIONS_TO_RP,
			rpc.Id
		);
		Assert.isNotNull(rpEmailJourney, 'Expected to find journey for RP');
		Assert.areEqual(
			rentalProvider.Transaction_notices__c,
			rpEmailJourney.Data.get('Txp_EmailAddress'),
			'expected transaction notices email addess'
		);
	}

	@IsTest
	public static void rejectTermsOfUse_oldRevisionNumber() {
		insert new Integration_Settings__c(MarketingCloud__c = 'UAT');
		MCJourneyControllerTest.ApiMockSuccess mcMock = MCJourneyControllerTest.setApiMockSuccess();
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;
		Transaction_Party__c tp = TestDataFactory.createTransactionParty(
			transferTx
		);
		Transaction_Party__c tp2 = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert new List<Transaction_Party__c>{ tp, tp2 };

		String transferException;
		transferTx = [
			SELECT Id, Revision_Number__c
			FROM Transaction__c
			WHERE Id = :transferTx.Id
			LIMIT 1
		];

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			try {
				TransferRenterReviewController.rejectTermsOfUse(
					tp.Id,
					Integer.valueOf(transferTx.Revision_Number__c + 1),
					PublicTransactionSummaryController.createIdToken(
						tp.Id,
						tp.Id,
						TestDataFactory.userInfoObject
					)
				);
			} catch (AuraHandledException e) {
				transferException = e.getMessage();
			}
		}

		Test.stopTest();

		Assert.isNotNull(transferException, 'Should have returned exception');
		Assert.areEqual(
			TransferRenterReviewController.ERROR_RP_RESUBMITTED,
			transferException,
			'Should have returned the correct error message'
		);
		Assert.isNull(mcMock.requestBody, 'Expected no email sent');
	}

	@IsTest
	public static void requestChanges() {
		insert new Integration_Settings__c(MarketingCloud__c = 'UAT');
		MCJourneyControllerTest.ApiMockSuccessV2 mcMock = MCJourneyControllerTest.setApiMockSuccessV2();
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		rentalProvider.Transaction_notices__c = '<EMAIL>';
		insert rentalProvider;

		Contact rpc = TestDataFactory.createContact(rentalProvider);
		insert rpc;

		Account nathanSmith = TestDataFactory.createRenterPersonAccount(
			'Nathan',
			'Smith',
			'<EMAIL>'
		);
		Account janeDoe = TestDataFactory.createRenterPersonAccount(
			'Jane',
			'Doe',
			'<EMAIL>'
		);
		Account testIncPA = TestDataFactory.createRenterPersonAccount(
			null,
			'Test Inc',
			'<EMAIL>'
		);
		Account testCoPA = TestDataFactory.createRenterPersonAccount(
			null,
			'Test Co',
			'<EMAIL>'
		);
		List<Account> personAccounts = new List<Account>{
			nathanSmith,
			janeDoe,
			testIncPA,
			testCoPA
		};
		insert personAccounts;
		Map<Id, Account> personAccountsWithContactId = new Map<Id, Account>(
			[SELECT Id, PersonContactId FROM Account WHERE id = :personAccounts]
		);

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;
		Transaction_Party__c tpRequestingChange = TestDataFactory.createTransactionParty(
			transferTx
		);
		tpRequestingChange.Rental_Provider__c = testIncPA.Id;
		tpRequestingChange.Renter__c = personAccountsWithContactId.get(
				testIncPA.Id
			)
			.PersonContactId;

		Transaction_Party__c renter2 = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		renter2.Rental_Provider__c = nathanSmith.Id;
		renter2.Renter__c = personAccountsWithContactId.get(nathanSmith.Id)
			.PersonContactId;

		Transaction_Party__c renter3 = TestDataFactory.createTransactionParty(
			transferTx
		);
		renter3.Rental_Provider__c = testCoPA.Id;
		renter3.Renter__c = personAccountsWithContactId.get(testCoPA.Id)
			.PersonContactId;

		Transaction_Party__c renter4 = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		renter4.Rental_Provider__c = janeDoe.Id;
		renter4.Renter__c = personAccountsWithContactId.get(janeDoe.Id)
			.PersonContactId;

		Transaction_Party__c rpParty = TestDataFactory.createTransactionParty(
			transferTx
		);
		rpParty.Role__c = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		rpParty.Renter__c = rpc.Id;
		rpParty.Rental_Provider__c = rentalProvider.Id;

		insert new List<Transaction_Party__c>{
			tpRequestingChange,
			renter2,
			renter3,
			renter4,
			rpParty
		};
		String comments = 'Test comments';

		Test.startTest();

		TransferRenterReviewController.RequestChangeWrapper changes = new TransferRenterReviewController.RequestChangeWrapper();
		changes.requestedById = tpRequestingChange.Id;
		changes.comments = comments;
		changes.revisionNumber = 1;
		changes.accessToken = PublicTransactionSummaryController.createIdToken(
			tpRequestingChange.Id,
			tpRequestingChange.Id,
			TestDataFactory.userInfoObject
		);

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			TransferRenterReviewController.requestChanges(changes);
		}

		Test.stopTest();

		transferTx = [
			SELECT
				Id,
				Status__c,
				Sub_Status__c,
				Comments__c,
				(
					SELECT Id, Party_Status__c, Renter__c, Role__c
					FROM Transaction_Parties__r
				)
			FROM Transaction__c
			WHERE Id = :transferTx.Id
			LIMIT 1
		];
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RP,
			transferTx.Status__c,
			'Transfer should have status set as Pending with RP'
		);
		Assert.areEqual(
			comments,
			transferTx.Comments__c,
			'Transfer should have comments matching the method provided value'
		);
		Assert.isTrue(
			!transferTx.Transaction_Parties__r.isEmpty(),
			'Transfer should have returned its parties'
		);
		for (
			Transaction_Party__c transferParty : transferTx.Transaction_Parties__r
		) {
			MCModel.JourneyEntryEvent renterSMSEntry = mcMock.findEntry(
				Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_SMS_NOTIFICATIONS_TO_RENTERS,
				transferParty.Renter__c
			);
			if (transferParty.Id == tpRequestingChange.Id) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_CHANGE_REQUESTED,
					transferParty.Party_Status__c,
					'Party should have sub-status set as Change Requested'
				);
			} else {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED,
					transferParty.Party_Status__c,
					'Party should have status set as Input Awaited'
				);
			}
			if (
				transferParty.Role__c == Constants.TRANSACTION_PARTY_ROLE_RENTER
			) {
				Assert.isNotNull(
					renterSMSEntry,
					'Expected sms to be sent to renter'
				);
			}
		}

		MCModel.JourneyEntryEvent rpEmailJourney = mcMock.findEntry(
			Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_CHANGEREQUESTED_EMAIL_NOTIFICATIONS_TO_RP,
			rpc.Id
		);
		Assert.isNotNull(rpEmailJourney, 'Expected to find journey for RP');
		Assert.areEqual(
			rentalProvider.Transaction_notices__c,
			rpEmailJourney.Data.get('Txp_EmailAddress'),
			'expected transaction notices email addess'
		);
	}

	@IsTest
	public static void acceptTransfer() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;
		Transaction_Party__c tp = TestDataFactory.createTransactionParty(
			transferTx
		);
		Transaction_Party__c tp2 = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert new List<Transaction_Party__c>{ tp, tp2 };

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			TransferRenterReviewController.acceptTransfer(
				tp.Id,
				1,
				makeToken(tp.Id)
			);
		}

		Test.stopTest();

		transferTx = [
			SELECT
				Id,
				Status__c,
				Sub_Status__c,
				Comments__c,
				(SELECT Id, Party_Status__c FROM Transaction_Parties__r)
			FROM Transaction__c
			WHERE Id = :transferTx.Id
			LIMIT 1
		];
		Assert.isNotNull(
			transferTx.Transaction_Parties__r,
			'Transfer should have returned its parties'
		);
		for (
			Transaction_Party__c transferParty : transferTx.Transaction_Parties__r
		) {
			if (transferParty.Id == tp.Id) {
				Assert.areEqual(
					Constants.TRANSACTION_PARTY_STATUS_AGREED,
					transferParty.Party_Status__c,
					'Party should have Status set as Agreed'
				);
			}
			if (transferParty.Id == tp2.Id) {
				Assert.areNotEqual(
					Constants.TRANSACTION_PARTY_STATUS_AGREED,
					transferParty.Party_Status__c,
					'Party should NOT have Status set as Agreed'
				);
			}
		}
	}

	@IsTest
	public static void finalisedTransfer() {
		insert new Integration_Settings__c(MarketingCloud__c = 'UAT');
		MCJourneyControllerTest.ApiMockSuccessV2 mcMock = MCJourneyControllerTest.setApiMockSuccessV2();
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		rentalProvider.Transaction_notices__c = '<EMAIL>';
		insert rentalProvider;

		Contact rpc = TestDataFactory.createContact(rentalProvider);
		insert rpc;

		Account incomingInc = TestDataFactory.createRenterPersonAccount(
			'Nathan',
			'Incoming Inc',
			'<EMAIL>'
		);
		Account testIncPA = TestDataFactory.createRenterPersonAccount(
			null,
			'Test Inc',
			'<EMAIL>'
		);
		List<Account> personAccounts = new List<Account>{
			incomingInc,
			testIncPA
		};
		insert personAccounts;
		Map<Id, Account> personAccountsWithContactId = new Map<Id, Account>(
			[SELECT Id, PersonContactId FROM Account WHERE id = :personAccounts]
		);

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;

		Bond_Party__c bp = TestDataFactory.createBondParty(
			bond,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		bp.Company_Name__c = 'Test Inc';
		bp.Role__c = Constants.BOND_PARTY_ROLE_RENTER;
		bp.Email_Address__c = '<EMAIL>';

		insert bp;

		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;

		Transaction_Party__c tp = TestDataFactory.createTransactionParty(
			transferTx
		);
		tp.Company_Name__c = 'Test Inc';
		tp.Renter_position__c = Constants.TRANSACTION_PARTY_RENTER_POSITION_STAYING;
		tp.Rental_Provider__c = testIncPA.Id;
		tp.Renter__c = personAccountsWithContactId.get(testIncPA.Id)
			.PersonContactId;

		Transaction_Party__c tp2 = TestDataFactory.createTransactionParty(
			transferTx
		);
		tp2.Company_Name__c = 'Incoming Inc';
		tp2.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		tp2.Renter_position__c = Constants.TRANSACTION_PARTY_RENTER_POSITION_INCOMING;
		tp2.Email_Address__c = '<EMAIL>';
		tp2.Rental_Provider__c = incomingInc.Id;
		tp2.Renter__c = personAccountsWithContactId.get(incomingInc.Id)
			.PersonContactId;

		Transaction_Party__c rpTp = TestDataFactory.createTransactionParty(
			transferTx
		);
		rpTp.Renter_Type__c = null;
		rpTp.Role__c = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		rpTp.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		rpTp.Renter__c = rpc.Id;
		rpTp.Rental_Provider__c = rentalProvider.Id;
		insert new List<Transaction_Party__c>{ tp, tp2, rpTp };

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			TransferRenterReviewController.acceptTransfer(
				tp.Id,
				1,
				makeToken(tp.Id)
			);
		}

		Test.stopTest();

		transferTx = [
			SELECT
				Id,
				Status__c,
				Sub_Status__c,
				Comments__c,
				(
					SELECT
						Id,
						Party_Status__c,
						Role__c,
						Latest_Journey_MC__c,
						Renter__c
					FROM Transaction_Parties__r
				)
			FROM Transaction__c
			WHERE Id = :transferTx.Id
			LIMIT 1
		];

		Assert.areEqual(
			Constants.TRANSACTION_STATUS_FINALISED,
			transferTx.Status__c,
			'Transaction should have Status set as finalised'
		);

		Assert.isNotNull(
			transferTx.Transaction_Parties__r,
			'Transfer should have returned its parties'
		);
		for (
			Transaction_Party__c transferParty : transferTx.Transaction_Parties__r
		) {
			if (
				transferParty.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				continue;
			}
			Assert.areEqual(
				Constants.TRANSACTION_PARTY_STATUS_AGREED,
				transferParty.Party_Status__c,
				'Party should have Status set as Agreed'
			);
			MCModel.JourneyEntryEvent smsEntry = mcMock.findEntry(
				Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_ACCEPTED_SMS_TO_RENTERS,
				transferParty.Renter__c
			);
			Assert.isNotNull(smsEntry, 'Expected sms journey');
		}

		bond = [SELECT Id, Renter_name__c FROM Bond__c WHERE Id = :bond.Id];
		Assert.areEqual(
			'Incoming Inc, Test Inc',
			bond.Renter_name__c,
			'Expected the incoming renter to be added'
		);

		List<Bond_Party__c> revisedBondParties = [
			SELECT Id, Status__c, Outgoing_Closed_Date__c, Role__c
			FROM Bond_Party__c
			WHERE Bond__c = :bond.Id
		];
		for (Bond_Party__c party : revisedBondParties) {
			if (
				party.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				continue;
			}

			//Staying renter shoud have Inactive status
			if (party.Id == bp.Id) {
				Assert.areEqual(
					Constants.BOND_PARTY_STATUS_ACTIVE,
					party.Status__c,
					'Party should have Status set as Active'
				);

				Assert.isNull(
					party.Outgoing_Closed_Date__c,
					'Active party should not have closed date'
				);
				continue;
			}

			//Rest of renters are Incoming renters, and shoud have active status
			Assert.areEqual(
				Constants.BOND_PARTY_STATUS_ACTIVE,
				party.Status__c,
				'Party should have Status set as Inactive'
			);

			Assert.isNull(
				party.Outgoing_Closed_Date__c,
				'Active party should not have closed date'
			);
		}
		MCModel.JourneyEntryEvent rpEmailJourney = mcMock.findEntry(
			Constants.MC_JOURNEY_NAME_RTRANSFER_BY_AGREEMENT_TRANSFER_RECEIPT_TO_RP,
			rpc.Id
		);
		Assert.isNotNull(rpEmailJourney, 'Expected to find journey for RP');
		Assert.areEqual(
			rentalProvider.Transaction_notices__c,
			rpEmailJourney.Data.get('Txp_EmailAddress'),
			'expected transaction notices email addess'
		);
	}

	@IsTest
	public static void finalisedTransferWithLeavingRenter() {
		insert new Integration_Settings__c(MarketingCloud__c = 'UAT');
		MCJourneyControllerTest.ApiMockSuccessV2 mcMock = MCJourneyControllerTest.setApiMockSuccessV2();
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		rentalProvider.Transaction_notices__c = '<EMAIL>';
		insert rentalProvider;

		Contact rpc = TestDataFactory.createContact(rentalProvider);
		insert rpc;

		Account outgoingInc = TestDataFactory.createRenterPersonAccount(
			'',
			'Outgoing Inc',
			'<EMAIL>'
		);
		Account testIncPA = TestDataFactory.createRenterPersonAccount(
			null,
			'Test Inc',
			'<EMAIL>'
		);
		List<Account> personAccounts = new List<Account>{
			outgoingInc,
			testIncPA
		};
		insert personAccounts;
		Map<Id, Account> personAccountsWithContactId = new Map<Id, Account>(
			[SELECT Id, PersonContactId FROM Account WHERE id = :personAccounts]
		);

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;

		Bond_Party__c bp = TestDataFactory.createBondParty(
			bond,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		bp.Company_Name__c = 'Test Inc';
		bp.Role__c = Constants.BOND_PARTY_ROLE_RENTER;
		bp.Email_Address__c = '<EMAIL>';
		Bond_Party__c bp2 = TestDataFactory.createBondParty(
			bond,
			Constants.BOND_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		bp2.First_Name__c = 'Nathan';
		bp2.Family_Name__c = 'Smith';
		bp2.Role__c = Constants.BOND_PARTY_ROLE_RENTER;
		bp2.Email_Address__c = '<EMAIL>';

		insert new List<Bond_Party__c>{ bp, bp2 };

		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;

		Transaction_Party__c tp = TestDataFactory.createTransactionParty(
			transferTx
		);
		tp.Company_Name__c = 'Test Inc';
		tp.Renter_position__c = Constants.TRANSACTION_PARTY_RENTER_POSITION_STAYING;
		tp.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		tp.Rental_Provider__c = testIncPA.Id;
		tp.Renter__c = personAccountsWithContactId.get(testIncPA.Id)
			.PersonContactId;

		Transaction_Party__c tp2 = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		tp2.First_Name__c = 'Nathan';
		tp2.Last_Name__c = 'Smith';
		tp2.Renter_position__c = Constants.TRANSACTION_PARTY_RENTER_POSITION_LEAVING;
		tp2.Email_Address__c = '<EMAIL>';
		tp2.Rental_Provider__c = outgoingInc.Id;
		tp2.Renter__c = personAccountsWithContactId.get(outgoingInc.Id)
			.PersonContactId;

		Transaction_Party__c rpTp = TestDataFactory.createTransactionParty(
			transferTx
		);
		rpTp.Renter_Type__c = null;
		rpTp.Role__c = Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER;
		rpTp.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED;
		rpTp.Renter__c = rpc.Id;
		rpTp.Rental_Provider__c = rentalProvider.Id;

		insert new List<Transaction_Party__c>{ tp, tp2, rpTp };

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			TransferRenterReviewController.acceptTransfer(
				tp2.Id,
				1,
				makeToken(tp.Id)
			);
		}

		Test.stopTest();

		transferTx = [
			SELECT
				Id,
				Status__c,
				Sub_Status__c,
				Comments__c,
				(
					SELECT Id, Party_Status__c, Role__c, Renter__c
					FROM Transaction_Parties__r
				)
			FROM Transaction__c
			WHERE Id = :transferTx.Id
			LIMIT 1
		];

		Assert.areEqual(
			Constants.TRANSACTION_STATUS_FINALISED,
			transferTx.Status__c,
			'Transaction should have Status set as finalised'
		);

		for (
			Transaction_Party__c transferParty : transferTx.Transaction_Parties__r
		) {
			if (
				transferParty.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				continue;
			}
			Assert.areEqual(
				Constants.TRANSACTION_PARTY_STATUS_AGREED,
				transferParty.Party_Status__c,
				'Party should have Status set as Agreed'
			);
			MCModel.JourneyEntryEvent smsEntry = mcMock.findEntry(
				Constants.MC_JOURNEY_NAME_RTRANSFER_RREVIEW_ACCEPTED_SMS_TO_RENTERS,
				transferParty.Renter__c
			);
			Assert.isNotNull(smsEntry, 'Expected sms journey');
		}

		bond = [SELECT Id, Renter_name__c FROM Bond__c WHERE Id = :bond.Id];
		Assert.areEqual(
			'Test Inc',
			bond.Renter_name__c,
			'Expected only the remaining renter'
		);

		List<Bond_Party__c> revisedBondParties = [
			SELECT Id, Status__c, Outgoing_Closed_Date__c, Role__c
			FROM Bond_Party__c
			WHERE Bond__c = :bond.Id
		];
		for (Bond_Party__c party : revisedBondParties) {
			if (
				party.Role__c ==
				Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			) {
				continue;
			}

			if (party.Id == bp2.Id) {
				Assert.areEqual(
					Constants.BOND_PARTY_STATUS_INACTIVE,
					party.Status__c,
					'Party should have Status set as Inactive'
				);

				Assert.areEqual(
					System.today(),
					party.Outgoing_Closed_Date__c,
					'Inactive Party should have closed date set as today'
				);
				continue;
			}
		}
		MCModel.JourneyEntryEvent rpEmailJourney = mcMock.findEntry(
			Constants.MC_JOURNEY_NAME_RTRANSFER_BY_AGREEMENT_TRANSFER_RECEIPT_TO_RP,
			rpc.Id
		);
		Assert.isNotNull(rpEmailJourney, 'Expected to find journey for RP');
		Assert.areEqual(
			rentalProvider.Transaction_notices__c,
			rpEmailJourney.Data.get('Txp_EmailAddress'),
			'expected transaction notices email addess'
		);
	}

	@IsTest
	public static void acceptTransfer_oldRevisionNumber() {
		insert new Integration_Settings__c(MarketingCloud__c = 'UAT');
		MCJourneyControllerTest.ApiMockSuccess mcMock = MCJourneyControllerTest.setApiMockSuccess();
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;
		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;
		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		insert transferTx;
		Transaction_Party__c tp = TestDataFactory.createTransactionParty(
			transferTx
		);
		Transaction_Party__c tp2 = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert new List<Transaction_Party__c>{ tp, tp2 };

		String transferException;
		transferTx = [
			SELECT Id, Revision_Number__c
			FROM Transaction__c
			WHERE Id = :transferTx.Id
			LIMIT 1
		];

		Test.startTest();

		System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
			try {
				System.runAs(new User(Id = RTBAOnlineHelper.guestUserId)) {
					TransferRenterReviewController.acceptTransfer(
						tp.Id,
						Integer.valueOf(transferTx.Revision_Number__c + 1),
						PublicTransactionSummaryController.createIdToken(
							tp.Id,
							tp.Id,
							TestDataFactory.userInfoObject
						)
					);
				}
			} catch (AuraHandledException e) {
				transferException = e.getMessage();
			}
		}

		Test.stopTest();

		Assert.isNotNull(transferException, 'Should have returned exception');
		Assert.areEqual(
			TransferRenterReviewController.ERROR_RP_RESUBMITTED,
			transferException,
			'Should have returned the correct error message'
		);
		Assert.isNull(mcMock.requestBody, 'Expected no email sent');
	}

	private static String makeToken(Id recordId) {
		return TokenUtility.makeTokenWithFields(
			new Map<String, Object>{
				TokenUtility.GENERIC_ID_KEY => recordId,
				TokenUtility.SV_USERDATA_KEY => TestDataFactory.userInfoObject.serialise()
			}
		);
	}
}