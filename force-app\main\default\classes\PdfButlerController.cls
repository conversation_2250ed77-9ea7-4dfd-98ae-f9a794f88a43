public without sharing class PdfButlerController {
	private static final Datetime NOW = Datetime.now();

	@AuraEnabled
	public static String bondDocumentPublishEvent(Id recordId) {
		String message;
		List<Bond_Document_Generation_Event__e> bondDocEvents = new List<Bond_Document_Generation_Event__e>();

		try {
			Bond_Document_Generation_Event__e bondDocObj = new Bond_Document_Generation_Event__e();
			bondDocObj.RecordId__c = recordId;
			bondDocEvents.add(bondDocObj);
			// Call method to publish events
			List<Database.SaveResult> results = EventBus.publish(bondDocEvents);

			for (Database.SaveResult sr : results) {
				message = sr.isSuccess() ? 'Success' : 'Failure';
			}
		} catch (Exception e) {
			Logger.error('PdfButlerController.bondDocumentPublishEvent', e);
		}
		return message;
	}

	public static void createBondDocument(
		List<Bond_Document_Generation_Event__e> records
	) {
		// Use sets to store unique IDs
		Set<Id> transactionIds = new Set<Id>();
		Set<Id> transactionPartyIds = new Set<Id>();
		Set<Id> otherRecordIds = new Set<Id>();
		Map<Id, List<Id>> subRecordMap = new Map<Id, List<Id>>();

		// Collect all record IDs and sub record IDs
		for (Bond_Document_Generation_Event__e obj : records) {
			Id recordId = obj.recordID__c;
			Id subRecordId = obj.Sub_Record_Id__c;

			if (!subRecordMap.containsKey(recordId)) {
				subRecordMap.put(recordId, new List<Id>());
			}
			subRecordMap.get(recordId).add(subRecordId);

			if (recordId.getSObjectType() == Transaction__c.SObjectType) {
				transactionIds.add(recordId);
			} else if (
				recordId.getSObjectType() == Transaction_Party__c.SObjectType
			) {
				transactionPartyIds.add(recordId);
			} else {
				otherRecordIds.add(recordId);
			}
		}

		// Query for all relevant Transactions
		Map<Id, Transaction__c> transactionMap = getTransactionMap(
			transactionIds
		);

		// Process Transaction__c records
		processTransactionRecords(transactionMap, subRecordMap);

		// Process Transaction_Party__c records
		processTransactionPartyRecords(transactionPartyIds, subRecordMap);

		// Process other records
		processOtherRecords(otherRecordIds, subRecordMap);
	}

	// Query for all relevant Transactions
	private static Map<Id, Transaction__c> getTransactionMap(
		Set<Id> transactionIds
	) {
		Map<Id, Transaction__c> transactionMap = new Map<Id, Transaction__c>(
			[
				SELECT
					Id,
					Valid_VCAT_Order_Number__c,
					Rental_Provider__r.Registered_Online__c,
					Status__c,
					Funding_Source__c,
					Rental_Provider__r.Office_Email__c
				FROM Transaction__c
				WHERE Id IN :transactionIds
			]
		);
		return transactionMap;
	}

	private static void processTransactionRecords(
		Map<Id, Transaction__c> transactionMap,
		Map<Id, List<Id>> subRecordMap
	) {
		Map<Id, Transaction_Party__c> subRecordMapDetails = new Map<Id, Transaction_Party__c>(
			[
				SELECT Id, Email_Address__c, Role__c
				FROM Transaction_Party__c
				WHERE Transaction__c IN :transactionMap.keySet()
			]
		);

		for (Id recordId : transactionMap.keySet()) {
			Transaction__c claimTx = transactionMap.get(recordId);
			Boolean isValidVCAT = claimTx.Valid_VCAT_Order_Number__c;

			for (Id subRecordId : subRecordMap.get(recordId)) {
				if (
					claimTx.Status__c == Constants.TRANSACTION_STATUS_FINALISED
				) {
					generatePostalNotificationWhenAllPartiesAgreed(
						recordId,
						subRecordId
					);
				} else {
					if (isValidVCAT) {
						generatePostalNotificationValidVCATUnregisteredRP(
							recordId,
							subRecordId
						);
					} else {
						if (
							!claimTx.Rental_Provider__r.Registered_Online__c &&
							subRecordMapDetails.get(subRecordId).Role__c ==
							Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
						) {
							generatePostalNotificationWithoutConsentUnregisteredRP(
								recordId,
								subRecordId
							);
						}
						if (
							String.isBlank(
								subRecordMapDetails.get(subRecordId)
									.Email_Address__c
							) &&
							subRecordMapDetails.get(subRecordId).Role__c ==
							Constants.TRANSACTION_PARTY_ROLE_RENTER
						) {
							generatePostalNotificationWithoutConsent(
								recordId,
								subRecordId
							);
						}
					}
				}
			}
		}
	}

	private static void processTransactionPartyRecords(
		Set<Id> transactionPartyIds,
		Map<Id, List<Id>> subRecordMap
	) {
		for (Id recordId : transactionPartyIds) {
			if (subRecordMap.containsKey(recordId)) {
				generatePostalNotificationRICRenterMissingEmail(recordId);
			}
		}
	}

	private static void processOtherRecords(
		Set<Id> otherRecordIds,
		Map<Id, List<Id>> subRecordMap
	) {
		for (Id recordId : otherRecordIds) {
			if (subRecordMap.containsKey(recordId)) {
				generateBondSummary(recordId);
			}
		}
	}

	@future(callout=true)
	public static void generatePostalNotificationWhenAllPartiesAgreed(
		String transactionId,
		String transactionPartyId
	) {
		Transaction_Party__c transactionParty = [
			SELECT
				Party_Name__c,
				Claim_Pay_Amount__c,
				Claim_Repayment_BSB__c,
				Role__c,
				Transaction__r.Funding_Source__c,
				Rental_Provider__r.Office_Email__c,
				Rental_Provider__r.Name
			FROM Transaction_Party__c
			WHERE Id = :transactionPartyId
			LIMIT 1
		];

		String pdfType = getDocConfigWhenTransactionFinalisedAllPartiesAgreed(
			transactionParty,
			transactionParty.Transaction__r.Funding_Source__c
		);

		if (pdfType != null) {
			GenericWithAddressPdfBuilder builder = new GenericWithAddressPdfBuilder(
				pdfType,
				transactionParty.Role__c ==
					Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
					? Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS
					: Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS
			);

			Blob content = builder.process(transactionId, transactionPartyId);

			String source = 'POST';
			String transactionNumber = [
				SELECT Name
				FROM Transaction__c
				WHERE Id = :transactionId
				LIMIT 1
			]
			.Name;

			createDocument(
				transactionId,
				content,
				getFileTitle(
					transactionNumber,
					transactionParty.Rental_Provider__r.Name
				),
				source
			);
		}
	}

	@testVisible
	private static String getDocConfigWhenTransactionFinalisedAllPartiesAgreed(
		Transaction_Party__c transactionParty,
		String fundingSource
	) {
		String pdfType;
		if (
			transactionParty.Role__c ==
			Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER &&
			transactionParty.Rental_Provider__r.Office_Email__c == null
		) {
			if (
				fundingSource == Constants.TRANSACTION_FUNDING_SOURCE_PRIVATE &&
				(transactionParty.Claim_Pay_Amount__c == 0 ||
				(transactionParty.Claim_Pay_Amount__c > 0 &&
				!String.isBlank(transactionParty.Claim_Repayment_BSB__c)))
			) {
				pdfType = Constants.PDF_DC_ALL_PARTIES_AGREED_PRIVATE;
			} else if (
				fundingSource == Constants.TRANSACTION_FUNDING_SOURCE_PRIVATE &&
				String.isBlank(transactionParty.Claim_Repayment_BSB__c) &&
				transactionParty.Claim_Pay_Amount__c > 0
			) {
				pdfType = Constants.PDF_DC_ALL_PARTIES_AGREED_BANK_DETAILS_NOT_PROVIDED;
			} else if (
				fundingSource ==
				Constants.TRANSACTION_FUNDING_SOURCE_HOMES_VICTORIA
			) {
				pdfType = Constants.PDF_DC_ALL_PARTIES_AGREED_HOMES_VICTORIA;
			}
		} else if (
			transactionParty.Role__c == Constants.TRANSACTION_PARTY_ROLE_RENTER
		) {
			if (
				fundingSource == Constants.TRANSACTION_FUNDING_SOURCE_PRIVATE &&
				(transactionParty.Claim_Pay_Amount__c > 0 &&
				String.isBlank(transactionParty.Claim_Repayment_BSB__c))
			) {
				pdfType = Constants.PDF_DC_ALL_PARTIES_AGREED_RENTER_NOTIFICATION;
			}
		}
		return pdfType;
	}

	@future(callout=true)
	public static void generateBondSummary(String recordId) {
		try {
			Map<String, Object> renterList = new Map<String, Object>();
			List<Map<String, Object>> renterDataSource = new List<Map<String, Object>>();

			List<Bond_Party__c> bondPartyList = [
				SELECT
					Id,
					First_Name__c,
					Family_Name__c,
					Company_Name__c,
					Renter_Type__c
				FROM Bond_Party__c
				WHERE
					Bond__c = :recordId
					AND Role__c = :Constants.TRANSACTION_PARTY_ROLE_RENTER
			];

			for (Bond_Party__c bondParty : bondPartyList) {
				Map<String, Object> bondPartyData = new Map<String, Object>();
				String renterName = (bondParty.Renter_Type__c == 'Individual')
					? bondParty.First_Name__c + ' ' + bondParty.Family_Name__c
					: bondParty.Company_Name__c;
				bondPartyData.put('RENTER_NAME', renterName);
				renterDataSource.add(bondPartyData);
			}
			cadmus_core__Data_Source__c renterListDataSource = [
				SELECT Id
				FROM cadmus_core__Data_Source__c
				WHERE Name = :Constants.PDF_DS_RENTERS_LIST_DATA
			];
			renterList.put(renterListDataSource.Id, renterDataSource);

			GenericPdfBuilder builder = new GenericPdfBuilder(
				Constants.PDF_DC_BOND_SUMMARY
			);
			Blob content = builder.process(recordId, null, renterList);

			ContentVersion cv = new ContentVersion(
				VersionData = content,
				Title = 'Bond Summary',
				PathOnClient = 'Bond Summary.pdf',
				SharingOption = 'A',
				Source__c = 'Other'
			);
			insert cv;

			Id conDocument = [
				SELECT ContentDocumentId
				FROM ContentVersion
				WHERE Id = :cv.Id
			]
			.ContentDocumentId;

			// update bond record
			update new Bond__c(Id = recordId, Bond_document_id__c = cv.Id);

			// delete existing bond summary docs
			List<ContentDocumentLink> existingFiles = [
				SELECT LinkedEntityId, ContentDocumentId
				FROM ContentDocumentLink
				WHERE
					LinkedEntityId = :recordId
					AND ContentDocument.title = 'Bond Summary'
			];
			if (existingFiles.size() > 0) {
				delete existingFiles;
			}

			ContentDocumentLink cDocLink = new ContentDocumentLink();
			cDocLink.ContentDocumentId = conDocument;
			cDocLink.LinkedEntityId = recordId;
			cDocLink.ShareType = 'V'; //V - Viewer permission. C - Collaborator permission. I - Inferred permission.
			cDocLink.Visibility = 'AllUsers'; //AllUsers, InternalUsers, SharedUsers
			insert cDocLink;
		} catch (Exception e) {
			Logger.error('PdfButlerController.generateBondSummary', e);
			Logger.error('Error generating bond summary', e);
			Logger.saveLog();
		}
	}

	@AuraEnabled
	public static Map<String, String> retrieveBondDocument(
		String referenceNumber
	) {
		Map<String, String> bondSummaryFile = new Map<String, String>();
		Id recordId = PublicBondSummaryController.getIdFromToken(
			referenceNumber
		);
		try {
			Id bondDocId = [
				SELECT bond_document_id__c
				FROM bond__c
				WHERE id = :recordId
			]
			?.bond_document_id__c;

			ContentVersion listContentVersion = [
				SELECT Id, ContentDocumentId, Title, VersionData
				FROM ContentVersion
				WHERE Id = :bondDocId
				LIMIT 1
			];

			bondSummaryFile.put('title', listContentVersion.Title);
			bondSummaryFile.put(
				'base64',
				EncodingUtil.base64Encode(listContentVersion.VersionData)
			);
		} catch (Exception e) {
			Logger.error('PdfButlerController.retrieveBondDocument', e);
		}

		return bondSummaryFile;
	}

	private static String getFileTitle(
		String transactionNumber,
		String partyName
	) {
		return transactionNumber + partyName + NOW.format('yyyyMMdd_HHmmss');
	}

	@AuraEnabled
	@future(callout=true)
	public static void generatePostalNotificationPdf(
		String transactionPartyId,
		String claimTransactionId
	) {
		String source = 'Post';

		try {
			GenericPdfBuilder builder = new GenericPdfBuilder(
				Constants.PDF_DC_POSTAL_NOTIFICATION
			);
			Blob content = builder.process(
				claimTransactionId,
				transactionPartyId
			);
			if (content == null) {
				return;
			}

			Transaction_Party__c txAndTxParty = [
				SELECT Party_Name__c, Transaction__r.Name
				FROM Transaction_Party__c
				WHERE Id = :transactionPartyId
			];

			//associate to claim tx
			createDocument(
				claimTransactionId,
				content,
				getFileTitle(
					txAndTxParty.Transaction__r.Name,
					txAndTxParty.Party_Name__c
				),
				source
			);
		} catch (Exception e) {
			Logger.error(
				'PdfButlerController.generatePostalNotificationPdf',
				e
			);
		}
	}

	@future(callout=true)
	public static void generatePostalNotificationWithoutConsent(
		String claimTransactionId,
		String transPartyId
	) {
		String source = 'Post';

		try {
			GenericWithAddressPdfBuilder builder = new GenericWithAddressPdfBuilder(
				Constants.PDF_DC_RENTER_INITIATED_WITHOUT_CONSENT_POSTAL_NOTIFICATION,
				Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS
			);
			Blob content = builder.process(claimTransactionId, transPartyId);
			if (content == null) {
				return;
			}

			String transactionNumber = [
				SELECT Name
				FROM Transaction__c
				WHERE Id = :claimTransactionId
				LIMIT 1
			]
			.Name;

			Transaction_Party__c tpID = [
				SELECT Party_Name__c
				FROM Transaction_Party__c
				WHERE Id = :transPartyId
				LIMIT 1
			];

			//associate to claim tx
			createDocument(
				claimTransactionId,
				content,
				getFileTitle(transactionNumber, tpID.Party_Name__c),
				source
			);
		} catch (Exception e) {
			Logger.error(
				'PdfButlerController.generatePostalNotificationWithoutConsent',
				e
			);
		}
	}

	// without consent - unregistered rp
	@future(callout=true)
	public static void generatePostalNotificationWithoutConsentUnregisteredRP(
		String claimTransactionId,
		String transPartyId
	) {
		String source = 'Post';

		try {
			Transaction__c tx = [
				SELECT
					Name,
					Funding_Source__c,
					Rental_Provider__r.Office_Email__c
				FROM Transaction__c
				WHERE Id = :claimTransactionId
				LIMIT 1
			];

			if (String.isEmpty(tx.Rental_Provider__r.Office_Email__c)) {
				GenericWithAddressPdfBuilder builder = new GenericWithAddressPdfBuilder(
					tx.Funding_Source__c ==
						Constants.BOND_FUNDING_SOURCE_HOMES_VICTORIA
						? Constants.PDF_DC_RIC_HOMES_VICTORIA_UNREGISTERED_RP_NOTIFICATION
						: Constants.PDF_DC_RIC_WITHOUT_CONSENT_UNREGISTERED_RP_POSTAL_NOTIFICATION,
					Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS
				);
				Blob content = builder.process(
					claimTransactionId,
					transPartyId
				);
				if (content == null) {
					return;
				}

				Transaction_Party__c tpID = [
					SELECT Rental_Provider__r.Name
					FROM Transaction_Party__c
					WHERE Id = :transPartyId
					LIMIT 1
				];

				//associate to claim tx
				createDocument(
					claimTransactionId,
					content,
					getFileTitle(tx.Name, tpID.Rental_Provider__r.Name),
					source
				);
			}
		} catch (Exception ex) {
			Logger.error(ex.getMessage());
		}
	}

	@future(callout=true)
	public static void generatePostalNotificationValidVCATUnregisteredRP(
		Id transactionId,
		Id bondPartyId
	) {
		ContentVersionInfoWrapper cvInfo = postalNotificationValidVCATUnregisteredRPRepayment(
			transactionId,
			bondPartyId
		);

		createDocument(
			cvInfo.recordId,
			cvInfo.content,
			cvInfo.title,
			cvInfo.source
		);
	}

	private static ContentVersionInfoWrapper postalNotificationValidVCATUnregisteredRPRepayment(
		Id recordId,
		Id bondPartyId
	) {
		Decimal claimAmount = [
			SELECT Claim_Pay_Amount__c
			FROM Transaction_Party__c
			WHERE
				Transaction__r.Id = :recordId
				AND Role__c = :Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
		]
		.Claim_Pay_Amount__c;

		String pdfType = claimAmount == 0
			? Constants.PDF_DC_UNREGISTERED_RP_NOTIFICATION_VALID_VCAT_ZERO_DOLLAR
			: Constants.PDF_DC_UNREGISTERED_RP_NOTIFICATION_VALID_VCAT_MORE_THAN_ZERO_DOLLAR;
		GenericPdfBuilder builder = new GenericPdfBuilder(pdfType);
		Blob content = builder.process(recordId);

		String source = 'POST';
		String transactionNumber = [
			SELECT Name
			FROM Transaction__c
			WHERE Id = :recordId
			LIMIT 1
		]
		.Name;

		Bond_Party__c bondParty = [
			SELECT First_Name__c, Family_Name__c
			FROM Bond_Party__c
			WHERE Id = :bondPartyId
			LIMIT 1
		];
		String partyName =
			bondParty.First_Name__c +
			' ' +
			bondParty.Family_Name__c;

		ContentVersionInfoWrapper contentVersionInfo = new ContentVersionInfoWrapper();
		contentVersionInfo.content = content;
		contentVersionInfo.recordId = recordId;
		contentVersionInfo.title = getFileTitle(transactionNumber, partyName);
		contentVersionInfo.source = source;

		return contentVersionInfo;
	}

	public static String publishPostalNotification(Id transactionId, Id tpId) {
		List<Bond_Document_Generation_Event__e> postalNotificationEvents = new List<Bond_Document_Generation_Event__e>();
		String message;
		try {
			postalNotificationEvents.add(
				new Bond_Document_Generation_Event__e(
					RecordId__c = transactionId,
					Sub_Record_Id__c = tpId
				)
			);

			// Call method to publish events
			List<Database.SaveResult> results = EventBus.publish(
				postalNotificationEvents
			);
			for (Database.SaveResult sr : results) {
				if (sr.isSuccess()) {
					message = 'Success';
				} else {
					message = 'Failure';
				}
			}
		} catch (Exception e) {
			Logger.error('PdfButlerController.publishPostalNotification', e);
		}
		return message;
	}

	@future(callout=true)
	public static void generateNotificationForRenterMissingEmailValidVcat(
		String transactionPartyId,
		String claimTransactionId
	) {
		String source = 'Post';

		try {
			GenericWithAddressPdfBuilder builder = new GenericWithAddressPdfBuilder(
				Constants.PDF_DC_RENTER_MISSING_EMAIL_VALID_VCAT_POSTAL_NOTIFICATION,
				Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS
			);
			Blob content = builder.process(
				claimTransactionId,
				transactionPartyId
			);
			if (content == null) {
				return;
			}

			Transaction_Party__c txAndTxParty = [
				SELECT Party_Name__c, Transaction__r.Name
				FROM Transaction_Party__c
				WHERE Id = :transactionPartyId
			];

			//associate to claim tx
			createDocument(
				claimTransactionId,
				content,
				getFileTitle(
					txAndTxParty.Transaction__r.Name,
					txAndTxParty.Party_Name__c
				),
				source
			);
		} catch (Exception e) {
			Logger.error(
				'PdfButlerController.generateNotificationForRenterMissingEmailValidVcat',
				e
			);
		}
	}

	// Generate postal notification for unregistered rp when there is a HV claim made by a renter
	@future(callout=true)
	public static void generatePostalNotificationPdfForUnregisteredRP(
		String recordId,
		String transactionPartyId
	) {
		UnregRpPostalNotificationPdf builder = new UnregRpPostalNotificationPdf();
		builder.process(recordId, transactionPartyId);
	}

	public static void generateTransactionResponseCancelNotification(
		List<Id> transactionIds
	) {
		for (Id transactionId : transactionIds) {
			generateTransactionResponseCancelNotificationPdfFuture(
				transactionId
			);
		}
	}

	@future(callout=true)
	public static void generateTransactionResponseCancelNotificationPdfFuture(
		Id transactionId
	) {
		GenericWithAddressPdfBuilder builder = new GenericWithAddressPdfBuilder(
			Constants.PDF_DC_TRANSACTION_RESPONSE_CANCELLED_RIC_NO_CONSENT,
			Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS
		);
		Blob content = builder.process(transactionId);

		String source = 'POST';
		String transactionNumber = [
			SELECT Name
			FROM Transaction__c
			WHERE Id = :transactionId
			LIMIT 1
		]
		.Name;

		Transaction_Party__c transactionPartyObj = [
			SELECT Party_Name__c
			FROM Transaction_Party__c
			WHERE
				Transaction__r.Id = :transactionId
				AND Role__c = :Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
			LIMIT 1
		];

		createDocument(
			transactionId,
			content,
			getFileTitle(transactionNumber, transactionPartyObj.Party_Name__c),
			source
		);
	}

	public static ContentWorkspace getWorkspace(String name) {
		List<ContentWorkspace> workspaceList = [
			SELECT Id, RootContentFolderId
			FROM ContentWorkspace
			WHERE Name = :name
			LIMIT 1
		];

		ContentWorkspace workspace = workspaceList?.isEmpty()
			? null
			: workspaceList.get(0);

		return workspace;
	}

	@TestVisible
	private static ContentFolder getFolder(String name, Id parentFolder) {
		List<ContentFolder> folderList = [
			SELECT Id, Name
			FROM ContentFolder
			WHERE Name = :name AND ParentContentFolderId = :parentFolder
		];

		ContentFolder folder = folderList?.isEmpty() ? null : folderList.get(0);

		if (folder == null) {
			folder = new ContentFolder();
			folder.Name = name;
			folder.ParentContentFolderId = parentFolder;
			insert folder;
		}

		return folder;
	}

	@TestVisible
	private static Id createDocument(
		Id recordId,
		Blob content,
		String title,
		String source
	) {
		String yearFolderName = NOW.format('yyyy');
		String monthFolderName = NOW.format('MMMM');

		ContentVersion newContentVersion = new ContentVersion();
		newContentVersion.VersionData = content;
		newContentVersion.Title = title;
		newContentVersion.PathOnClient = title + '.pdf';
		newContentVersion.SharingOption = 'A';

		newContentVersion.Source__c = source;
		newContentVersion.LibraryPathName__c =
			Constants.POSTAL_NOTIFICATION_CONTENT_WORKSPACE +
			'/' +
			yearFolderName +
			'/' +
			monthFolderName;
		newContentVersion.UpdateLocation__c = true;

		if (
			UserInfo.getUserType() != 'Guest' &&
			UserInfo.getUserType() != 'Standard'
		) {
			newContentVersion.NetworkId = RTBAOnlineHelper.networkId;
		}
		insert newContentVersion;

		Id contentDocumentId = [
			SELECT ContentDocumentId
			FROM ContentVersion
			WHERE Id = :newContentVersion.Id
		]
		.ContentDocumentId;

		ContentDocumentLink contentDocLink = new ContentDocumentLink();
		contentDocLink.ContentDocumentId = contentDocumentId;
		contentDocLink.LinkedEntityId = recordId;
		contentDocLink.ShareType = 'I'; // TODO: I or V?
		contentDocLink.Visibility = 'AllUsers';
		insert contentDocLink;

		return contentDocumentId;
	}
	@TestVisible
	private static ContentFolderMember changeDocumentLocation(
		Id documentId,
		Id sourceFolderId,
		Id destinationFolderId
	) {
		ContentFolderMember folderMember = [
				SELECT Id, ParentContentFolderId, ChildRecordId
				FROM ContentFolderMember
				WHERE
					ChildRecordId = :documentId
					AND ParentContentFolderId = :sourceFolderId
			]
			.get(0);

		folderMember.ParentContentFolderId = destinationFolderId;
		update folderMember;

		return folderMember;
	}

	@AuraEnabled
	public static String publishEventForRICRenterPostalNotification(
		Id transactionPartyId
	) {
		String message;

		List<Bond_Document_Generation_Event__e> postalNotificationRicEvents = new List<Bond_Document_Generation_Event__e>();

		try {
			Bond_Document_Generation_Event__e postalNotificationRicEvent = new Bond_Document_Generation_Event__e();
			PostalNotificationRicEvent.RecordId__c = transactionPartyId;
			postalNotificationRicEvents.add(PostalNotificationRicEvent);

			List<Database.SaveResult> results = EventBus.publish(
				postalNotificationRicEvents
			);

			for (Database.SaveResult sr : results) {
				if (sr.isSuccess()) {
					message = 'Success';
				} else {
					message = 'Failure';
				}
			}
		} catch (Exception e) {
			Logger.error(
				'PdfButlerController.publishEventForRICRenterPostalNotification',
				e
			);
		}
		return message;
	}

	// Called from the flow when an email is bounced and because the method is called in system context,
	// schedule class to change document location is not used.
	@InvocableMethod(Label='Email Bounced Postal Notification')
	public static void generatePostalNotificationBasedOnEmailTitle(
		List<MyInputParameters> transactionDetails
	) {
		for (MyInputParameters txDetail : transactionDetails) {
			if (
				txDetail.emailTitle == null ||
				DOC_CONFIG_BY_EMAIL_NAME.get(txDetail.emailTitle) == null
			) {
				continue;
			}

			generateEmailBouncePostalNotification(
				txDetail.transactionId,
				txDetail.transactionPartyId,
				txDetail.emailTitle
			);
		}
	}

	private static ContentDocumentLink createContentDocumentLink(
		Id contentDocumentId,
		Id linkedEntityId,
		String shareType
	) {
		return new ContentDocumentLink(
			ContentDocumentId = contentDocumentId,
			LinkedEntityId = linkedEntityId,
			ShareType = shareType,
			Visibility = 'AllUsers'
		);
	}

	@future(callout=true)
	public static void generatePostalNotificationRICRenterMissingEmail(
		Id tpId
	) {
		try {
			ContentVersionInfoWrapper cvInfo = generatePdfRICRenterMissingEmail(
				tpId
			);

			createDocument(
				cvInfo.recordId,
				cvInfo.content,
				cvInfo.title,
				cvInfo.source
			);
		} catch (Exception e) {
			Logger.error(
				'PdfButlerController.generatePostalNotificationRICRenterMissingEmail',
				e
			);
		}
	}

	private static ContentVersionInfoWrapper generatePdfRICRenterMissingEmail(
		Id transactionPartyId
	) {
		String source = 'POST';

		List<Transaction_Party__c> transactionParty = [
			SELECT transaction__r.Id, Id, Party_Name__c, transaction__r.Name
			FROM Transaction_Party__c
			WHERE Id = :transactionPartyId
			LIMIT 1
		];
		String transactionId = transactionParty[0].transaction__r.Id;

		GenericWithAddressPdfBuilder builder = new GenericWithAddressPdfBuilder(
			Constants.PDF_DC_POSTAL_NOTIFICATION_RIC,
			Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS
		);
		Blob content = builder.process(transactionId, transactionPartyId);

		ContentVersionInfoWrapper contentVersionInfo = new ContentVersionInfoWrapper();
		contentVersionInfo.content = content;
		contentVersionInfo.recordId = transactionId;
		contentVersionInfo.title = getFileTitle(
			transactionParty[0].transaction__r.Name,
			transactionParty[0].Party_Name__c
		);
		contentVersionInfo.source = source;

		return contentVersionInfo;
	}

	private static final Map<String, String> DOC_CONFIG_BY_EMAIL_NAME = new Map<String, String>{
		Constants.EMAIL_TITLE_RIC_RETAINED_REPAYMENT_UNREG_RP => Constants.PDF_DC_BOUNCED_EMAIL_UNREG_RP_REPAYMENT_MORE_THAN_ZERO,
		Constants.EMAIL_TITLE_RIC_FINALISED_UNREG_RP => Constants.PDF_DC_BOUNCED_EMAIL_UNREG_RP_ZERO_REPAYMENT,
		Constants.EMAIL_TITLE_BC_VCAT_RETAINED_BOND_PAYMENT_RENTER_EMAIL => Constants.PDF_DC_POSTAL_NOTIFICATION,
		Constants.EMAIL_TITLE_BC_FINALISED_CLAIM_RENTERS_NOT_CONTACTABLE => Constants.PDF_DC_PDF_BUTLER_BC_RENTER_NOT_CONTACTABLE,
		Constants.EMAIL_TITLE_RIC_RESPONSE_PERIOD_EXPIRED_UNREG_RP => Constants.PDF_DC_BOUNCED_EMAIL_UNREG_RP_RIC_RESPONSE_PERIOD_EXPIRED,
		Constants.EMAIL_TITLE_OTHER_RENTER_VALIDATED_VCAT_ORDER_ACCEPTED_REPAYMENT_AMOUNTS => Constants.PDF_DC_BOUNCED_EMAIL_OTHER_RENTERS_RIC_VCAT_ACCEPTED_AMOUNT,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_RENTER_NOTIFICATION => Constants.PDF_DC_RIC_WITHOUT_CONSENT_RENTER_POSTAL_NOTIFICATION,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_RENTER => Constants.PDF_DC_ALL_PARTIES_AGREED_RENTER_NOTIFICATION,
		Constants.EMAIL_TITLE_RIC_HV_BOND_RP_NOTIFICATION => Constants.PDF_DC_BOUNCED_EMAIL_RIC_HV_UNREG_RP_NOTIFICATION,
		Constants.EMAIL_TITLE_RIC_HV_BOND_UNREG_RP_NOTIFICATION => Constants.PDF_DC_BOUNCED_EMAIL_RIC_HV_UNREG_RP_NOTIFICATION,
		Constants.EMAIL_TITLE_RIC_HV_BOND_UNREG_RP_NOTIFICATION2 => Constants.PDF_DC_BOUNCED_EMAIL_RIC_HV_UNREG_RP_NOTIFICATION,
		Constants.EMAIL_TITLE_RIC_HV_BOND_RENTER_NOTIFICATION => Constants.PDF_DC_BOUNCED_EMAIL_RIC_HV_RENTER_NOTIFICATION,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_UNREG_RP_BOUNCED_EMAIL_NOTIFICATION => Constants.PDF_DC_RIC_RP_EMAIL_BOUNCED_NOTIIFICATION,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_RP_BOUNCED_EMAIL_NOTIFICATION => Constants.PDF_DC_RIC_RP_EMAIL_BOUNCED_NOTIIFICATION,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_WITH_BANK_DETAILS => Constants.PDF_DC_BOUNCED_EMAIL_UNREG_RP_REPAYMENT_WITH_BANK_DETAILS,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_WITHOUT_BANK_DETAILS => Constants.PDF_DC_BOUNCED_EMAIL_UNREG_RP_REPAYMENT_WITHOUT_BANK_DETAILS,
		Constants.EMAIL_TITLE_REPAYMENT_EXCEPTION_CLAIM_RENTER_NOTIFICATION => Constants.PDF_DC_REPAYMENT_EXCEPTION_CLAIM_RENTER_NOTIFICATION,
		Constants.EMAIL_TITLE_REPAYMENT_EXCEPTION_CLAIM_RP_NOTIFICATION => Constants.PDF_DC_REPAYMENT_EXCEPTION_CLAIM_RP_NOTIFICATION,
		Constants.EMAIL_TITLE_REPAYMENT_EXCEPTION_RR_RP_NOTIFICATION => Constants.PDF_DC_REPAYMENT_EXCEPTION_RR_RP_NOTIFICATION,
		Constants.EMAIL_TITLE_REPAYMENT_EXCEPTION_RR_RENTER_NOTIFICATION => Constants.PDF_DC_REPAYMENT_EXCEPTION_RR_RENTER_NOTIFICATION
	};

	private static final Map<String, String> ADDITIONAL_DATA_SOURCE_BY_EMAIL_NAME = new Map<String, String>{
		Constants.EMAIL_TITLE_BC_FINALISED_CLAIM_RENTERS_NOT_CONTACTABLE => Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_RESPONSE_PERIOD_EXPIRED_UNREG_RP => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_RETAINED_REPAYMENT_UNREG_RP => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_FINALISED_UNREG_RP => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_RENTER_NOTIFICATION => Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_OTHER_RENTER_VALIDATED_VCAT_ORDER_ACCEPTED_REPAYMENT_AMOUNTS => Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_HV_BOND_RP_NOTIFICATION => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_HV_BOND_UNREG_RP_NOTIFICATION => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_HV_BOND_RENTER_NOTIFICATION => Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_UNREG_RP_BOUNCED_EMAIL_NOTIFICATION => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_RP_BOUNCED_EMAIL_NOTIFICATION => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_WITH_BANK_DETAILS => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_RIC_WITHOUT_CONSENT_ALL_PARTIES_AGREED_WITHOUT_BANK_DETAILS => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_REPAYMENT_EXCEPTION_CLAIM_RENTER_NOTIFICATION => Constants.PDF_DS_RENTED_PROPERTY_FULL_STREET_ADDRESS,
		Constants.EMAIL_TITLE_REPAYMENT_EXCEPTION_CLAIM_RP_NOTIFICATION => Constants.PDF_DS_RENTAL_PROVIDER_FULL_STREET_ADDRESS
	};

	@TestVisible
	@future(callout=true)
	private static void generateEmailBouncePostalNotification(
		Id transactionId,
		Id transactionPartyId,
		String emailName
	) {
		try {
			IPdfBuilder builder;
			if (ADDITIONAL_DATA_SOURCE_BY_EMAIL_NAME.containsKey(emailName)) {
				builder = new GenericWithAddressPdfBuilder(
					DOC_CONFIG_BY_EMAIL_NAME.get(emailName),
					ADDITIONAL_DATA_SOURCE_BY_EMAIL_NAME.get(emailName)
				);
			} else {
				builder = new GenericPdfBuilder(
					DOC_CONFIG_BY_EMAIL_NAME.get(emailName)
				);
			}

			Blob content = builder.process(transactionId, transactionPartyId);

			if (content == null) {
				return;
			}

			Transaction_Party__c transParty = [
				SELECT Party_Name__c, Transaction__r.Name
				FROM Transaction_Party__c
				WHERE Id = :transactionPartyId
			];

			//associate to claim tx
			Id documentId = createDocument(
				transactionId,
				content,
				getFileTitle(
					transParty.Transaction__r.Name,
					transParty.Party_Name__c
				),
				'POST'
			);

			ContentWorkspace rootFolder = Test.isRunningTest()
				? PdfButlerController.getWorkspace(
						Constants.TEST_POSTAL_NOTIFICATION_CONTENT_WORKSPACE
				  )
				: PdfButlerController.getWorkspace(
						Constants.POSTAL_NOTIFICATION_CONTENT_WORKSPACE
				  );

			String yearFolderName = NOW.format('yyyy');
			ContentFolder yearFolder = getFolder(
				yearFolderName,
				rootFolder.RootContentFolderId
			);

			String monthFolderName = NOW.format('MMMM');
			ContentFolder monthFolder = getFolder(
				monthFolderName,
				yearFolder.Id
			);
			//associate to post library
			insert createContentDocumentLink(documentId, rootFolder.Id, 'I');

			//associate to month folder
			changeDocumentLocation(
				documentId,
				rootFolder.RootContentFolderId,
				monthFolder.Id
			);
		} catch (Exception e) {
			Logger.error(
				'PdfButlerController.generateEmailBouncePostalNotification',
				e
			);
		}
	}

	public class ContentVersionInfoWrapper {
		Blob content { get; set; }
		Id recordId { get; set; }
		String title { get; set; }
		String source { get; set; }
	}
	public class MyInputParameters {
		@InvocableVariable(label='transactionId')
		public Id transactionId;

		@InvocableVariable(label='transactionPartyId')
		public Id transactionPartyId;

		@InvocableVariable(label='emailTitle')
		public String emailTitle;
	}
}