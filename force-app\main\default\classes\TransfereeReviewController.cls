public with sharing class TransfereeReviewController {
	@TestVisible
	private static final WithoutSharingHelper WITHOUT_SHARING = new WithoutSharingHelper();
	public static final String ERROR_RP_REJECTED = Label.Transferee_Review_Message_When_RP_Rejected;
	public static final String ERROR_RP_CANCELLED = Label.Transferee_Review_Message_When_RP_Cancelled;
	public static final String ERROR_FINALISED = Label.Transferee_Review_Message_When_Finalised;
	public static final String ERROR_TIMED_OUT = Label.Transferee_Review_Message_When_Timed_Out;
	public static final String SUCCESS_REJECT_TRANSFER = Label.Transferee_Review_Message_When_Successful_Reject_Transfer;
	public static final String SUCCESS_APPROVAL_TRANSFER = Label.Transferee_Review_Message_When_Successful_Approval_Transfer;
	private static final Datetime NOW = Datetime.now();

	@AuraEnabled
	public static ReviewResponse getTransaction(
		String encryptedTransactionPartyId
	) {
		ReviewResponse result = new ReviewResponse();
		Id newOwnerId = UserInfo.getUserId();

		String transactionPartyId = EncryptionUtility.decryptWithKey(
			encryptedTransactionPartyId
		);

		Transaction__c transactionRecord = getTransactionByTransactionParty(
			transactionPartyId
		);

		if (isNewOwner(newOwnerId, transactionPartyId, transactionRecord)) {
			transactionRecord = WITHOUT_SHARING.transferTransactionToNewRP(
				transactionPartyId,
				newOwnerId
			);
		}

		Transaction__c transactionDetails = TransferRpReviewController.getTransaction(
			transactionRecord.Id
		);

		validateTransactionForTransfer(transactionDetails);

		result.transactionRecord = transactionDetails;
		result.bonds = WITHOUT_SHARING.getRelatedBonds(transactionRecord.Id);

		return result;
	}

	@AuraEnabled
	public static ActionResponse rejectTransfer(Id transactionId) {
		ActionResponse result = new ActionResponse();

		Transaction__c transactionRecord = TransferRpReviewController.getTransaction(
			transactionId
		);

		validateTransactionForTransfer(transactionRecord);

		Datetime rejectionTime = NOW;
		List<Transaction_Party__c> transferParties = new List<Transaction_Party__c>();
		for (
			Transaction_Party__c tp : transactionRecord.Transaction_Parties__r
		) {
			String tpStatus;
			if (
				tp.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR
			) {
				tpStatus = Constants.TRANSACTION_PARTY_STATUS_CANCELLED;
			} else if (
				tp.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			) {
				tpStatus = Constants.TRANSACTION_PARTY_STATUS_RP_REJECTED;
			}
			transferParties.add(
				new Transaction_Party__c(
					Id = tp.Id,
					Party_Status__c = tpStatus,
					Party_Status_Date__c = rejectionTime
				)
			);
		}

		try {
			update new Transaction__c(
				Id = transactionRecord.Id,
				Status__c = Constants.TRANSACTION_STATUS_CANCELLED
			);
			update transferParties;

			for (
				Transaction_Party__c tp : transactionRecord.Transaction_Parties__r
			) {
				MCJourneyController.fireJourneyEntry(
					Constants.MC_JOURNEY_NAME_RP_TRANSFER_REJECTED,
					tp.Renter__c,
					new List<Id>{ transactionId, tp.Id, tp.Rental_Provider__c },
					tp.Id
				);
			}
		} catch (Exception e) {
			throw AuraHelper.throwToAura('Error updating record.', e);
		}

		result.message = StringUtility.replacePlaceholders(
			SUCCESS_REJECT_TRANSFER,
			new Map<String, String>{
				'transactionNumber' => transactionRecord.Name
			}
		);
		return result;
	}

	@AuraEnabled
	public static ActionResponse acceptTransfer(Id transactionId) {
		ActionResponse result = new ActionResponse();

		Transaction__c transactionRecord = TransferRpReviewController.getTransaction(
			transactionId
		);

		validateTransactionForTransfer(transactionRecord);

		Map<Id, Transaction_Party__c> tpsWithEmail = WITHOUT_SHARING.getTpsWithEmail(
			transactionRecord
		);

		List<Transaction_Party__c> transferParties = new List<Transaction_Party__c>();
		Transaction_Party__c transferor = null;
		for (
			Transaction_Party__c tp : transactionRecord.Transaction_Parties__r
		) {
			if (
				tp.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
			) {
				Id contactId = WITHOUT_SHARING.getNewOwnerContact(
						UserInfo.getUserId()
					)
					?.Id;

				Transaction_Party__c tpToUpdate = new Transaction_Party__c(
					Id = tp.Id,
					Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_AGREED
				);

				if (contactId != null) {
					tpToUpdate.Renter__c = contactId;
					tp.Renter__c = contactId;
				}

				transferParties.add(tpToUpdate);
			} else if (
				tp.Rental_Provider_Type__c ==
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR
			) {
				transferor = tp;
			}
		}

		try {
			update new Transaction__c(
				Id = transactionRecord.Id,
				Status__c = Constants.TRANSACTION_STATUS_FINALISED,
				Transaction_Completion_Date__c = NOW
			);
			update transferParties;

			String previousRpName = transferor?.RTBA_Registered_Name__c;
			if (String.isBlank(previousRpName)) {
				previousRpName = transferor?.Party_Name__c;
			}
			List<Bond__c> bonds = WITHOUT_SHARING.transferBondsToNewRP(
				previousRpName,
				transactionRecord.Id,
				UserInfo.getUserId(),
				RentalProviderTransferController.currentUser.AccountId
			);

			AuraHelper.failIf(bonds.isEmpty(), 'No bonds transferred');

			for (
				Transaction_Party__c tp : transactionRecord.Transaction_Parties__r
			) {
				String journeyToFire = null;
				Map<String, Object> extraFields = new Map<String, Object>();
				String emailToUse;
				if (
					tp.Rental_Provider_Type__c ==
					Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE
				) {
					journeyToFire = Constants.MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEREE;
					//decide email fields, add bond number if single bond
					if (
						transactionRecord.Transfer_Option__c ==
						Constants.TRANSACTION_TRANSFER_OPTION_TRANSFER_OUT_ONE_BOND
					) {
						emailToUse = tpsWithEmail.get(tp.Id)
							?.Rental_Provider__r
							?.Bond_receipts__c;
						extraFields.put('Bnd_Number', bonds[0].Name);
					} else {
						emailToUse = tpsWithEmail.get(tp.Id)
							?.Rental_Provider__r
							?.Transaction_notices__c;
					}
				} else {
					journeyToFire = Constants.MC_JOURNEY_NAME_RP_TRANSFER_WITH_CONSENT_FINALISED_TRANSFEROR;
					//MC misconfigured
					emailToUse = tpsWithEmail.get(tp.Id)
						?.Rental_Provider__r
						?.Transaction_notices__c;
				}
				if (String.isNotBlank(emailToUse)) {
					extraFields.put('Txp_EmailAddress', emailToUse);
				}
				MCJourneyController.fireJourneyEntry(
					journeyToFire,
					tp.Renter__c,
					new List<Id>{ transactionId, tp.Id, tp.Rental_Provider__c },
					extraFields,
					tp.Id
				);
			}
		} catch (Exception e) {
			Logger.error('Error finalising transfer', e);
			Logger.saveLog();
			throw AuraHelper.throwToAura('Error updating record.', e);
		}

		result.message = StringUtility.replacePlaceholders(
			SUCCESS_APPROVAL_TRANSFER,
			new Map<String, String>{
				'transactionNumber' => transactionRecord.Name
			}
		);
		return result;
	}

	/**
	 * Is a new Owner if have no access to the Transaction Or the Transaction Owner is different,
	 * And no Account has been assigned to the Transferee
	 */
	@TestVisible
	private static Boolean isNewOwner(
		Id newOwnerId,
		Id transactionPartyId,
		Transaction__c transactionRecord
	) {
		Transaction_Party__c transferee = WITHOUT_SHARING.getTransferee(
			transactionPartyId
		);

		Boolean isUserTransactionOwner =
			transactionRecord != null &&
			transactionRecord?.OwnerId == newOwnerId;
		Boolean isTransactionTransferred =
			transferee.Rental_Provider__c != null;

		return !isUserTransactionOwner && !isTransactionTransferred;
	}

	private static Transaction__c getTransactionByTransactionParty(
		Id transactionPartyId
	) {
		List<Transaction_Party__c> tpList = [
			SELECT Transaction__c
			FROM Transaction_Party__c
			WHERE Id = :transactionPartyId
			LIMIT 1
		];

		if (tpList == null || tpList.size() == 0) {
			return null;
		}

		List<Transaction__c> txList = [
			SELECT Id, OwnerId
			FROM Transaction__c
			WHERE Id = :tpList[0].Transaction__c
			LIMIT 1
		];

		if (txList == null || txList.size() == 0) {
			return null;
		}

		return txList[0];
	}

	private static void validateTransactionForTransfer(
		Transaction__c transactionRecord
	) {
		AuraHelper.failIf(
			transactionRecord.Type__c !=
			Constants.TRANSACTION_TYPE_RENTAL_PROVIDER_TRANSFER,
			'Wrong transfer type'
		);

		CancelTransactionController.validateTransfereeRPRejected(
			transactionRecord,
			ERROR_RP_REJECTED
		);

		CancelTransactionController.validateTransfereeRPCancelled(
			transactionRecord,
			ERROR_RP_CANCELLED
		);

		TransferRenterReviewController.validateFinalised(
			transactionRecord,
			ERROR_FINALISED
		);

		TransferRenterReviewController.validateTimedOut(
			transactionRecord,
			ERROR_TIMED_OUT
		);
	}

	@AuraEnabled
	public static Map<String, String> generatePdfRentalProviderTransferBondsWithoutSharing(
		List<RentalProviderTransferController.BondExport> validBonds
	) {
		return WITHOUT_SHARING.generatePdfRentalProviderTransferBonds(
			validBonds
		);
	}

	@AuraEnabled
	public static Map<String, String> generateExcelRentalProviderTransferBondsWithoutSharing(
		List<RentalProviderTransferController.BondExport> validBonds
	) {
		return WITHOUT_SHARING.generateExcelRentalProviderTransferBonds(
			validBonds
		);
	}

	private without sharing class WithoutSharingHelper {
		private Transaction_Party__c getTransferee(Id transactionPartyId) {
			return [
				SELECT Id, Rental_Provider__c
				FROM Transaction_Party__c
				WHERE Id = :transactionPartyId
				LIMIT 1
			];
		}

		public Transaction__c transferTransactionToNewRP(
			Id transactionPartyId,
			Id newOwnerId
		) {
			Account newOwnerAccount = getNewOwnerAccount(newOwnerId);

			Transaction__c transactionRecord = changeTransactionOwner(
				transactionPartyId,
				newOwnerId,
				newOwnerAccount.Id
			);

			setTransfereeAccount(transactionPartyId, newOwnerAccount);

			return transactionRecord;
		}

		private Contact getNewOwnerContact(Id userId) {
			Contact newOwnerContact = [
				SELECT Id, AccountId
				FROM Contact
				WHERE
					Id IN (
						SELECT ContactId
						FROM User
						WHERE Id = :userId
					)
				LIMIT 1
			];
			return newOwnerContact;
		}

		@TestVisible
		private Account getNewOwnerAccount(Id userId) {
			Contact newOwnerContact = getNewOwnerContact(userId);

			if (newOwnerContact == null) {
				return null;
			}

			Account newOwnerAccount = [
				SELECT
					Id,
					Name,
					RTBA_Registration_Number__c,
					RTBA_Registered_Name__c
				FROM Account
				WHERE Id = :newOwnerContact.AccountId
			];

			return newOwnerAccount;
		}

		@TestVisible
		private Transaction__c changeTransactionOwner(
			Id transactionPartyId,
			Id newOwnerId,
			Id newOwnerAccountId
		) {
			Transaction__c transactionRecord = [
				SELECT Id, OwnerId
				FROM Transaction__c
				WHERE
					Id IN (
						SELECT Transaction__c
						FROM Transaction_Party__c
						WHERE Id = :transactionPartyId
					)
			];

			if (newOwnerId != null) {
				transactionRecord.OwnerId = newOwnerId;
			}
			transactionRecord.Rental_Provider__c = newOwnerAccountId;
			update transactionRecord;
			return transactionRecord;
		}

		@TestVisible
		private Transaction_Party__c setTransfereeAccount(
			Id transactionPartyId,
			Account newOwnerAccount
		) {
			Transaction_Party__c transactionPartyRecord = [
				SELECT Id, Rental_Provider__c, Rental_Provider_Type__c
				FROM Transaction_Party__c
				WHERE Id = :transactionPartyId
			];

			AuraHelper.failIf(
				transactionPartyRecord.Rental_Provider_Type__c !=
				Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEREE,
				'Supplied party is not transferee'
			);

			transactionPartyRecord.Rental_Provider__c = newOwnerAccount.Id;
			transactionPartyRecord.Company_Name__c = newOwnerAccount.Name;
			update transactionPartyRecord;
			return transactionPartyRecord;
		}

		@TestVisible
		private List<Bond__c> getRelatedBonds(Id transactionId) {
			return [
				SELECT
					Id,
					Name,
					Rented_Property_Address__c,
					Current_Bond_Amount__c,
					Homes_Victoria_Loan_Number__c,
					Bond_Paid_By__c,
					Modification_Bond_Component__c,
					Modification_Bond_Description__c,
					Type__c,
					Street_Address__c,
					Additional_Address_Details__c,
					Suburb__c,
					State__c,
					Postcode__c,
					OwnerId,
					Rental_Provider__c,
					(
						SELECT
							Id,
							First_Name__c,
							Family_Name__c,
							Company_Name__c,
							Role__c,
							Renter_Type__c,
							Status__c,
							Email_Address__c
						FROM Bond_Parties__r
						WHERE Status__c = :Constants.BOND_PARTY_STATUS_ACTIVE
					)
				FROM Bond__c
				WHERE
					Id IN (
						SELECT Bond__c
						FROM Transfer_Transaction_Bonds__c
						WHERE Transfer_Transaction__c = :transactionId
					)
				ORDER BY Name
			];
		}

		private Map<String, String> generatePdfRentalProviderTransferBonds(
			List<RentalProviderTransferController.BondExport> validBonds
		) {
			return RentalProviderTransferController.generatePdfRentalProviderTransferBonds(
				validBonds
			);
		}

		private Map<String, String> generateExcelRentalProviderTransferBonds(
			List<RentalProviderTransferController.BondExport> validBonds
		) {
			return RentalProviderTransferController.generateExcelRentalProviderTransferBonds(
				validBonds
			);
		}

		/**
		 * @param oldRpName		 	Old rp's name, for MC
		 * @param transactionId		tx for the transfer
		 * @param newOwnerId		user id of new rp
		 * @param newOwnerAccountId	account id of new rp
		 *
		 * @return the list of bonds from {@link TransfereeReviewController.WithoutSharingHelper#getRelatedBonds}
		 */
		@TestVisible
		private List<Bond__c> transferBondsToNewRP(
			String oldRpName,
			Id transactionId,
			Id newOwnerId,
			Id newOwnerAccountId
		) {
			if (newOwnerAccountId == null) {
				throw new IllegalArgumentException('Blank new owner account');
			}
			if (String.isBlank(oldRpName)) {
				throw new IllegalArgumentException('Blank old rp name');
			}
			List<Bond__c> relatedBonds = getRelatedBonds(transactionId);
			Account newOwnerAccount = [
				SELECT Id, Name, Phone, Transaction_notices__c
				FROM Account
				WHERE Id = :newOwnerAccountId
			];
			Transaction_Party__c txPartyTransferee = TransactionPartyUtility.createTransactionPartyForRentalProvider(
				newOwnerAccount,
				transactionId
			);

			List<Bond_Party__c> bondPartiesToUpsert = new List<Bond_Party__c>();
			Set<Id> bondIds = new Set<Id>();

			for (Bond__c bond : relatedBonds) {
				if (bond.OwnerId == newOwnerId) {
					continue;
				}
				if (newOwnerId != null) {
					bond.OwnerId = newOwnerId;
				}
				bond.Rental_Provider__c = newOwnerAccount.Id;

				if (bond.Bond_Parties__r != null) {
					for (Bond_Party__c bp : bond.Bond_Parties__r) {
						if (
							bp.Role__c !=
							Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER ||
							bp.Status__c != Constants.BOND_PARTY_STATUS_ACTIVE
						) {
							continue;
						}
						bp.Status__c = Constants.BOND_PARTY_STATUS_INACTIVE;
						bp.Outgoing_Closed_Date__c = System.today();
						bondPartiesToUpsert.add(bp);
					}
				}

				Bond_Party__c newParty = BondsUtility.createBondParty(
					bond,
					txPartyTransferee
				);
				newParty.Status__c = Constants.BOND_PARTY_STATUS_ACTIVE;
				if (newOwnerId != null) {
					Id contactId = getNewOwnerContact(newOwnerId)?.Id;
					if (contactId != null) {
						newParty.Renter__c = contactId;
					}
				}
				bondPartiesToUpsert.add(newParty);
				bondIds.add(bond.Id);
			}

			update relatedBonds;
			upsert bondPartiesToUpsert;
			RPTransferRenterNotificationBatch.queueRenterTransferBatch(
				oldRpName,
				bondIds,
				transactionId
			);
			return relatedBonds;
		}

		private Map<Id, Transaction_Party__c> getTpsWithEmail(
			Transaction__c transactionRecord
		) {
			return new Map<Id, Transaction_Party__c>(
				[
					SELECT
						Id,
						Rental_Provider__r.Transaction_notices__c,
						Rental_Provider__r.Bond_receipts__c
					FROM Transaction_Party__c
					WHERE Id IN :transactionRecord.Transaction_Parties__r
				]
			);
		}
	}

	@InvocableMethod(Label='Transfer bonds to new RP')
	public static void transferBondsToNewRP(
		List<TransferBondsToNewRpInput> inputs
	) {
		if (inputs.isEmpty()) {
			throw new IllegalArgumentException('No inputs supplied');
		}
		if (inputs.size() > 1) {
			throw new IllegalArgumentException(
				'Bulkified contexts not supported'
			);
		}
		TransferBondsToNewRpInput input = inputs[0];

		if (
			input.newRpId == null ||
			input.newRpId.getSobjectType() != Account.SObjectType
		) {
			throw new IllegalArgumentException(
				'Bad account id: ' + input.newRpId
			);
		}
		Transaction_Party__c transferor = [
			SELECT Party_Name__c, RTBA_Registered_Name__c
			FROM Transaction_Party__c
			WHERE
				Transaction__c = :input.transactionId
				AND Rental_Provider_Type__c = :Constants.TRANSACTION_PARTY_RENTAL_PROVIDER_TYPE_TRANSFEROR
		];
		String oldRpName = String.isNotBlank(transferor.RTBA_Registered_Name__c)
			? transferor.RTBA_Registered_Name__c
			: transferor.Party_Name__c;
		WITHOUT_SHARING.transferBondsToNewRP(
			oldRpName,
			input.transactionId,
			null,
			input.newRpId
		);
	}

	public class ReviewResponse {
		@AuraEnabled
		public Transaction__c transactionRecord { get; set; }
		@AuraEnabled
		public List<Bond__c> bonds { get; set; }

		public ReviewResponse() {
			this.transactionRecord = new Transaction__c();
			this.bonds = new List<Bond__c>();
		}
	}

	public class ActionResponse {
		@AuraEnabled
		public String message { get; set; }
	}

	public class TransferBondsToNewRpInput {
		@InvocableVariable(Label='Transaction Id' Required=true)
		public Id transactionId;
		@InvocableVariable(
			Label='New RP Id'
			Required=true
			Description='Account Id'
		)
		public Id newRpId;
	}
}