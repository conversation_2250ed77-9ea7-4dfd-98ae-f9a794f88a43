@IsTest
public class MCJourneyControllerTest {
	private static final String TEST_BUSINESS_ENTITY = 'FAKE';

	public static ApiMockSuccess setApiMockSuccess() {
		ApiMockSuccess mock = new ApiMockSuccess();
		Test.setMock(HttpCalloutMock.class, mock);
		MCJourneyController.mockSet = true;
		return mock;
	}

	public static ApiMockSuccessV2 setApiMockSuccessV2() {
		ApiMockSuccessV2 mock = new ApiMockSuccessV2();
		Test.setMock(HttpCalloutMock.class, mock);
		MCJourneyController.mockSet = true;
		return mock;
	}

	public static ApiMockSuccessBulk setApiMockSuccessBulk() {
		ApiMockSuccessBulk mock = new ApiMockSuccessBulk();
		Test.setMock(HttpCalloutMock.class, mock);
		MCJourneyController.mockSet = true;
		return mock;
	}

	public static ApiMockFailure setApiMockFailure() {
		ApiMockFailure mock = new ApiMockFailure();
		Test.setMock(HttpCalloutMock.class, mock);
		MCJourneyController.mockSet = true;
		return mock;
	}

	public static ApiMockFailureBulk setApiMockFailureBulk() {
		ApiMockFailureBulk mock = new ApiMockFailureBulk();
		Test.setMock(HttpCalloutMock.class, mock);
		MCJourneyController.mockSet = true;
		return mock;
	}

	@IsTest
	static void testSuccessBehavior() {
		ApiMockSuccess mock = setApiMockSuccess();

		MCJourneyController.JOURNEY_MAPPINGS.clear();

		final String TEST_JOURNEY_NAME = 'TestJourney';

		MCJourneyController.processMappingMeta(
			new List<MC_Journey_Mapping__mdt>{
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = TEST_BUSINESS_ENTITY,
					DeveloperName = 'Test_Journey',
					Journey_ID__c = 'FakeJourneyId',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				),
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = 'random other entity',
					DeveloperName = 'Test_Journey_2',
					Journey_ID__c = 'the other id',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				)
			},
			TEST_BUSINESS_ENTITY
		);
		Assert.areEqual(
			1,
			MCJourneyController.JOURNEY_MAPPINGS.size(),
			'Expected only our test mapping matching the test entity'
		);

		Account rp = TestDataFactory.createRentalProviderAccount();
		insert rp;

		Contact con = TestDataFactory.createContact(rp);
		insert con;

		Transaction__c tx = TestDataFactory.createTransaction(rp);
		insert tx;

		Transaction_Party__c party = TestDataFactory.createTransactionParty(
			tx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert party;

		Test.startTest();
		MCJourneyController.fireJourneyEntry(
			TEST_JOURNEY_NAME,
			con.Id,
			new List<Id>{ tx.Id, party.Id },
			new Map<String, Object>{ 'foo' => 'bar' },
			party.Id
		);
		Test.stopTest();

		Transaction_Party__c partyAfter = [
			SELECT Id, Latest_Journey_MC__c
			FROM Transaction_Party__c
			WHERE Id = :party.Id
		];
		Assert.isNotNull(
			partyAfter.Latest_Journey_MC__c,
			'Expected a journey to be set'
		);
		Assert.isTrue(
			partyAfter.Latest_Journey_MC__c.endsWith(TEST_JOURNEY_NAME),
			'expected journey to have succeeded'
		);
		Assert.isNotNull(mock.requestBody, 'Body should have been set');
		Assert.isNotNull(
			mock.requestBody.ContactKey,
			'Contact key should be present'
		);
		Assert.areEqual(
			con.Id,
			Id.valueOf(mock.requestBody.ContactKey),
			'Contact key should be what we sent'
		);
		Assert.isTrue(
			String.isNotBlank(mock.requestBody.EventDefinitionKey),
			'Event definition should not be empty'
		);
		Assert.isNotNull(mock.requestBody.Data, 'Data should be present');
		Assert.areEqual(
			'bar',
			mock.requestBody.Data.get('foo'),
			'Extra field not set correctly'
		);
	}

	@IsTest
	static void testSuccessBehaviorBulk() {
		ApiMockSuccessBulk mock = setApiMockSuccessBulk();

		MCJourneyController.JOURNEY_MAPPINGS.clear();

		final String TEST_JOURNEY_NAME = 'TestJourney';

		MCJourneyController.processMappingMeta(
			new List<MC_Journey_Mapping__mdt>{
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = TEST_BUSINESS_ENTITY,
					DeveloperName = 'Test_Journey',
					Journey_ID__c = 'FakeJourneyId',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				),
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = 'random other entity',
					DeveloperName = 'Test_Journey_2',
					Journey_ID__c = 'the other id',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				)
			},
			TEST_BUSINESS_ENTITY
		);
		Assert.areEqual(
			1,
			MCJourneyController.JOURNEY_MAPPINGS.size(),
			'Expected only our test mapping matching the test entity'
		);

		Account rp = TestDataFactory.createRentalProviderAccount();
		insert rp;

		Contact con = TestDataFactory.createContact(rp);
		insert con;

		Transaction__c tx = TestDataFactory.createTransaction(rp);
		insert tx;

		Transaction_Party__c party = TestDataFactory.createTransactionParty(
			tx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert party;

		Test.startTest();
		MCJourneyController.BulkEntryItem item = new MCJourneyController.BulkEntryItem(
			new List<Id>{ tx.Id, party.Id },
			party.Id
		);
		MCJourneyController.fireJourneyEntryBulk(
			TEST_JOURNEY_NAME,
			new Map<Id, MCJourneyController.BulkEntryItem>{ con.Id => item },
			new Map<String, Object>{ 'foo' => 'bar' }
		);
		Test.stopTest();

		Transaction_Party__c partyAfter = [
			SELECT Id, Latest_Journey_MC__c
			FROM Transaction_Party__c
			WHERE Id = :party.Id
		];
		Assert.isNotNull(
			partyAfter.Latest_Journey_MC__c,
			'Expected a journey to be set'
		);
		Assert.isTrue(
			partyAfter.Latest_Journey_MC__c.endsWith(TEST_JOURNEY_NAME),
			'expected journey to have succeeded'
		);
		Assert.isNotNull(mock.requestBody, 'Body should have been set');
		Assert.isNotNull(
			mock.requestBody.members[0].contactKey,
			'Contact key should be present'
		);
		Assert.areEqual(
			con.Id,
			Id.valueOf(mock.requestBody.members[0].contactKey),
			'Contact key should be what we sent'
		);
		Assert.isTrue(
			String.isNotBlank(mock.requestBody.eventDefinitionKey),
			'Event definition should not be empty'
		);
		Assert.isNotNull(
			mock.requestBody.members[0].data,
			'Data should be present'
		);
		Assert.areEqual(
			'bar',
			mock.requestBody.members[0].data.get('foo'),
			'Extra field not set correctly'
		);
	}

	@IsTest
	static void testSuccessBehaviorBondPartyCompletion() {
		ApiMockSuccess mock = setApiMockSuccess();

		MCJourneyController.JOURNEY_MAPPINGS.clear();

		final String TEST_JOURNEY_NAME = 'TestJourney';

		MCJourneyController.processMappingMeta(
			new List<MC_Journey_Mapping__mdt>{
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = TEST_BUSINESS_ENTITY,
					DeveloperName = 'Test_Journey',
					Journey_ID__c = 'FakeJourneyId',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				),
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = 'random other entity',
					DeveloperName = 'Test_Journey_2',
					Journey_ID__c = 'the other id',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				)
			},
			TEST_BUSINESS_ENTITY
		);
		Assert.areEqual(
			1,
			MCJourneyController.JOURNEY_MAPPINGS.size(),
			'Expected only our test mapping matching the test entity'
		);

		Account rp = TestDataFactory.createRentalProviderAccount();
		insert rp;

		Contact con = TestDataFactory.createContact(rp);
		insert con;

		Transaction__c tx = TestDataFactory.createTransaction(rp);
		insert tx;

		Bond__c bond = TestDataFactory.createBond(tx);
		insert bond;

		Bond_Party__c bondParty = TestDataFactory.createBondParty(
			bond,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		insert bondParty;

		Test.startTest();
		MCJourneyController.fireJourneyEntry(
			TEST_JOURNEY_NAME,
			con.Id,
			new List<Id>{ tx.Id, bond.Id, bondParty.Id },
			bondParty.Id
		);
		Test.stopTest();

		Bond_Party__c partyAfter = [
			SELECT Id, Latest_Journey_MC__c
			FROM Bond_Party__c
			WHERE Id = :bondParty.Id
		];
		Assert.isNotNull(
			partyAfter.Latest_Journey_MC__c,
			'Expected a journey to be set'
		);
		Assert.isTrue(
			partyAfter.Latest_Journey_MC__c.endsWith(TEST_JOURNEY_NAME),
			'expected journey to have succeeded'
		);
		Assert.isNotNull(mock.requestBody, 'Body should have been set');
		Assert.isNotNull(
			mock.requestBody.ContactKey,
			'Contact key should be present'
		);
		Assert.areEqual(
			con.Id,
			Id.valueOf(mock.requestBody.ContactKey),
			'Contact key should be what we sent'
		);
		Assert.isTrue(
			String.isNotBlank(mock.requestBody.EventDefinitionKey),
			'Event definition should not be empty'
		);
		Assert.isNotNull(mock.requestBody.Data, 'Data should be present');
	}

	@IsTest
	static void testSuccessBehaviorUnknownCompletion() {
		setApiMockSuccess();
		MCJourneyController.JOURNEY_MAPPINGS.clear();
		final String TEST_JOURNEY_NAME = 'TestJourney';

		MCJourneyController.processMappingMeta(
			new List<MC_Journey_Mapping__mdt>{
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = TEST_BUSINESS_ENTITY,
					DeveloperName = 'Test_Journey',
					Journey_ID__c = 'FakeJourneyId',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				)
			},
			TEST_BUSINESS_ENTITY
		);

		Account rp = TestDataFactory.createRentalProviderAccount();
		insert rp;

		Contact con = TestDataFactory.createContact(rp);
		insert con;

		Transaction__c tx = TestDataFactory.createTransaction(rp);
		insert tx;

		Transaction_Party__c party = TestDataFactory.createTransactionParty(
			tx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert party;

		Test.startTest();
		MCJourneyController.fireJourneyEntry(
			TEST_JOURNEY_NAME,
			con.Id,
			new List<Id>{ tx.Id, party.Id },
			tx.Id
		);
		Test.stopTest();

		Transaction_Party__c partyAfter = [
			SELECT Id, Latest_Journey_MC__c
			FROM Transaction_Party__c
			WHERE Id = :party.Id
		];
		Assert.isNull(
			partyAfter.Latest_Journey_MC__c,
			'Expected no journey to be set'
		);
	}

	@IsTest
	static void testApiFailBehavior() {
		setApiMockFailure();
		MCJourneyController.JOURNEY_MAPPINGS.clear();
		final String TEST_JOURNEY_NAME = 'TestJourney';

		MCJourneyController.processMappingMeta(
			new List<MC_Journey_Mapping__mdt>{
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = TEST_BUSINESS_ENTITY,
					DeveloperName = 'Test_Journey',
					Journey_ID__c = 'FakeJourneyId',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				)
			},
			TEST_BUSINESS_ENTITY
		);

		Account rp = TestDataFactory.createRentalProviderAccount();
		insert rp;

		Contact con = TestDataFactory.createContact(rp);
		insert con;

		Transaction__c tx = TestDataFactory.createTransaction(rp);
		insert tx;

		Transaction_Party__c party = TestDataFactory.createTransactionParty(
			tx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert party;

		Test.startTest();
		MCJourneyController.fireJourneyEntry(
			TEST_JOURNEY_NAME,
			con.Id,
			new List<Id>{ tx.Id, party.Id },
			party.Id
		);
		Test.stopTest();

		Transaction_Party__c partyAfter = [
			SELECT Id, Latest_Journey_MC__c
			FROM Transaction_Party__c
			WHERE Id = :party.Id
		];
		Assert.isNull(
			partyAfter.Latest_Journey_MC__c,
			'Expected no journey to be set'
		);
	}

	@IsTest
	static void testApiFailBehaviorBulk() {
		setApiMockFailureBulk();
		MCJourneyController.JOURNEY_MAPPINGS.clear();
		final String TEST_JOURNEY_NAME = 'TestJourney';

		MCJourneyController.processMappingMeta(
			new List<MC_Journey_Mapping__mdt>{
				new MC_Journey_Mapping__mdt(
					Business_Entity__c = TEST_BUSINESS_ENTITY,
					DeveloperName = 'Test_Journey',
					Journey_ID__c = 'FakeJourneyId',
					Journey_Name__c = TEST_JOURNEY_NAME,
					MasterLabel = 'Test Journey'
				)
			},
			TEST_BUSINESS_ENTITY
		);

		Account rp = TestDataFactory.createRentalProviderAccount();
		insert rp;

		Contact con = TestDataFactory.createContact(rp);
		insert con;

		Transaction__c tx = TestDataFactory.createTransaction(rp);
		insert tx;

		Transaction_Party__c party = TestDataFactory.createTransactionParty(
			tx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert party;

		Test.startTest();
		MCJourneyController.BulkEntryItem item = new MCJourneyController.BulkEntryItem(
			new List<Id>{ tx.Id, party.Id },
			party.Id
		);
		MCJourneyController.fireJourneyEntryBulk(
			TEST_JOURNEY_NAME,
			new Map<Id, MCJourneyController.BulkEntryItem>{ con.Id => item },
			new Map<String, Object>{ 'foo' => 'bar' }
		);
		Test.stopTest();

		Transaction_Party__c partyAfter = [
			SELECT Id, Latest_Journey_MC__c
			FROM Transaction_Party__c
			WHERE Id = :party.Id
		];
		Assert.isNull(
			partyAfter.Latest_Journey_MC__c,
			'Expected no journey to be set'
		);
	}

	@IsTest
	static void testmissingDataBehavior() {
		setApiMockSuccess();

		Account rp = TestDataFactory.createRentalProviderAccount();
		insert rp;

		Contact con = TestDataFactory.createContact(rp);
		insert con;

		Transaction__c tx = TestDataFactory.createTransaction(rp);
		insert tx;

		Transaction_Party__c party = TestDataFactory.createTransactionParty(
			tx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		insert party;

		Test.startTest();
		final String TEST_JOURNEY_NAME = 'Some random name that will never be used, right?';
		try {
			MCJourneyController.fireJourneyEntry(
				TEST_JOURNEY_NAME,
				con.Id,
				new List<Id>{ tx.Id, party.Id },
				party.Id
			);
			Assert.fail('Expected exception');
		} catch (IllegalArgumentException e) {
			Assert.isTrue(
				e.getMessage()
					.startsWith('Journey name not configured anywhere'),
				'expected bad journey message'
			);
		}

		//mark it as used somewhere
		MCJourneyController.ALL_KNOWN_JOURNEY_NAMES.add(TEST_JOURNEY_NAME);
		MCJourneyController.fireJourneyEntry(
			TEST_JOURNEY_NAME,
			con.Id,
			new List<Id>{ tx.Id, party.Id },
			party.Id
		);
		Test.stopTest();

		Transaction_Party__c partyAfter = [
			SELECT Id, Latest_Journey_MC__c
			FROM Transaction_Party__c
			WHERE Id = :party.Id
		];
		Assert.isNull(
			partyAfter.Latest_Journey_MC__c,
			'Expected no journey to be set'
		);
	}

	@IsTest
	private static void test_stub() {
		ApiMockSuccess apiMockSuccess = setApiMockSuccess();

		Assert.isTrue(MCJourneyController.mockSet, 'expected mock to be set');

		upsert new Integration_Stubs__c(MarketingCloud__c = true);

		Assert.isTrue(
			IntegrationStubProvider.isStubEnabled(
				MCJourneyController.INTEGRATION_NAME
			),
			'expected stub to be enabled'
		);

		Test.startTest();

		MCJourneyController.callEventsApi('dummy payload');
		MCJourneyController.callEventsApiBulk(new MCModel.BulkEntry('fake'));

		Test.stopTest();

		Assert.areEqual(
			0,
			apiMockSuccess.calloutCount,
			'expected no callout with mocks enabled'
		);
	}

	@IsTest
	static void testValidateArgument() {
		MCJourneyController.validateArgument(true, 'This should never be seen');
		try {
			MCJourneyController.validateArgument(false, 'fail me');
			Assert.fail('Should have thrown');
		} catch (Exception e) {
			Assert.isInstanceOfType(
				e,
				IllegalArgumentException.class,
				'Expected IAE'
			);
			Assert.areEqual(
				'fail me',
				e.getMessage(),
				'expected input message'
			);
		}
	}

	@IsTest
	static void testValidateContactId() {
		try {
			MCJourneyController.validateContactId(UserInfo.getUserId());
			Assert.fail('Should have thrown');
		} catch (Exception e) {
			Assert.isInstanceOfType(
				e,
				IllegalArgumentException.class,
				'Expected IAE'
			);
			Assert.areEqual(
				'contactId should be an Id for a Contact record. Got: ' +
				UserInfo.getUserId(),
				e.getMessage(),
				'expected explanation'
			);
		}
	}

	@IsTest
	static void testThrowIfCtxNull() {
		try {
			MCJourneyController.throwIfCtxNull(null, 0);
			Assert.fail('Should have thrown');
		} catch (Exception e) {
			Assert.isInstanceOfType(
				e,
				IllegalArgumentException.class,
				'Expected IAE'
			);
			Assert.areEqual(
				'Context object element 0 is null',
				e.getMessage(),
				'expected explanation'
			);
		}
	}

	@IsTest
	static void testThrowIfCtxNull2() {
		try {
			MCJourneyController.throwIfCtxNull(null, UserInfo.getUserId(), 0);
			Assert.fail('Should have thrown');
		} catch (Exception e) {
			Assert.isInstanceOfType(
				e,
				IllegalArgumentException.class,
				'Expected IAE'
			);
			Assert.areEqual(
				'Context object element 0 for contact ' +
					UserInfo.getUserId() +
					' is null',
				e.getMessage(),
				'expected explanation'
			);
		}
	}

	@TestVisible
	class ApiMockSuccess implements HttpCalloutMock {
		public MCModel.JourneyEntryEvent requestBody;
		public Integer calloutCount { get; private set; }
		private ApiMockSuccess() {
			calloutCount = 0;
		}

		public HttpResponse respond(HttpRequest request) {
			calloutCount++;
			Assert.areEqual('POST', request.getMethod(), 'expected POST');
			Assert.isFalse(
				String.isBlank(request.getBody()),
				'expected a body'
			);
			//Deserialise untyped because Typed will fail on the Data map.
			Map<String, Object> payload = (Map<String, Object>) JSON.deserializeUntyped(
				request.getBody()
			);
			MCModel.JourneyEntryEvent entry = new MCModel.JourneyEntryEvent(
				(String) payload.get('ContactKey'),
				(String) payload.get('EventDefinitionKey')
			);
			entry.Data = (Map<String, Object>) payload.get('Data');
			this.requestBody = entry;

			HttpResponse res = new HttpResponse();
			res.setStatusCode(201);

			return res;
		}
	}

	public class ApiMockSuccessV2 implements HttpCalloutMock {
		public List<MCModel.JourneyEntryEvent> requestBodies = new List<MCModel.JourneyEntryEvent>();

		public HttpResponse respond(HttpRequest request) {
			if (
				request.getEndpoint().endsWith('/interaction/v1/async/events')
			) {
				ApiMockSuccessBulk bulkMock = new ApiMockSuccessBulk();
				return bulkMock.respond(request);
			}
			Assert.areEqual('POST', request.getMethod(), 'expected POST');
			Assert.isFalse(
				String.isBlank(request.getBody()),
				'expected a body'
			);
			//Deserialise untyped because Typed will fail on the Data map.
			Map<String, Object> payload = (Map<String, Object>) JSON.deserializeUntyped(
				request.getBody()
			);
			MCModel.JourneyEntryEvent entry = new MCModel.JourneyEntryEvent(
				(String) payload.get('ContactKey'),
				(String) payload.get('EventDefinitionKey')
			);
			entry.Data = (Map<String, Object>) payload.get('Data');
			this.requestBodies.add(entry);

			HttpResponse res = new HttpResponse();
			res.setStatusCode(201);

			return res;
		}

		public MCModel.JourneyEntryEvent findEntry(
			String journeyName,
			Id contactId
		) {
			if (String.isBlank(journeyName) || contactId == null) {
				throw new IllegalArgumentException(
					journeyName + ', ' + contactId
				);
			}
			MC_Journey_Mapping__mdt mapping = MCJourneyController.JOURNEY_MAPPINGS.get(
				journeyName
			);
			Assert.isNotNull(mapping, 'Did not find journey: ' + journeyName);
			for (MCModel.JourneyEntryEvent requestBody : this.requestBodies) {
				if (
					requestBody.ContactKey == contactId &&
					requestBody.EventDefinitionKey == mapping.Journey_ID__c
				) {
					return requestBody;
				}
			}
			return null;
		}
	}

	private static MCModel.BulkEntry decodeBulkInput(String body) {
		//Deserialise untyped because Typed will fail on the Data map.
		Map<String, Object> payload = (Map<String, Object>) JSON.deserializeUntyped(
			body
		);
		MCModel.BulkEntry entry = new MCModel.BulkEntry(
			(String) payload.get('eventDefinitionKey')
		);
		List<Object> members = (List<Object>) payload.get('members');
		for (Object memberPayloadRaw : members) {
			Map<String, Object> memberPayload = (Map<String, Object>) memberPayloadRaw;
			MCModel.BulkEntryMember member = new MCModel.BulkEntryMember(
				(String) memberPayload.get('contactKey')
			);
			member.data.putAll((Map<String, Object>) memberPayload.get('data'));
			entry.add(member);
		}
		return entry;
	}

	@TestVisible
	class ApiMockSuccessBulk implements HttpCalloutMock {
		public MCModel.BulkEntry requestBody;
		public Integer calloutCount { get; private set; }
		private ApiMockSuccessBulk() {
			calloutCount = 0;
		}

		public HttpResponse respond(HttpRequest request) {
			calloutCount++;
			Assert.areEqual('POST', request.getMethod(), 'expected POST');
			Assert.isFalse(
				String.isBlank(request.getBody()),
				'expected a body'
			);

			this.requestBody = decodeBulkInput(request.getBody());

			HttpResponse res = new HttpResponse();
			res.setStatusCode(200);

			res.setBody(
				JSON.serialize(
					MCJourneyController.generateFakeResponse(this.requestBody)
				)
			);

			return res;
		}
	}

	class ApiMockFailure implements HttpCalloutMock {
		public Integer calloutCount { get; private set; }
		private ApiMockFailure() {
			calloutCount = 0;
		}
		public HttpResponse respond(HttpRequest request) {
			calloutCount++;
			HttpResponse res = new HttpResponse();
			res.setStatusCode(400);
			return res;
		}
	}

	class ApiMockFailureBulk implements HttpCalloutMock {
		public Integer calloutCount { get; private set; }
		private ApiMockFailureBulk() {
			calloutCount = 0;
		}
		public HttpResponse respond(HttpRequest request) {
			Assert.areEqual('POST', request.getMethod(), 'expected POST');
			Assert.isFalse(
				String.isBlank(request.getBody()),
				'expected a body'
			);

			calloutCount++;
			HttpResponse res = new HttpResponse();
			res.setStatusCode(400);

			MCModel.BulkEntry bulkEntry = decodeBulkInput(request.getBody());
			MCModel.BulkInsertResult bulkInsertResult = MCJourneyController.generateFakeResponse(
				bulkEntry
			);
			bulkInsertResult.notQueuedCount = bulkInsertResult.count;
			for (
				MCModel.BulkInsertResultMember member : bulkInsertResult.members
			) {
				member.errorcode = '1234';
				member.message = 'testing failure';
			}

			res.setBody(JSON.serialize(bulkInsertResult));
			res.setHeader('Content-Type', 'application/json');

			return res;
		}
	}
}