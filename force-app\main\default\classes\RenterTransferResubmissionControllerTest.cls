@IsTest
private class RenterTransferResubmissionControllerTest {
	public static final String RP_USERNAME =
		'<EMAIL>' +
		EnvironmentUtility.getUsernameSuffix();

	@TestSetup
	static void setup() {
		Account rentalProvider = TestDataFactory.createRentalProviderAccount();
		insert rentalProvider;
		account rpa = TestDataFactory.createRenterPersonAccount(
			'test',
			'Name',
			'<EMAIL>'
		);
		insert rpa;
		rpa = [SELECT Id, PersonContactId FROM Account WHERE Id = :rpa.Id];
		account rpac = TestDataFactory.createRenterPersonAccount(
			'test',
			'Company',
			'<EMAIL>'
		);
		insert rpac;
		rpac = [SELECT Id, PersonContactId FROM Account WHERE Id = :rpac.Id];

		Contact rpc = TestDataFactory.createContact(rentalProvider);
		insert rpc;

		User rpu = TestDataFactory.createRentalProviderUser(
			rpc,
			[
				SELECT Id
				FROM Profile
				WHERE
					Name = :Constants.PROFILE_RTBA_CUSTOMER_COMMUNITY_LOGIN_USER
			]
		);
		rpu.Username = RP_USERNAME;
		insert rpu;

		Transaction__c tx = TestDataFactory.createTransaction(rentalProvider);
		insert tx;

		Bond__c bond = TestDataFactory.createBond(tx);
		bond.Funding_Source__c = Constants.BOND_FUNDING_SOURCE_PRIVATE;
		bond.Bond_Paid_By__c = Constants.TRANSACTION_BOND_PAID_BY_RENTER;
		insert bond;

		Bond_Party__c bondParty = TestDataFactory.createBondParty(
			bond,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		bondParty.First_Name__c = 'Nathan';
		bondParty.Family_Name__c = 'Smith';
		bondParty.Date_of_Birth__c = Date.newInstance(1990, 12, 29);
		bondParty.Role__c = Constants.BOND_PARTY_ROLE_RENTER;
		//bondParty.Renter__c = rpc.Id;
		insert bondParty;

		Transaction__c transferTx = TestDataFactory.createTransaction(
			rentalProvider
		);
		transferTx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS;
		transferTx.RecordTypeId = Constants.RECORD_TYPE_ID_TRANSACTION_TRANSFER;
		transferTx.Type__c = Constants.TRANSACTION_TYPE_RENTER_TRANSFER;
		transferTx.Bond__c = bond.Id;
		transferTx.Revision_Number__c = 1;

		insert transferTx;

		Transaction_Party__c testIndividual = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		Transaction_Party__c testCompany = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		Transaction_Party__c rpTp = TestDataFactory.createTransactionParty(
			transferTx,
			Constants.BOND_PARTY_RENTER_TYPE_COMPANY
		);
		testIndividual.Renter__c = rpa.personContactId;
		testIndividual.Rental_Provider__c = rpa.Id;
		testCompany.renter__c = rpac.personContactId;
		testCompany.Rental_Provider__c = rpac.Id;
		rpTp.Role__c = Constants.BOND_PARTY_ROLE_RENTAL_PROVIDER;
		rptp.Rental_Provider__c = rentalProvider.Id;
		insert new List<Transaction_Party__c>{
			testIndividual,
			testCompany,
			rpTp
		};
	}

	private static SObject findParty(
		List<SObject> partyList,
		String role,
		String renterType
	) {
		for (SObject party : partyList) {
			if (
				party.get('Role__c') == role &&
				party.get('Renter_Type__c') == renterType
			) {
				return party;
			}
		}
		return null;
	}

	@IsTest
	private static void getValidatedTransactionForUpdate_ShouldReturnTransaction() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Revision_Number__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		// When

		Test.startTest();

		Transaction__c result;

		System.runAs(rpUser[0]) {
			result = RenterTransferResubmissionController.getValidatedTransactionForUpdate(
				tx.Id
			);
		}

		Test.stopTest();

		// Then
		Assert.isNotNull(result, 'Should return a transaction record.');
		Assert.areEqual(
			tx.Id,
			result.Id,
			'Returned transfer transaction details should match the provided transaction.'
		);
	}

	@IsTest
	private static void getValidatedTransactionForResubmit_ShouldReturnTransaction() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		update tx;

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);

			TestDataFactory.activateCustomPerm(
				rpUser[0].Id,
				Constants.CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION,
				Constants.CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION
			);
		}

		// When

		Test.startTest();

		RenterTransferResubmissionController.QueryResponse result;

		System.runAs(rpUser[0]) {
			result = RenterTransferResubmissionController.getValidatedTransactionForResubmit(
				tx.Id,
				tx.Bond__c
			);
		}

		Test.stopTest();

		// Then
		Assert.isNotNull(result, 'Should return a transaction record.');
		Assert.areEqual(
			tx.Id,
			result.txDetails.Id,
			'Returned transfer transaction details should match the provided transaction.'
		);
	}

	@IsTest
	private static void testHandleRenterTransferResubmit_Success() {
		MCJourneyControllerTest.ApiMockSuccess mcJourneyMock = MCJourneyControllerTest.setApiMockSuccess();
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		update tx;

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);

			TestDataFactory.activateCustomPerm(
				rpUser[0].Id,
				Constants.CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION,
				Constants.CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION
			);
		}

		// When

		Test.startTest();

		RenterTransferResubmissionController.QueryResponse result = null;
		RenterTransferResubmissionController.TransferSubmissionResponse submissionResponse = null;
		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();

		System.runAs(rpUser[0]) {
			result = RenterTransferResubmissionController.getValidatedTransactionForResubmit(
				tx.Id,
				tx.Bond__c
			);
		}

		request.txDetails = result.txDetails;
		request.txDetails.Comments__c = 'Resubmit a renter transfer';
		request.txDetails.Date_Of_Transfer__c = Date.newInstance(2024, 7, 2);
		request.tpDetails = result.txDetails.Transaction_Parties__r;
		request.modifiedDateTimeMap = result.modifiedDateTimeMap;

		System.runAs(rpUser[0]) {
			submissionResponse = RenterTransferResubmissionController.handleRenterTransferResubmit(
				request
			);
		}
		Test.stopTest();

		// Then
		Assert.isNotNull(
			submissionResponse,
			'Should return a submission response.'
		);
		Assert.areEqual(
			Label.Renter_Transfer_Message_When_Successful_Transfer_Resubmission,
			submissionResponse.success,
			'The correct success message should have been returned.'
		);

		//Returned new transaction
		Transaction__c rpTransferTransaction = [
			SELECT
				Id,
				Origin__c,
				Status__c,
				(
					SELECT Id
					FROM Transaction_Parties__r
					WHERE
						Party_Status__c = :Constants.TRANSACTION_PARTY_STATUS_INPUT_AWAITED
				)
			FROM Transaction__c
			WHERE Name = :submissionResponse.transactionNumber
			LIMIT 1
		];
		Assert.isNotNull(
			rpTransferTransaction,
			'Transaction should have been created.'
		);
		Assert.areEqual(
			Constants.TRANSACTION_ORIGIN_WEBSITE,
			rpTransferTransaction.Origin__c,
			'Transaction origin should be set to website.'
		);
		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS,
			rpTransferTransaction.Status__c,
			'Expected a pending with renters tx'
		);
		Assert.areEqual(
			rpTransferTransaction.Transaction_Parties__r.size() * 2,
			mcJourneyMock.calloutCount,
			'Expected callouts for pending input parties (email + sms)'
		);
	}

	@IsTest
	private static void testHandleRenterTransferResubmit_BondChanged() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		update tx;

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);

			TestDataFactory.activateCustomPerm(
				rpUser[0].Id,
				Constants.CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION,
				Constants.CUSTOM_PERMISSION_RESUBMIT_RENTER_TRANSFER_TRANSACTION
			);
		}

		// When

		Test.startTest();

		RenterTransferResubmissionController.QueryResponse result = null;
		RenterTransferResubmissionController.TransferSubmissionResponse submissionResponse = null;
		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();

		Boolean exceptionThrown = false;
		String exceptionMessage;

		System.runAs(rpUser[0]) {
			result = RenterTransferResubmissionController.getValidatedTransactionForResubmit(
				tx.Id,
				tx.Bond__c
			);
		}

		Bond__c revisedBond = new Bond__c(
			Id = tx.Bond__c,
			Street_Address__c = '2 Flinders Street'
		);

		update revisedBond;

		request.txDetails = result.txDetails;
		request.txDetails.Comments__c = 'Resubmit a renter transfer';
		request.txDetails.Date_Of_Transfer__c = Date.newInstance(2024, 7, 2);
		request.tpDetails = result.txDetails.Transaction_Parties__r;
		request.modifiedDateTimeMap = result.modifiedDateTimeMap;

		try {
			System.runAs(rpUser[0]) {
				submissionResponse = RenterTransferResubmissionController.handleRenterTransferResubmit(
					request
				);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}
		Test.stopTest();

		// Then
		Assert.isNull(
			submissionResponse,
			'Should not return a submission response.'
		);
		Assert.areEqual(
			Label.Renter_Transfer_Message_When_Bond_Details_Changed_In_Resubmission,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	private static void testhandleRpReviewTransferTxResubmit() {
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];
		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		update tx;

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Test.startTest();

		TransferRpReviewController.TransactionResponseWrapper result = null;
		RenterTransferResubmissionController.TransferSubmissionResponse submissionResponse = null;
		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();

		System.runAs(rpUser[0]) {
			result = TransferRpReviewController.getValidatedTransaction(tx.Id);
		}

		request.txDetails = result.txDetails;
		request.txDetails.Comments__c = 'Rp review resubmit renter transfer';
		request.txDetails.Date_Of_Transfer__c = Date.newInstance(2024, 7, 2);
		request.tpDetails = result.txDetails.Transaction_Parties__r;
		request.modifiedDateTimeMap = result.modifiedDateTimeMap;

		System.runAs(rpUser[0]) {
			submissionResponse = RenterTransferResubmissionController.handleRpReviewTransferTxResubmit(
				request
			);
		}

		Test.stopTest();

		Assert.isNotNull(submissionResponse, 'A response should be returned.');
		Assert.areEqual(
			tx.Name,
			submissionResponse.transactionNumber,
			'The correct transaction name should be returned.'
		);

		Assert.areEqual(
			Label.Renter_Transfer_Message_When_Successful_Transfer_Resubmission,
			submissionResponse.success,
			'The correct success message should be returned.'
		);

		Transaction__c transferTx = TransferRpReviewController.getTransaction(
			tx.Id
		);

		Assert.areEqual(
			'Rp review resubmit renter transfer',
			transferTx.Comments__c,
			'The comments field should be updated.'
		);

		Assert.areEqual(
			Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS,
			transferTx.Status__c,
			'The status field should be updated.'
		);
	}

	@IsTest
	private static void testhandleRpReviewTransferTxResubmitErrorPendingWithRenters() {
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		update tx;
		String exceptionMessage;
		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Test.startTest();

		TransferRpReviewController.TransactionResponseWrapper result = null;
		RenterTransferResubmissionController.TransferSubmissionResponse submissionResponse = null;
		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();

		System.runAs(rpUser[0]) {
			result = TransferRpReviewController.getValidatedTransaction(tx.Id);
		}

		request.txDetails = result.txDetails;
		request.txDetails.Comments__c = 'Rp review resubmit renter transfer';
		request.txDetails.Date_Of_Transfer__c = Date.newInstance(2024, 7, 2);
		request.tpDetails = result.txDetails.Transaction_Parties__r;
		request.modifiedDateTimeMap = result.modifiedDateTimeMap;

		try {
			System.runAs(rpUser[0]) {
				tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS;
				update tx;
			}

			submissionResponse = RenterTransferResubmissionController.handleRpReviewTransferTxResubmit(
				request
			);
			Assert.fail('Should throw exception');
		} catch (AuraHandledException e) {
			exceptionMessage = e.getMessage();
		}
		Test.stopTest();
		Assert.areEqual(
			Label.RP_Review_Message_When_Resubmit_If_Pending_With_Renters,
			exceptionMessage,
			'Expected error message for pending with renters status.'
		);
	}

	@IsTest
	private static void testhandleRpReviewTransferTxResubmitErrorTimedOut() {
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		update tx;
		String exceptionMessage;
		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Test.startTest();

		TransferRpReviewController.TransactionResponseWrapper result = null;
		RenterTransferResubmissionController.TransferSubmissionResponse submissionResponse = null;
		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();

		System.runAs(rpUser[0]) {
			result = TransferRpReviewController.getValidatedTransaction(tx.Id);
		}

		request.txDetails = result.txDetails;
		request.txDetails.Comments__c = 'Rp review resubmit renter transfer';
		request.txDetails.Date_Of_Transfer__c = Date.newInstance(2024, 7, 2);
		request.tpDetails = result.txDetails.Transaction_Parties__r;
		request.modifiedDateTimeMap = result.modifiedDateTimeMap;

		try {
			System.runAs(rpUser[0]) {
				tx.Status__c = Constants.TRANSACTION_STATUS_TIMEDOUT;
				update tx;
			}

			submissionResponse = RenterTransferResubmissionController.handleRpReviewTransferTxResubmit(
				request
			);
			Assert.fail('Should throw exception');
		} catch (AuraHandledException e) {
			exceptionMessage = e.getMessage();
		}
		Test.stopTest();

		Assert.areEqual(
			Label.RP_Review_Claim_Message_On_Resubmission_If_Timed_Out,
			exceptionMessage,
			'Expected error message for timed out status.'
		);
	}

	@IsTest
	private static void testhandleRpReviewTransferTxResubmitErrorEmailBounced() {
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		update tx;
		String exceptionMessage;
		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Test.startTest();

		TransferRpReviewController.TransactionResponseWrapper result = null;
		RenterTransferResubmissionController.TransferSubmissionResponse submissionResponse = null;
		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();

		System.runAs(rpUser[0]) {
			result = TransferRpReviewController.getValidatedTransaction(tx.Id);
		}

		request.txDetails = result.txDetails;
		request.txDetails.Comments__c = 'Rp review resubmit renter transfer';
		request.txDetails.Date_Of_Transfer__c = Date.newInstance(2024, 7, 2);
		request.tpDetails = result.txDetails.Transaction_Parties__r;
		request.modifiedDateTimeMap = result.modifiedDateTimeMap;

		try {
			System.runAs(rpUser[0]) {
				tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
				tx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_EMAIL_BOUNCED;
				update tx;
			}

			submissionResponse = RenterTransferResubmissionController.handleRpReviewTransferTxResubmit(
				request
			);
			Assert.fail('Should throw exception');
		} catch (AuraHandledException e) {
			exceptionMessage = e.getMessage();
		}
		Test.stopTest();

		Assert.areEqual(
			Label.RP_Review_Claim_Message_On_Resubmission_If_Email_Bounced,
			exceptionMessage,
			'Expected error message for email bounced status.'
		);
	}

	@IsTest
	private static void testhandleRpReviewTransferTxResubmitErrorCancelledByOtherRP() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		Transaction__c tx = [
			SELECT Id, Bond__c, Name
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_PENDING_WITH_RP;
		update tx;
		String exceptionMessage;
		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Test.startTest();

		TransferRpReviewController.TransactionResponseWrapper result = null;
		RenterTransferResubmissionController.TransferSubmissionResponse submissionResponse = null;
		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();

		System.runAs(rpUser[0]) {
			result = TransferRpReviewController.getValidatedTransaction(tx.Id);
		}

		request.txDetails = result.txDetails;
		request.txDetails.Comments__c = 'Rp review resubmit renter transfer';
		request.txDetails.Date_Of_Transfer__c = Date.newInstance(2024, 7, 2);
		request.tpDetails = result.txDetails.Transaction_Parties__r;
		request.modifiedDateTimeMap = result.modifiedDateTimeMap;

		try {
			System.runAs(rpUser[0]) {
				tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
				tx.Sub_Status__c = Constants.TRANSACTION_SUB_STATUS_RENTAL_PROVIDER_CANCELLED;
				update tx;
			}

			submissionResponse = RenterTransferResubmissionController.handleRpReviewTransferTxResubmit(
				request
			);
			Assert.fail('Should throw exception');
		} catch (AuraHandledException e) {
			exceptionMessage = e.getMessage();
		}
		Test.stopTest();

		Assert.areEqual(
			Label.RP_Review_Message_When_Resubmit_If_Transaction_Cancelled,
			exceptionMessage,
			'Expected error message for cancelled status.'
		);
	}

	@IsTest
	private static void validateUpdateTransaction_ShouldReturnError_WhenBondIsSuspended() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = [
			SELECT Id, Bond__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		Bond__c bond = [
			SELECT Id, Status__c
			FROM Bond__c
			WHERE Id = :tx.Bond__c
		];

		bond.Status__c = Constants.BOND_STATUS_SUSPENDED;
		update bond;

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;

		// When

		Test.startTest();

		Boolean exceptionThrown = false;
		String exceptionMessage;

		try {
			System.runAs(rpUser[0]) {
				RenterTransferResubmissionController.validateUpdateTransaction(
					request
				);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			Label.Renter_Transfer_Message_When_Bond_is_Suspended,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	private static void validateUpdateTransaction_ShouldReturnError_WhenTransactionIsRpCancelled() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = [
			SELECT Id, Bond__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		update tx;

		Transaction_Party__c rp = [
			SELECT Id, Party_Status__c
			FROM Transaction_Party__c
			WHERE
				Role__c = :Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
				AND Transaction__c = :tx.Id
		];
		rp.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_RP_CANCELLED;
		update rp;

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;

		// When
		Test.startTest();

		Boolean exceptionThrown = false;
		String exceptionMessage;

		try {
			System.runAs(rpUser[0]) {
				RenterTransferResubmissionController.validateUpdateTransaction(
					request
				);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			Label.RP_Review_Message_When_Resubmit_If_Transaction_Cancelled,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	private static void validateUpdateTransaction_ShouldReturnError_WhenStatusIsEmailBounced() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = [
			SELECT Id, Bond__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_CANCELLED;
		update tx;

		Transaction_Party__c rp = [
			SELECT Id, Party_Status__c
			FROM Transaction_Party__c
			WHERE
				Role__c = :Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER
				AND Transaction__c = :tx.Id
		];
		rp.Party_Status__c = Constants.TRANSACTION_PARTY_STATUS_EMAIL_BOUNCED;
		update rp;

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;

		// When
		Test.startTest();

		Boolean exceptionThrown = false;
		String exceptionMessage;

		try {
			System.runAs(rpUser[0]) {
				RenterTransferResubmissionController.validateUpdateTransaction(
					request
				);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			Label.RP_Review_Claim_Message_On_Resubmission_If_Email_Bounced,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	private static void validateUpdateTransaction_ShouldReturnError_WhenStatusIsFinalised() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = [
			SELECT Id, Bond__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_FINALISED;
		update tx;

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;

		// When
		Test.startTest();

		Boolean exceptionThrown = false;
		String exceptionMessage;

		try {
			System.runAs(rpUser[0]) {
				RenterTransferResubmissionController.validateUpdateTransaction(
					request
				);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			Label.Transaction_Review_Message_When_RP_Resubmit_If_Finalised_And_Private_Fund,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	private static void validateUpdateTransaction_ShouldReturnError_WhenRevisionIsUpdated() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = [
			SELECT Id, Bond__c, Revision_Number__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Revision_Number__c = 2;

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;

		// When
		Test.startTest();

		Boolean exceptionThrown = false;
		String exceptionMessage;

		try {
			System.runAs(rpUser[0]) {
				RenterTransferResubmissionController.validateUpdateTransaction(
					request
				);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			Label.Transaction_Resubmit_Message_When_Is_Updated,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	private static void validateUpdateTransaction_ShouldReturnError_WhenStatusIsTimeOut() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = [
			SELECT Id, Bond__c, Revision_Number__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		tx.Status__c = Constants.TRANSACTION_STATUS_TIMEDOUT;
		update tx;

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;

		// When
		Test.startTest();

		Boolean exceptionThrown = false;
		String exceptionMessage;

		try {
			System.runAs(rpUser[0]) {
				RenterTransferResubmissionController.validateUpdateTransaction(
					request
				);
			}
		} catch (AuraHandledException e) {
			exceptionThrown = true;
			exceptionMessage = e.getMessage();
		}

		Test.stopTest();

		// Then
		Assert.areEqual(
			Label.RP_Review_Claim_Message_On_Resubmission_If_Timed_Out,
			exceptionMessage,
			'Expected an exception to be thrown: ' + exceptionMessage
		);
	}

	@IsTest
	private static void validateUpdateTransaction_ShouldReturnSuccess_WhenNoValidationError() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = [
			SELECT
				Id,
				Name,
				Bond__c,
				Revision_Number__c,
				Comments__c,
				Date_Of_Transfer__c
			FROM Transaction__c
			WHERE
				Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
				AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
			LIMIT 1
		];

		List<Transaction_Party__c> tp = [
			SELECT
				Id,
				Name,
				First_Name__c,
				Last_Name__c,
				Company_Name__c,
				Email_Address__c,
				Date_of_Birth__c,
				Mobile_Number__c,
				Party_Status__c,
				Role__c,
				Renter_Type__c,
				Party_Name__c,
				Renter__c,
				Renter_position__c
			FROM Transaction_Party__c
			WHERE Transaction__c = :tx.Id
		];

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;
		request.tpDetails = tp;

		// When
		Test.startTest();

		RenterTransferResubmissionController.TransferSubmissionResponse response;

		System.runAs(rpUser[0]) {
			response = RenterTransferResubmissionController.validateUpdateTransaction(
				request
			);
		}

		Test.stopTest();

		// Then
		Assert.isNotNull(request, 'Should return a transaction record.');
		Assert.areEqual(
			tx.Name,
			response.transactionNumber,
			'Should return a response with the same Name as the input.'
		);
	}

	@IsTest
	private static void testCreateNewTransactionReview() {
		// Given
		List<User> rpUser = [
			SELECT Id
			FROM User
			WHERE Username = :RP_USERNAME
			LIMIT 1
		];

		System.runAs(new User(Id = UserInfo.getUserId())) {
			TestDataFactory.assignPermissionSetGroup(
				rpUser[0].Id,
				Constants.PERMISSION_SET_GROUP_RENTAL_PROVIDER_L4
			);
		}

		Transaction__c tx = RenterTransferResubmissionController.getValidatedTransactionForUpdate(
			[
				SELECT Id
				FROM Transaction__c
				WHERE
					Status__c = :Constants.TRANSACTION_STATUS_PENDING_WITH_RENTERS
					AND Type__c = :Constants.TRANSACTION_TYPE_RENTER_TRANSFER
				LIMIT 1
			]
			.Id
		);

		Transaction_Party__c rpParty = (Transaction_Party__c) findParty(
			tx.Transaction_Parties__r,
			Constants.TRANSACTION_PARTY_ROLE_RENTAL_PROVIDER,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_COMPANY
		);
		Transaction_Party__c individualRenterParty = (Transaction_Party__c) findParty(
			tx.Transaction_Parties__r,
			Constants.TRANSACTION_PARTY_ROLE_RENTER,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_INDIVIDUAL
		);
		String previousFirstName = individualRenterParty.First_Name__c;
		individualRenterParty.First_Name__c = 'John';
		Transaction_Party__c companyRenterParty = (Transaction_Party__c) findParty(
			tx.Transaction_Parties__r,
			Constants.TRANSACTION_PARTY_ROLE_RENTER,
			Constants.TRANSACTION_PARTY_RENTER_TYPE_COMPANY
		);
		List<Transaction_Party__c> tps = new List<Transaction_Party__c>{
			rpParty,
			individualRenterParty,
			companyRenterParty
		};

		RenterTransferResubmissionController.ResubmissionRequest request = new RenterTransferResubmissionController.ResubmissionRequest();
		request.txDetails = tx;
		request.tpDetails = tps;

		// When
		Test.startTest();

		RenterTransferResubmissionController.TransferSubmissionResponse response;

		System.runAs(rpUser[0]) {
			response = RenterTransferResubmissionController.updateTransaction(
				request
			);
		}

		Test.stopTest();

		// Then
		Assert.isNotNull(
			response.transactionNumber,
			'Should return a transaction record.'
		);

		Transaction_Review__c review = [
			SELECT Id, First_Name__c, Revised_First_Name__c
			FROM Transaction_Review__c
			WHERE Transaction__c = :tx.Id AND Active_Review__c = TRUE
			LIMIT 1
		];

		Assert.isNotNull(review, 'Should return a transaction review record.');
		Assert.areEqual(
			previousFirstName,
			review.First_Name__c,
			'Transaction review should have the previous first name.'
		);
		Assert.areEqual(
			'John',
			review.Revised_First_Name__c,
			'Transaction review should have the updated first name.'
		);
	}
}