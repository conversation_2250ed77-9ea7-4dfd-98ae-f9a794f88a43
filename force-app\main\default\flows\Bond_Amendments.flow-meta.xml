<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <choices>
        <name>AdhocComment</name>
        <choiceText>Adhoc Comments</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Adhoc Comments</stringValue>
        </value>
    </choices>
    <choices>
        <name>BondAddress</name>
        <choiceText>Rented Property Address</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Rented Property Address</stringValue>
        </value>
    </choices>
    <choices>
        <name>bondAmount</name>
        <choiceText>Current Bond Amount</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Current Bond Amount</stringValue>
        </value>
    </choices>
    <choices>
        <name>bondLodgementAmount</name>
        <choiceText>Bond Lodgement Amount</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Bond Lodgement Amount</stringValue>
        </value>
    </choices>
    <choices>
        <name>fundingSource</name>
        <choiceText>Funding Source</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Funding Source</stringValue>
        </value>
    </choices>
    <choices>
        <name>otherBondDetails</name>
        <choiceText>Other Bond Details</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Other Bond Details</stringValue>
        </value>
    </choices>
    <decisions>
        <description>Link to different sub flow based on different selection</description>
        <name>Check_Bond_Amendment_options</name>
        <label>Check Bond Amendment options</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isRecon</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Bond_amendment_options_with_recon</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>bondAmount</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Bond_amendment_options_with_recon</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>bondLodgementAmount</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Bond_amendment_options_with_recon</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>fundingSource</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Internal_Bond_Amendment_Subflow</targetReference>
            </connector>
            <label>isRecon</label>
        </rules>
        <rules>
            <name>isBondAddress</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Bond_amendments_options</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>BondAddress</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Bond_amendment_options_with_recon</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>BondAddress</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Amend_property_address</targetReference>
            </connector>
            <label>isBondAddress</label>
        </rules>
        <rules>
            <name>isOtherBondDetails</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Bond_amendments_options</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>otherBondDetails</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Bond_amendment_options_with_recon</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>otherBondDetails</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Other_Bond_Details</targetReference>
            </connector>
            <label>isOtherBondDetails</label>
        </rules>
        <rules>
            <name>isAdhocComment</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>Bond_amendments_options</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>AdhocComment</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Bond_amendment_options_with_recon</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>AdhocComment</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Adhoc_Comment</targetReference>
            </connector>
            <label>isAdhocComment</label>
        </rules>
    </decisions>
    <description>Bond Amendments</description>
    <dynamicChoiceSets>
        <name>bondStatusPicklistValues</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Status__c</picklistField>
        <picklistObject>Bond__c</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>bondTypePicklistValues</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Type__c</picklistField>
        <picklistObject>Bond__c</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>Bond Amendments {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Bond Amendments</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <screens>
        <description>Bond amendments options screen, different option will go to different sub flow</description>
        <name>Bond_amendments_screen</name>
        <label>What would you like to update in Bond</label>
        <locationX>578</locationX>
        <locationY>134</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Back</backButtonLabel>
        <connector>
            <targetReference>Check_Bond_Amendment_options</targetReference>
        </connector>
        <fields>
            <name>Bond_amendment_options_with_recon</name>
            <choiceReferences>bondAmount</choiceReferences>
            <choiceReferences>bondLodgementAmount</choiceReferences>
            <choiceReferences>fundingSource</choiceReferences>
            <choiceReferences>BondAddress</choiceReferences>
            <choiceReferences>otherBondDetails</choiceReferences>
            <choiceReferences>AdhocComment</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Bond amendment options</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>HasCurrentBondAmountEditPermission</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Bond_amendments_options</name>
            <choiceReferences>BondAddress</choiceReferences>
            <choiceReferences>otherBondDetails</choiceReferences>
            <choiceReferences>AdhocComment</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Bond amendment options</fieldText>
            <fieldType>RadioButtons</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>HasCurrentBondAmountEditPermission</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>false</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>452</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Bond_amendments_screen</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <description>Adding an adhoc comment to a bond</description>
        <name>Adhoc_Comment</name>
        <label>Adhoc Comment</label>
        <locationX>842</locationX>
        <locationY>350</locationY>
        <flowName>Bond_Amendments_Adhoc_Comments</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <name>Amend_property_address</name>
        <label>Amend property address</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <flowName>Bond_Amendments_Amend_property_address</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Internal_Bond_Amendment_Subflow</name>
        <label>Internal Bond Amendment</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <flowName>Bond_Amendments_Internal_Bond_Amendment</flowName>
        <inputAssignments>
            <name>amendmentOption</name>
            <value>
                <elementReference>Bond_amendment_options_with_recon</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <stringValue>{!recordId}</stringValue>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <description>Open Other Bond Details Sub flow</description>
        <name>Other_Bond_Details</name>
        <label>Other Bond Details</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <flowName>Bond_Amendments_Other_Bond_Details</flowName>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>HasCurrentBondAmountEditPermission</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Permission.Amend_Bond_Show_Current_Bond_Amount</elementReference>
        </value>
    </variables>
    <variables>
        <name>HasReconciliationPermission</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <elementReference>$Permission.Reconciliation</elementReference>
        </value>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Bond__c</objectType>
    </variables>
    <variables>
        <name>updatedBondStatusValue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>updatedBondTypeValue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
